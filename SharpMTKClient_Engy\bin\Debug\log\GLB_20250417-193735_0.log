[00000001] [19:37:35:264516] [Tid0x0001043c] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [19:37:35:265515] [Tid0x0001043c] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [19:37:35:265515] [Tid0x0001043c] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [19:37:35:265515] [Tid0x0001043c] [info] create new hsession 0x115cac60 #(kernel.cpp, line:92)
[00000005] [19:37:35:265515] [Tid0x0001043c] [debug] <--[C3] kernel::create_new_session
[00000006] [19:37:35:265515] [Tid0x0001043c] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [19:37:35:265515] [Tid0x0001043c] [debug] <--[C4] boot_rom::boot_rom
[00000008] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C6] device_log_source::device_log_source
[00000011] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C7] data_mux::data_mux
[00000013] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C5] device_instance::device_instance
[00000014] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C2] connection::create_session
[00000015] [19:37:35:271450] [Tid0x0001043c] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [19:37:35:271450] [Tid0x0001043c] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [19:37:35:271450] [Tid0x0001043c] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C11] is_valid_ip
[00000022] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C12] is_lge_impl
[00000024] [19:37:35:271450] [Tid0x0001043c] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [19:37:35:271450] [Tid0x0001043c] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C13] lib_config_parser::get_value
[00000027] [19:37:35:271450] [Tid0x0001043c] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [19:37:35:271450] [Tid0x0001043c] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [19:37:35:271450] [Tid0x0001043c] [info] try to open device: COM13 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [19:37:35:271450] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000031] [19:37:35:271450] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000032] [19:37:35:324990] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000033] [19:37:35:324990] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000034] [19:37:35:387160] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000035] [19:37:35:387160] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000036] [19:37:35:449747] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000037] [19:37:35:449747] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000038] [19:37:35:512320] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000039] [19:37:35:512320] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000040] [19:37:35:574109] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000041] [19:37:35:574109] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000042] [19:37:35:636450] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000043] [19:37:35:637444] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000044] [19:37:35:697381] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000045] [19:37:35:697381] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000046] [19:37:35:758278] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000047] [19:37:35:758278] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000048] [19:37:35:820759] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000049] [19:37:35:820759] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000050] [19:37:35:882120] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000051] [19:37:35:882120] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000052] [19:37:35:942898] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000053] [19:37:35:943900] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000054] [19:37:36:004487] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000055] [19:37:36:004487] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000056] [19:37:36:065958] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000057] [19:37:36:066957] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000058] [19:37:36:127847] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000059] [19:37:36:127847] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000060] [19:37:36:190194] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000061] [19:37:36:190194] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000062] [19:37:36:252521] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000063] [19:37:36:252521] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000064] [19:37:36:315431] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000065] [19:37:36:315431] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000066] [19:37:36:376701] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000067] [19:37:36:376701] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000068] [19:37:36:437913] [Tid0x0001043c] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000069] [19:37:36:437913] [Tid0x0001043c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000070] [19:37:36:498571] [Tid0x0001043c] [info] <--[C14] comm_engine::open
[00000071] [19:37:36:498571] [Tid0x0001043c] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000072] [19:37:36:498571] [Tid0x0001043c] [debug] <--[C9] connection::connect_brom
[00000073] [19:37:36:498571] [Tid0x0001043c] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000074] [19:37:36:498571] [Tid0x0001043c] [info] <--[C8] flashtool_connect_brom
[00000075] [19:37:36:498571] [Tid0x0001043c] [info] -->[C15] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000076] [19:37:36:499570] [Tid0x0001043c] [debug] -->[C17] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000077] [19:37:36:499570] [Tid0x0001043c] [info] -->[C18] device_log_source::stop #(device_log_source.cpp, line:29)
[00000078] [19:37:36:499570] [Tid0x0001043c] [info] <--[C18] device_log_source::stop
[00000079] [19:37:36:499570] [Tid0x0001043c] [info] -->[C19] data_mux::stop #(data_mux.cpp, line:92)
[00000080] [19:37:36:499570] [Tid0x0001043c] [info] <--[C19] data_mux::stop
[00000081] [19:37:36:499570] [Tid0x0001043c] [debug] <--[C17] device_instance::~device_instance
[00000082] [19:37:36:499570] [Tid0x0001043c] [info] -->[C20] comm_engine::close #(comm_engine.cpp, line:382)
[00000083] [19:37:36:499570] [Tid0x0001043c] [debug] -->[C21] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000084] [19:37:36:499570] [Tid0x0001043c] [debug] <--[C21] comm_engine::cancel
[00000085] [19:37:36:499570] [Tid0x0001043c] [info] <--[C20] comm_engine::close
[00000086] [19:37:36:499570] [Tid0x0001043c] [info] delete hsession 0x115cac60 #(kernel.cpp, line:102)
[00000087] [19:37:36:499570] [Tid0x0001043c] [info] <--[C15] flashtool_destroy_session

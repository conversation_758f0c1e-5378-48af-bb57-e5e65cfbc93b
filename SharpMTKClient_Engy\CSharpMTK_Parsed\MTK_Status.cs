﻿namespace SharpMTKClient_Engy.CSharpMTK_Parsed
{
    public class MTK_Status
    {
        public enum STATUS_E : uint
        {
            S_DONE = 0u,
            STATUS_OK = 0u,
            S_COMMON_ERROR_BEGIN = 1000u,
            S_STOP = 1000u,
            S_UNDEFINED_ERROR = 1001u,
            S_INVALID_ARGUMENTS = 1002u,
            S_INVALID_BBCHIP_TYPE = 1003u,
            S_INVALID_EXT_CLOCK = 1004u,
            S_INVALID_BMTSIZE = 1005u,
            S_GET_DLL_VER_FAIL = 1006u,
            S_INVALID_BUF = 1007u,
            S_BUF_IS_NULL = 1008u,
            S_BUF_LEN_IS_ZERO = 1009u,
            S_BUF_SIZE_TOO_SMALL = 1010u,
            S_NOT_ENOUGH_STORAGE_SPACE = 1011u,
            S_NOT_ENOUGH_MEMORY = 1012u,
            S_COM_PORT_OPEN_FAIL = 1013u,
            S_COM_PORT_SET_TIMEOUT_FAIL = 1014u,
            S_COM_PORT_SET_STATE_FAIL = 1015u,
            S_COM_PORT_PURGE_FAIL = 1016u,
            S_FILEPATH_NOT_SPECIFIED_YET = 1017u,
            S_UNKNOWN_TARGET_BBCHIP = 1018u,
            S_SKIP_BBCHIP_HW_VER_CHECK = 1019u,
            S_UNSUPPORTED_VER_OF_BOOT_ROM = 1020u,
            S_UNSUPPORTED_VER_OF_BOOTLOADER = 1021u,
            S_UNSUPPORTED_VER_OF_DA = 1022u,
            S_UNSUPPORTED_VER_OF_SEC_INFO = 1023u,
            S_UNSUPPORTED_VER_OF_ROM_INFO = 1024u,
            S_SEC_INFO_NOT_FOUND = 1025u,
            S_ROM_INFO_NOT_FOUND = 1026u,
            S_CUST_PARA_NOT_SUPPORTED = 1027u,
            S_CUST_PARA_WRITE_LEN_INCONSISTENT = 1028u,
            S_SEC_RO_NOT_SUPPORTED = 1029u,
            S_SEC_RO_WRITE_LEN_INCONSISTENT = 1030u,
            S_ADDR_N_LEN_NOT_32BITS_ALIGNMENT = 1031u,
            S_UART_CHKSUM_ERROR = 1032u,
            S_EMMC_FLASH_BOOT = 1033u,
            S_NOR_FLASH_BOOT = 1034u,
            S_NAND_FLASH_BOOT = 1035u,
            S_UNSUPPORTED_VER_OF_EMI_INFO = 1036u,
            S_PART_NO_VALID_TABLE = 1037u,
            S_PART_NO_SPACE_FOUND = 1038u,
            S_UNSUPPORTED_VER_OF_SEC_CFG = 1039u,
            S_UNSUPPORTED_OPERATION = 1040u,
            S_CHKSUM_ERROR = 1041u,
            S_TIMEOUT = 1042u,
            S_COMMON_ERROR_END = 1043u,
            S_BROM_ERROR_BEGIN = 2000u,
            S_BROM_SET_META_REG_FAIL = 2000u,
            S_BROM_SET_FLASHTOOL_REG_FAIL = 2001u,
            S_BROM_SET_REMAP_REG_FAIL = 2002u,
            S_BROM_SET_EMI_FAIL = 2003u,
            S_BROM_DOWNLOAD_DA_FAIL = 2004u,
            S_BROM_CMD_STARTCMD_FAIL = 2005u,
            S_BROM_CMD_STARTCMD_TIMEOUT = 2006u,
            S_BROM_CMD_JUMP_FAIL = 2007u,
            S_BROM_CMD_WRITE16_MEM_FAIL = 2008u,
            S_BROM_CMD_READ16_MEM_FAIL = 2009u,
            S_BROM_CMD_WRITE16_REG_FAIL = 2010u,
            S_BROM_CMD_READ16_REG_FAIL = 2011u,
            S_BROM_CMD_CHKSUM16_MEM_FAIL = 2012u,
            S_BROM_CMD_WRITE32_MEM_FAIL = 2013u,
            S_BROM_CMD_READ32_MEM_FAIL = 2014u,
            S_BROM_CMD_WRITE32_REG_FAIL = 2015u,
            S_BROM_CMD_READ32_REG_FAIL = 2016u,
            S_BROM_CMD_CHKSUM32_MEM_FAIL = 2017u,
            S_BROM_JUMP_TO_META_MODE_FAIL = 2018u,
            S_BROM_WR16_RD16_MEM_RESULT_DIFF = 2019u,
            S_BROM_CHKSUM16_MEM_RESULT_DIFF = 2020u,
            S_BROM_BBCHIP_HW_VER_INCORRECT = 2021u,
            S_BROM_FAIL_TO_GET_BBCHIP_HW_VER = 2022u,
            S_BROM_AUTOBAUD_FAIL = 2023u,
            S_BROM_SPEEDUP_BAUDRATE_FAIL = 2024u,
            S_BROM_LOCK_POWERKEY_FAIL = 2025u,
            S_BROM_WM_APP_MSG_OUT_OF_RANGE = 2026u,
            S_BROM_NOT_SUPPORT_MT6205B = 2027u,
            S_BROM_EXCEED_MAX_DATA_BLOCKS = 2028u,
            S_BROM_EXTERNAL_SRAM_DETECTION_FAIL = 2029u,
            S_BROM_EXTERNAL_DRAM_DETECTION_FAIL = 2030u,
            S_BROM_GET_FW_VER_FAIL = 2031u,
            S_BROM_CONNECT_TO_BOOTLOADER_FAIL = 2032u,
            S_BROM_CMD_SEND_DA_FAIL = 2033u,
            S_BROM_CMD_SEND_DA_CHKSUM_DIFF = 2034u,
            S_BROM_CMD_JUMP_DA_FAIL = 2035u,
            S_BROM_CMD_JUMP_BL_FAIL = 2036u,
            S_BROM_EFUSE_REG_NO_MATCH_WITH_TARGET = 2037u,
            S_BROM_EFUSE_WRITE_TIMEOUT = 2038u,
            S_BROM_EFUSE_DATA_PROCESS_ERROR = 2039u,
            S_BROM_EFUSE_BLOW_ERROR = 2040u,
            S_BROM_EFUSE_ALREADY_BROKEN = 2041u,
            S_BROM_EFUSE_BLOW_PARTIAL = 2042u,
            S_BROM_SEC_VER_FAIL = 2043u,
            S_BROM_PL_SEC_VER_FAIL = 2044u,
            S_BROM_SET_WATCHDOG_FAIL = 2045u,
            S_BROM_EFUSE_VALUE_IS_NOT_ZERO = 2046u,
            S_BROM_EFUSE_WRITE_TIMEOUT_WITHOUT_EFUSE_VERIFY = 2047u,
            S_BROM_EFUSE_UNKNOW_EXCEPTION_WITHOUT_EFUSE_VERIFY = 2048u,
            S_BROM_ERROR_END = 2049u,
            S_DA_ERROR_BEGIN = 3000u,
            S_DA_INT_RAM_ERROR = 3000u,
            S_DA_EXT_RAM_ERROR = 3001u,
            S_DA_SETUP_DRAM_FAIL = 3002u,
            S_DA_SETUP_PLL_ERR = 3003u,
            S_DA_SETUP_EMI_PLL_ERR = 3004u,
            S_DA_DRAM_ABNORMAL_TYPE_SETTING = 3005u,
            S_DA_DRAMC_RANK0_CALIBRATION_FAILED = 3006u,
            S_DA_DRAMC_RANK1_CALIBRATION_FAILED = 3007u,
            S_DA_DRAM_NOT_SUPPORT = 3008u,
            S_DA_RAM_FLOARTING = 3009u,
            S_DA_RAM_UNACCESSABLE = 3010u,
            S_DA_RAM_ERROR = 3011u,
            S_DA_DEVICE_NOT_FOUND = 3012u,
            S_DA_NOR_UNSUPPORTED_DEV_ID = 3013u,
            S_DA_NAND_UNSUPPORTED_DEV_ID = 3014u,
            S_DA_NOR_FLASH_NOT_FOUND = 3015u,
            S_DA_NAND_FLASH_NOT_FOUND = 3016u,
            S_DA_SOC_CHECK_FAIL = 3017u,
            S_DA_NOR_PROGRAM_FAILED = 3018u,
            S_DA_NOR_ERASE_FAILED = 3019u,
            S_DA_NAND_PAGE_PROGRAM_FAILED = 3020u,
            S_DA_NAND_SPARE_PROGRAM_FAILED = 3021u,
            S_DA_NAND_HW_COPYBACK_FAILED = 3022u,
            S_DA_NAND_ERASE_FAILED = 3023u,
            S_DA_TIMEOUT = 3024u,
            S_DA_IN_PROGRESS = 3025u,
            S_DA_SUPERAND_ONLY_SUPPORT_PAGE_READ = 3026u,
            S_DA_SUPERAND_PAGE_PRGRAM_NOT_SUPPORT = 3027u,
            S_DA_SUPERAND_SPARE_PRGRAM_NOT_SUPPORT = 3028u,
            S_DA_SUPERAND_COPYBACK_NOT_SUPPORT = 3029u,
            S_DA_NOR_CMD_SEQUENCE_ERR = 3030u,
            S_DA_NOR_BLOCK_IS_LOCKED = 3031u,
            S_DA_NAND_BLOCK_IS_LOCKED = 3032u,
            S_DA_NAND_BLOCK_DATA_UNSTABLE = 3033u,
            S_DA_NOR_BLOCK_DATA_UNSTABLE = 3034u,
            S_DA_NOR_VPP_RANGE_ERR = 3035u,
            S_DA_INVALID_BEGIN_ADDR = 3036u,
            S_DA_NOR_INVALID_ERASE_BEGIN_ADDR = 3037u,
            S_DA_NOR_INVALID_READ_BEGIN_ADDR = 3038u,
            S_DA_NOR_INVALID_PROGRAM_BEGIN_ADDR = 3039u,
            S_DA_INVALID_RANGE = 3040u,
            S_DA_NOR_PROGRAM_AT_ODD_ADDR = 3041u,
            S_DA_NOR_PROGRAM_WITH_ODD_LENGTH = 3042u,
            S_DA_NOR_BUFPGM_NO_SUPPORT = 3043u,
            S_DA_NAND_UNKNOWN_ERR = 3044u,
            S_DA_NAND_BAD_BLOCK = 3045u,
            S_DA_NAND_ECC_1BIT_CORRECT = 3046u,
            S_DA_NAND_ECC_2BITS_ERR = 3047u,
            S_DA_NAND_SPARE_CHKSUM_ERR = 3048u,
            S_DA_NAND_HW_COPYBACK_DATA_INCONSISTENT = 3049u,
            S_DA_NAND_INVALID_PAGE_INDEX = 3050u,
            S_DA_NFI_NOT_SUPPORT = 3051u,
            S_DA_NFI_CS1_NOT_SUPPORT = 3052u,
            S_DA_NFI_16BITS_IO_NOT_SUPPORT = 3053u,
            S_DA_NFB_BOOTLOADER_NOT_EXIST = 3054u,
            S_DA_NAND_NO_GOOD_BLOCK = 3055u,
            S_DA_NAND_UBIIMG_NOT_SPARSEIMAGE = 3056u,
            S_DA_BOOTLOADER_IS_TOO_LARGE = 3057u,
            S_DA_SIBLEY_REWRITE_OBJ_MODE_REGION = 3058u,
            S_DA_SIBLEY_WRITE_B_HALF_IN_CTRL_MODE_REGION = 3059u,
            S_DA_SIBLEY_ILLEGAL_CMD = 3060u,
            S_DA_SIBLEY_PROGRAM_AT_THE_SAME_REGIONS = 3061u,
            S_DA_UART_GET_DATA_TIMEOUT = 3062u,
            S_DA_UART_GET_CHKSUM_LSB_TIMEOUT = 3063u,
            S_DA_UART_GET_CHKSUM_MSB_TIMEOUT = 3064u,
            S_DA_UART_DATA_CKSUM_ERROR = 3065u,
            S_DA_UART_RX_BUF_FULL = 3066u,
            S_DA_UART_RX_BUF_NOT_ENOUGH = 3067u,
            S_DA_FLASH_RECOVERY_BUF_NOT_ENOUGH = 3068u,
            S_DA_HANDSET_SEC_INFO_NOT_FOUND = 3069u,
            S_DA_HANDSET_SEC_INFO_MAC_VERIFY_FAIL = 3070u,
            S_DA_HANDSET_ROM_INFO_NOT_FOUND = 3071u,
            S_DA_HANDSET_FAT_INFO_NOT_FOUND = 3072u,
            S_DA_OPERATION_UNSUPPORT_FOR_NFB = 3073u,
            S_DA_BYPASS_POST_PROCESS = 3074u,
            S_DA_NOR_OTP_NOT_SUPPORT = 3075u,
            S_DA_NOR_OTP_EXIST = 3076u,
            S_DA_NOR_OTP_LOCKED = 3077u,
            S_DA_NOR_OTP_GETSIZE_FAIL = 3078u,
            S_DA_NOR_OTP_READ_FAIL = 3079u,
            S_DA_NOR_OTP_PROGRAM_FAIL = 3080u,
            S_DA_NOR_OTP_LOCK_FAIL = 3081u,
            S_DA_NOR_OTP_LOCK_CHECK_STATUS_FAIL = 3082u,
            S_DA_BLANK_FLASH = 3083u,
            S_DA_CODE_AREA_IS_BLANK = 3084u,
            S_DA_SEC_RO_AREA_IS_BLANK = 3085u,
            S_DA_NOR_OTP_UNLOCKED = 3086u,
            S_DA_UNSUPPORTED_BBCHIP = 3087u,
            S_DA_FAT_NOT_EXIST = 3088u,
            S_DA_EXT_SRAM_NOT_FOUND = 3089u,
            S_DA_EXT_DRAM_NOT_FOUND = 3090u,
            S_DA_MT_PIN_LOW = 3091u,
            S_DA_MT_PIN_HIGH = 3092u,
            S_DA_MT_PIN_SHORT = 3093u,
            S_DA_MT_BUS_ERROR = 3094u,
            S_DA_MT_ADDR_NOT_2BYTE_ALIGNMENT = 3095u,
            S_DA_MT_ADDR_NOT_4BYTE_ALIGNMENT = 3096u,
            S_DA_MT_SIZE_NOT_2BYTE_ALIGNMENT = 3097u,
            S_DA_MT_SIZE_NOT_4BYTE_ALIGNMENT = 3098u,
            S_DA_MT_DEDICATED_PATTERN_ERROR = 3099u,
            S_DA_MT_INC_PATTERN_ERROR = 3100u,
            S_DA_MT_DEC_PATTERN_ERROR = 3101u,
            S_DA_NFB_BLOCK_0_IS_BAD = 3102u,
            S_DA_CUST_PARA_AREA_IS_BLANK = 3103u,
            S_DA_ENTER_RELAY_MODE_FAIL = 3104u,
            S_DA_ENTER_RELAY_MODE_IS_FORBIDDEN_AFTER_META = 3105u,
            S_DA_NAND_PAGE_READ_FAILED = 3106u,
            S_DA_NAND_IMAGE_BLOCK_NO_EXIST = 3107u,
            S_DA_NAND_IMAGE_LIST_NOT_EXIST = 3108u,
            S_DA_MBA_RESOURCE_NO_EXIST_IN_TARGET = 3109u,
            S_DA_MBA_PROJECT_VERSION_NO_MATCH_WITH_TARGET = 3110u,
            S_DA_MBA_UPDATING_RESOURCE_NO_EXIST_IN_TARGET = 3111u,
            S_DA_MBA_UPDATING_RESOURCE_SIZE_EXCEED_IN_TARGET = 3112u,
            S_DA_NAND_BIN_SIZE_EXCEED_MAX_SIZE = 3113u,
            S_DA_NAND_EXCEED_CONTAINER_LIMIT = 3114u,
            S_DA_NAND_REACH_END_OF_FLASH = 3115u,
            S_DA_NAND_OTP_NOT_SUPPORT = 3116u,
            S_DA_NAND_OTP_EXIST = 3117u,
            S_DA_NAND_OTP_LOCKED = 3118u,
            S_DA_NAND_OTP_LOCK_FAIL = 3119u,
            S_DA_NAND_OTP_UNLOCKED = 3120u,
            S_DA_OTP_NOT_SUPPORT = 3121u,
            S_DA_OTP_EXIST = 3122u,
            S_DA_OTP_LOCKED = 3123u,
            S_DA_OTP_GETSIZE_FAIL = 3124u,
            S_DA_OTP_READ_FAIL = 3125u,
            S_DA_OTP_PROGRAM_FAIL = 3126u,
            S_DA_OTP_LOCK_FAIL = 3127u,
            S_DA_OTP_LOCK_CHECK_STATUS_FAIL = 3128u,
            S_DA_OTP_UNLOCKED = 3129u,
            S_DA_SEC_RO_ILLEGAL_MAGIC_TAIL = 3130u,
            S_DA_HANDSET_FOTA_INFO_NOT_FOUND = 3131u,
            S_DA_HANDSET_UA_INFO_NOT_FOUND = 3132u,
            S_DA_SB_FSM_INVALID_INFO = 3133u,
            S_DA_NFB_TARGET_DUAL_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI = 3134u,
            S_DA_NFB_TARGET_DUAL_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI = 3135u,
            S_DA_NFB_TARGET_IS_SINGLE_BL_BUT_PC_NOT = 3136u,
            S_DA_NFB_TARGET_IS_DUAL_BL_BUT_PC_NOT = 3137u,
            S_DA_NOR_TARGET_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI = 3138u,
            S_DA_NOR_TARGET_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI = 3139u,
            S_DA_NOR_TARGET_IS_NOT_NEW_BL_BUT_PC_IS = 3140u,
            S_DA_NOR_TARGET_IS_NEW_BL_BUT_PC_NOT = 3141u,
            S_DA_DOWNLOAD_BOOTLOADER_FLASH_DEV_IS_NONE = 3142u,
            S_DA_DOWNLOAD_BOOTLOADER_FLASH_DEV_IS_NOT_SUPPORTED = 3143u,
            S_DA_DOWNLOAD_BOOTLOADER_BEGIN_ADDR_OVERLAPS_WITH_PREVIOUS_BOUNDARY = 3144u,
            S_DA_UPDATE_BOOTLOADER_EXIST_MAGIC_NOT_MATCHED = 3145u,
            S_DA_UPDATE_BOOTLOADER_FILE_TYPE_NOT_MATCHED = 3146u,
            S_DA_UPDATE_BOOTLOADER_FILE_SIZE_EXCEEDS_BOUNDARY_ADDR = 3147u,
            S_DA_UPDATE_BOOTLOADER_BEGIN_ADDR_NOT_MATCHED = 3148u,
            S_DA_EMMC_FLASH_NOT_FOUND = 3149u,
            S_DA_EMMC_FW_VER_CHECK_FAIL = 3150u,
            S_DA_SDMMC_FLASH_NOT_FOUND = 3151u,
            S_DA_SDMMC_CONFIG_FAILED = 3152u,
            S_DA_SDMMC_READ_FAILED = 3153u,
            S_DA_SDMMC_WRITE_FAILED = 3154u,
            S_DA_SDMMC_ERR_CRC = 3155u,
            S_DA_SDMMC_ERR_TIMEOUT = 3156u,
            S_DA_SDMMC_UNSUPPORTED = 3157u,
            S_DA_DSPBL_CHECK_PLATFORM_FAILED = 3158u,
            S_DA_UFS_FLASH_NOT_FOUND = 3159u,
            S_DA_UFS_CONFIG_FAILED = 3160u,
            S_DA_UFS_READ_FAILED = 3161u,
            S_DA_UFS_WRITE_FAILED = 3162u,
            S_DA_UFS_ERR_TIMEOUT = 3163u,
            S_DA_UFS_UNSUPPORTED = 3164u,
            S_DA_UFS_OTP_NOT_SUPPORT = 3165u,
            S_DA_UFS_OTP_EXIST = 3166u,
            S_DA_UFS_OTP_LOCKED = 3167u,
            S_DA_UFS_OTP_LOCK_FAIL = 3168u,
            S_DA_UFS_OTP_UNLOCKED = 3169u,
            S_DA_HANDSET_SEC_CFG_NOT_FOUND = 3170u,
            S_DA_EMMC_OTP_NOT_SUPPORT = 3171u,
            S_DA_EMMC_OTP_EXIST = 3172u,
            S_DA_EMMC_OTP_LOCKED = 3173u,
            S_DA_EMMC_OTP_LOCK_FAIL = 3174u,
            S_DA_EMMC_OTP_UNLOCKED = 3175u,
            S_DA_READ_IMEI_PID_SWV_NOT_SUPPORT = 3176u,
            S_DA_NFI_EMPTY_PAGE = 3177u,
            S_DA_INVALID_STORAGE_TYPE = 3178u,
            S_DA_SEND_CMD_FAIL = 3179u,
            S_DA_READ_CMD_ACK_FAIL = 3180u,
            S_DA_READ_FLASH_STATUS_INFO_FAIL = 3181u,
            S_PL_VALIDATION_FAIL = 3182u,
            S_STORAGE_NOT_MATCH = 3183u,
            S_CHIP_TYPE_NOT_MATCH = 3184u,
            S_DA_EXCEED_MAX_PARTITION_COUNT = 3185u,
            S_DA_ERROR_END = 3186u,
            S_FT_ERROR_BEGIN = 4000u,
            S_FT_CALLBACK_DA_REPORT_FAIL = 4000u,
            S_FT_DA_NO_RESPONSE = 4001u,
            S_FT_DA_SYNC_INCORRECT = 4002u,
            S_FT_DA_VERSION_INCORRECT = 4003u,
            S_FT_DA_INIT_SYNC_ERROR = 4004u,
            S_FT_GET_DSP_VER_FAIL = 4005u,
            S_FT_CHANGE_BAUDRATE_FAIL = 4006u,
            S_FT_SET_DOWNLOAD_BLOCK_FAIL = 4007u,
            S_FT_DOWNLOAD_FAIL = 4008u,
            S_FT_READBACK_FAIL = 4009u,
            S_FT_FORMAT_FAIL = 4010u,
            S_FT_FINISH_CMD_FAIL = 4011u,
            S_FT_ENABLE_WATCHDOG_FAIL = 4012u,
            S_FT_NFB_DOWNLOAD_BOOTLOADER_FAIL = 4013u,
            S_FT_NFB_DOWNLOAD_CODE_FAIL = 4014u,
            S_FT_NFB_INVALID_BOOTLOADER_DRAM_SETTING = 4015u,
            S_FT_NAND_READADDR_NOT_PAGE_ALIGNMENT = 4016u,
            S_FT_NAND_READLEN_NOT_PAGE_ALIGNMENT = 4017u,
            S_FT_READ_REG16_FAIL = 4018u,
            S_FT_WRITE_REG16_FAIL = 4019u,
            S_FT_CUST_PARA_GET_INFO_FAIL = 4020u,
            S_FT_CUST_PARA_READ_FAIL = 4021u,
            S_FT_CUST_PARA_WRITE_FAIL = 4022u,
            S_FT_INVALID_FTCFG_OPERATION = 4023u,
            S_FT_INVALID_CUST_PARA_OPERATION = 4024u,
            S_FT_INVALID_SEC_RO_OPERATION = 4025u,
            S_FT_INVALID_OTP_OPERATION = 4026u,
            S_FT_POST_PROCESS_FAIL = 4027u,
            S_FT_FTCFG_UPDATE_FAIL = 4028u,
            S_FT_SEC_RO_GET_INFO_FAIL = 4029u,
            S_FT_SEC_RO_READ_FAIL = 4030u,
            S_FT_SEC_RO_WRITE_FAIL = 4031u,
            S_FT_ENABLE_DRAM_FAIL = 4032u,
            S_FT_FS_FINDFIRSTEX_FAIL = 4033u,
            S_FT_FS_FINDNEXTEX_FAIL = 4034u,
            S_FT_FS_FOPEN_FAIL = 4035u,
            S_FT_FS_GETFILESIZE_FAIL = 4036u,
            S_FT_FS_READ_FAIL = 4037u,
            S_FT_FS_FILENAME_INVALID = 4038u,
            S_FT_FS_FILENAME_TOO_LONG = 4039u,
            S_FT_FS_ASSERT = 4040u,
            S_FT_OTP_ADDR_NOT_WORD_ALIGNMENT = 4041u,
            S_FT_OTP_LENGTH_NOT_WORD_ALIGNMENT = 4042u,
            S_FT_OTP_INVALID_ADDRESS_RANGE = 4043u,
            S_FT_NAND_READ_TO_BUFFER_NOT_SUPPORT = 4044u,
            S_FT_GET_PROJECT_ID_FAIL = 4045u,
            S_FT_ENFB_ROM_FILE_SMALL_THAN_HEADER_DESCRIBE = 4046u,
            S_FT_RW_EXTRACT_NFB_FAIL = 4047u,
            S_FT_MEMORY_TEST_FAIL = 4048u,
            S_FT_CHECK_BOOTLOADER_FEATURE_FAIL = 4049u,
            S_FT_NEED_DOWNLOAD_ALL_FAIL = 4050u,
            S_FT_NEW_PARTITION_TBL_FAIL = 4051u,
            S_FT_UPDATE_PARTITION_TBL_FAIL = 4052u,
            S_FT_PROTOCOL_EXCEPTION = 4053u,
            S_FT_PROTOCOL_EXCEPTION_WITHOUT_EFUSE_VERIFY = 4054u,
            S_FT_COMMUNICATION_ERROR_WITHOUT_EFUSE_VERIFY = 4055u,
            S_FT_GET_MAC_FAIL = 4056u,
            S_FT_GET_TIME_FAIL = 4057u,
            S_FT_GET_MEMORY_FAIL = 4058u,
            S_FT_GET_MODEM_FAIL = 4059u,
            S_FT_ERROR_END = 4060u,
            S_FTHND_ERROR_BEGIN = 5000u,
            S_AUTH_HANDLE_IS_NOT_READY = 5000u,
            S_INVALID_AUTH_FILE = 5001u,
            S_INVALID_DA_FILE = 5002u,
            S_DA_HANDLE_IS_NOT_READY = 5003u,
            S_FTHND_ILLEGAL_INDEX = 5004u,
            S_FTHND_HANDLE_BUSY_NOW = 5005u,
            S_FTHND_FILE_IS_UPDATED = 5006u,
            S_FTHND_FILE_IS_NOT_LOADED_YET = 5007u,
            S_FTHND_FILE_LOAD_FAIL = 5008u,
            S_FTHND_FILE_UNLOAD_FAIL = 5009u,
            S_FTHND_LIST_IS_EMPTY = 5010u,
            S_DL_SCAT_INCORRECT_FORMAT = 5011u,
            S_DL_SCAT_ADDR_IS_NOT_WORD_ALIGN = 5012u,
            S_DL_SCAT_OFFSET_IS_NOT_WORD_ALIGN = 5013u,
            S_DL_SCAT_ADDR_IS_NOT_ASCENDING_ORDER = 5014u,
            S_DL_SCAT_JUMPTABLE_IS_NOT_ABSOLUTE_ADDR = 5015u,
            S_DL_LOAD_REGION_IS_OVERLAP = 5016u,
            S_DL_LOAD_REGION_NOT_FOUND = 5017u,
            S_DL_NOT_RESOURCE_BIN = 5018u,
            S_DL_MULTIBIN_MECHANISM_DISABLED = 5019u,
            S_DL_RESOURCE_NOT_MATCH_IN_JUMPTABLE = 5020u,
            S_DL_RESOURCE_MUST_DOWNLOAD_WITH_JUMPTABLE = 5021u,
            S_DL_OVERLAP_WITH_EXISTING_RESOURCE = 5022u,
            S_DL_INVALID_RESOURCE_BIN = 5023u,
            S_DL_JUMPTABLE_INCONSISTENT_WITH_SCAT = 5024u,
            S_DL_INVALID_JUMPTABLE = 5025u,
            S_DL_IMG_BEGIN_ADDR_NOT_BLOCK_ALIGNMENT = 5026u,
            S_DL_REGION_ADDR_INCONSISTENT_WITH_SCAT = 5027u,
            S_DL_REGION_ADDR_INCONSISTENT_WITH_RESOURCE_ADDR = 5028u,
            S_DL_INVALID_BOOTLOADER = 5029u,
            S_DL_INVALID_BOOTLOADER_CHKSUM_SEED = 5030u,
            S_DL_BOOTLOADER_IS_NOT_LOADED_YET = 5031u,
            S_DL_BOOTLOADER_NOT_FOUND = 5032u,
            S_DL_REMOTE_FILE_UNSUPPORTED_BY_BL_AUTOLOAD = 5033u,
            S_DLIST_SAME_BBCHIP_SW_VER = 5034u,
            S_DLIST_BBCHIP_HW_VER_NOT_MATCHED = 5035u,
            S_DLIST_NO_MATCHED_DL_HANDLE_FOUND = 5036u,
            S_DLIST_DL_HANDLE_NOT_IN_LIST = 5037u,
            S_DLIST_DL_HANDLE_ALREADY_IN_LIST = 5038u,
            S_FTHND_CALLBACK_REMOTE_GET_FILE_LEN_FAIL = 5039u,
            S_FTHND_CALLBACK_REMOTE_READ_FILE_FAIL = 5040u,
            S_FTHND_CALLBACK_FILE_INTEGRITY_CHECK_FAIL = 5041u,
            S_UNSUPPORTED_VER_OF_AUTH_FILE = 5042u,
            S_DL_PROJECT_ID_DIFF_BETWEEN_MAIN_CODE_AND_JUMP_TBL = 5043u,
            S_DL_SCAT_OPEN_FAIL = 5044u,
            S_FTHND_CALLBACK_COM_INIT_STAGE_FAIL = 5045u,
            S_DL_UNSECURE_MAUI_TO_SECURE_BB = 5046u,
            S_FTHND_CALLBACK_REMOTE_GET_SIG_LEN_FAIL = 5047u,
            S_FTHND_CALLBACK_REMOTE_READ_SIG_FAIL = 5048u,
            S_DL_RESOURCE_MUST_DOWNLOAD_WITH_ANOTHERBIN = 5049u,
            S_DL_RESOURCE_MUST_DOWNLOAD_WITH_ENFB = 5050u,
            S_DL_PROJECT_ID_DIFF_BETWEEN_MAIN_CODE_AND_RESOURCE_BIN = 5051u,
            S_DL_PROJECT_ID_DIFF_AMONG_RESOURCE_BIN = 5052u,
            S_DL_UNSECURE_BOOTLOADER_TO_SECURE_BB = 5053u,
            S_DL_GET_DRAM_SETTING_FAIL = 5054u,
            S_DL_FOTA_INFO_IMAGE_NUMBER_NOT_MATCH_WITH_SCATTER_FILE = 5055u,
            S_DL_PROJECT_ID_DIFF_BETWEEN_THIRD_ROM_AND_RESOURCE_BIN = 5056u,
            S_DL_FOTA_SEC_INFO_MAC_ADDR_NOT_MATCH_WITH_MAUI = 5057u,
            S_DL_PC_NFB_DUAL_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI = 5058u,
            S_DL_PC_NFB_DUAL_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI = 5059u,
            S_DL_PC_NOR_XIP_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI = 5060u,
            S_DL_PC_NOR_XIP_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI = 5061u,
            S_INVALID_SCERT_FILE = 5062u,
            S_UNSUPPORTED_VER_OF_SCERT_FILE = 5063u,
            S_DL_PC_BL_FILE_TYPE_IS_DUPLICATED = 5064u,
            S_DL_PC_BL_FILE_DEV_IS_DIFFERENT = 5065u,
            S_DL_PC_BL_INVALID_GFH_FILE_INFO = 5066u,
            S_DL_PC_BL_INVALID_GFH_BL_INFO = 5067u,
            S_DL_PC_BL_INVALID_GFH_ANTI_CLONE = 5068u,
            S_DL_PMT_ERR_NO_SPACE = 5069u,
            S_DL_PC_BL_INVALID_BL_SEC_K = 5070u,
            S_DL_PC_BL_BL_SEC_K_HASH_FAIL = 5071u,
            S_DL_WRITE_PT_FAIL = 5072u,
            S_DL_READ_PT_FAIL = 5073u,
            S_DL_BL_HASH_FAIL = 5074u,
            S_DL_BL_HASH_MISMATCH = 5075u,
            S_DL_BL_SIG_FAIL = 5076u,
            S_DL_BL_SIG_MISMATCH = 5077u,
            S_DL_BL_SIG_TYPE_UNSUPPORTED = 5078u,
            S_DL_SCAT_VERSION_UNSUPPORTED = 5079u,
            S_DL_SCAT_IS_DOWNLOAD_PROP_ERR = 5080u,
            S_FTHND_ERROR_END = 5081u,
            S_SECURITY_ERROR_BEGIN = 6000u,
            S_SECURITY_CALLBACK_SLA_CHALLENGE_FAIL = 6000u,
            S_SECURITY_SLA_WRONG_AUTH_FILE = 6001u,
            S_SECURITY_SLA_INVALID_AUTH_FILE = 6002u,
            S_SECURITY_SLA_CHALLENGE_FAIL = 6003u,
            S_SECURITY_SLA_FAIL = 6004u,
            S_SECURITY_DAA_FAIL = 6005u,
            S_SECURITY_SBC_FAIL = 6006u,
            S_SECURITY_SF_SECURE_VER_CHECK_FAIL = 6007u,
            S_SECURITY_SF_HANDSET_SECURE_CUSTOM_NAME_NOT_MATCH = 6008u,
            S_SECURITY_SF_FTCFG_LOCKDOWN = 6009u,
            S_SECURITY_SF_CODE_DOWNLOAD_FORBIDDEN = 6010u,
            S_SECURITY_SF_CODE_READBACK_FORBIDDEN = 6011u,
            S_SECURITY_SF_CODE_FORMAT_FORBIDDEN = 6012u,
            S_SECURITY_SF_SEC_RO_DOWNLOAD_FORBIDDEN = 6013u,
            S_SECURITY_SF_SEC_RO_READBACK_FORBIDDEN = 6014u,
            S_SECURITY_SF_SEC_RO_FORMAT_FORBIDDEN = 6015u,
            S_SECURITY_SF_FAT_DOWNLOAD_FORBIDDEN = 6016u,
            S_SECURITY_SF_FAT_READBACK_FORBIDDEN = 6017u,
            S_SECURITY_SF_FAT_FORMAT_FORBIDDEN = 6018u,
            S_SECURITY_SF_RESTRICTED_AREA_ACCESS_FORBIDDEN = 6019u,
            S_SECURITY_SECURE_CUSTOM_NAME_NOT_MATCH_BETWEEN_AUTH_AND_DL_HANDLE = 6020u,
            S_SECURITY_DOWNLOAD_FILE_IS_CORRUPTED = 6021u,
            S_SECURITY_NOT_SUPPORT = 6022u,
            S_SECURITY_BOOTLOADER_IMAGE_SIGNATURE_FAIL = 6023u,
            S_SECURITY_BOOTLOADER_ELDER_SW_VERSION_CANNOT_BE_DOWNLOADED = 6024u,
            S_SECURITY_BOOTLOADER_IMAGE_NO_SIGNATURE = 6025u,
            S_SECURITY_BOOTLOADER_CORRUPTED_SCATTER_FILE = 6026u,
            S_SECURITY_SECURE_USB_DL_NO_MAUI_IN_SCATTER_FILE = 6027u,
            S_SECURITY_SEND_CERT_FAIL = 6028u,
            S_SECURITY_SEND_AUTH_FAIL = 6029u,
            S_SECURITY_GET_SEC_CONFIG_FAIL = 6030u,
            S_SECURITY_GET_ME_ID_FAIL = 6031u,
            S_BROM_GET_HW_SW_VER_FAIL = 6032u,
            S_BROM_GET_HW_CODE_FAIL = 6033u,
            S_SECURITY_ROM_INFO_NOT_FOUND = 6034u,
            S_SECURITY_ROM_INFO_ID_MISMATCH = 6035u,
            S_SECURITY_SEC_CTRL_ID_MISMATCH = 6036u,
            S_SECURITY_SEC_KEY_ID_MISMATCH = 6037u,
            S_SECURITY_SECURE_USB_DL_FAIL = 6038u,
            S_SECURITY_SECURE_USB_DL_CHECK_TARGET_STATUS_FAIL = 6039u,
            S_SECURITY_SECURE_USB_DL_SEND_CHIP_STATUS_FAIL = 6040u,
            S_SECURITY_SECURE_USB_DL_DISABLED = 6041u,
            S_SECURITY_SECURE_USB_DL_ENABLED = 6042u,
            S_SECURITY_SECURE_USB_DL_IMAGE_PUBLIC_N_KEY_READ_FAIL = 6043u,
            S_SECURITY_SECURE_USB_DL_IMAGE_PUBLIC_E_KEY_READ_FAIL = 6044u,
            S_SECURITY_SECURE_USB_DL_IMAGE_SIGN_HEADER_NOT_FOUND = 6045u,
            S_SECURITY_SECURE_USB_DL_IMGAE_SIGNATURE_VERIFY_FAIL = 6046u,
            S_SECURITY_SECURE_USB_DL_IMAGE_HASH_FAIL = 6047u,
            S_SECURITY_SECURE_USB_DL_IMAGE_NOT_FOUND = 6048u,
            S_SECURITY_SECURE_USB_DL_INVALID_IMAGE_ARGUMENT = 6049u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INIT_FAIL = 6050u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_IMAGE_NAME_FAIL = 6051u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_IMAGE_NAME_LEN_FAIL = 6052u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_TYPE_FAIL = 6053u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_HEADER_FAIL = 6054u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_IMAGE_OFFSET_FAIL = 6055u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_SIGNATURE_HASH_FAIL = 6056u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_GET_CHECK_RESULT_FAIL = 6057u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_DOWNLOAD_IMAGE_INVALID = 6058u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_UNKNOWN_CHECK_RESULT = 6059u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRONG_OPERATION = 6060u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INVALID_HEADER_LENGTH = 6061u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INVALID_IMAGE_OFFSET = 6062u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INVALID_SIGNATURE_LENGTH = 6063u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_SIGNATURE_LENGTH_TOO_LARGE = 6064u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_IMAGE_NAME_LENGTH_TOO_LONG = 6065u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_EXT_HEADER_TOO_LARGE = 6066u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_EXT_HEADER_OFFSET_INVALID = 6067u,
            S_SECURITY_SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_EXT_HEADER_SELF_COPY_FAIL = 6068u,
            S_SECURITY_SEC_CFG_NOT_EXIST = 6069u,
            S_SECURITY_SEC_CFG_WRITE_CMD_INIT_FAIL = 6070u,
            S_SECURITY_SEC_CFG_WRONG_MAGIC_NUMBER = 6071u,
            S_SECURITY_SEC_CFG_IS_FULL_CANNOT_ADD_NEW_IMAGE = 6072u,
            S_SECURITY_SEC_CFG_IMAGE_NOT_FOUND_SO_CANNOT_UPDATE = 6073u,
            S_SECURITY_SEC_CFG_IMAGE_CUST_NAME_MISMATCH = 6074u,
            S_SECURITY_SEC_CFG_IMAGE_CANNOT_ROLL_BACK_SW_LOAD = 6075u,
            S_SECURITY_SEC_CFG_IMAGE_EXIST_CANNOT_CREATE_MEW_FILE = 6076u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_CFG_NOT_EXIST = 6077u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_MAGIC_INCORRECT = 6078u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_CANNOT_WRITE_TO_FIRST_BLOCK = 6079u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_YAFFS2_POST_PROCESS_FAIL = 6080u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_NAND_DEVICE = 6081u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_CANNOT_READ_BACK = 6082u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_READ_BACK_MAGIC_INCORRECT = 6083u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_READ_BACK_ID_INCORRECT = 6084u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_READ_BACK_STATUS_INCORRECT = 6085u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_READ_BACK_END_PATTERN_INCORRECT = 6086u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_SEC_CFG_CANNOT_OVERWRITE_NEXT_PARTITION = 6087u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_NAND_NOT_DETECTED = 6088u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_EMMC_NOT_DETECTED = 6089u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_EMMC_DEVICE = 6090u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_NAND_PAGE_SIZE_NOT_SUPPORT = 6091u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_NAND_FIND_GOOD_BLK_FAIL = 6092u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_NAND_ERASE_FAIL = 6093u,
            S_SECURITY_SEC_CFG_WRITE_FAIL_UNKNOWN_DEVIE_TYPE = 6094u,
            S_SECURITY_SEC_CFG_READ_FAIL_NAND_NOT_DETECTED = 6095u,
            S_SECURITY_SEC_CFG_READ_FAIL_EMMC_NOT_DETECTED = 6096u,
            S_SECURITY_SEC_CFG_READ_FAIL_EMMC_DEVICE = 6097u,
            S_SECURITY_SEC_CFG_READ_FAIL_NAND_PAGE_SIZE_NOT_SUPPORT = 6098u,
            S_SECURITY_SEC_CFG_READ_FAIL_NAND_FIND_GOOD_BLK_FAIL = 6099u,
            S_SECURITY_SEC_CFG_READ_FAIL_UNKNOWN_DEVIE_TYPE = 6100u,
            S_SECURITY_SEC_CFG_READ_FAIL_NAND_LOGICAL_READ_FAIL = 6101u,
            S_SECURITY_SEC_CFG_EXT_REGION_SPACE_OVERFLOW = 6102u,
            S_SECURITY_SECURE_USB_DL_ROM_INFO_UPDATE_REQUEST_FAIL = 6103u,
            S_SECURITY_SECURE_USB_DL_DA_RETURN_INVALID_TYPE = 6104u,
            S_SECURITY_SECURE_USB_DL_MOVE_IMAGE_HEADER_TO_END_FAIL = 6105u,
            S_SECURITY_SECURE_USB_DL_NO_NEED_TO_MOVE_IMAGE_HEADER = 6106u,
            S_SECURITY_SECURE_USB_DL_NO_NEED_TO_REMOVE_IMAGE_HEADER_AND_SIG = 6107u,
            S_SECURITY_CIPHER_DATA_UNALIGNED = 6108u,
            S_SECURITY_CIPHER_MODE_INVALID = 6109u,
            S_SECURITY_CIPHER_KEY_INVALID = 6110u,
            S_SECURITY_CIPHER_INIT_FAIL = 6111u,
            S_SECURITY_CIPHER_ROM_NOT_LOADED = 6112u,
            S_SECURITY_CIPHER_DEC_FAIL = 6113u,
            S_SECURITY_CIPHER_ENC_TEST_FAIL = 6114u,
            S_SECURITY_AES_VER_INVALID = 6115u,
            S_INVALID_IMGDEC_CFG = 6116u,
            S_SECURITY_IMGDEC_INVALID_FORCE_DEC_PARAM = 6117u,
            S_SECURITY_IMGDEC_INVALID_AES_KEY_SIZE = 6118u,
            S_SECURITY_IMGDEC_FAIL_IMAGE_NOT_ENCRYPTED = 6119u,
            S_SECURITY_IMGDEC_INIT_FAIL_FTH_IS_NULL = 6120u,
            S_SECURITY_IMGDEC_INIT_FAIL_DECH_IS_NULL = 6121u,
            S_SECURITY_INIDEC_FAIL_INI_NOT_ENCRYPTED = 6122u,
            S_SECURITY_INIDEC_INVALID_AES_KEY_SIZE = 6123u,
            S_SECURITY_INVALID_PROJECT = 6124u,
            S_SECURITY_SECRO_ANTICLONE_LENGTH_INVALID = 6125u,
            S_SECURITY_SECRO_HASH_INCORRECT = 6126u,
            S_SECURITY_SECRO_ENCRYPT_FAIL = 6127u,
            S_SECURITY_AC_REGION_NOT_FOUND_IN_SECROIMG = 6128u,
            S_SECURITY_ERROR_END = 6129u,
            S_EPP_COMMON_ERROR_BEGIN = 7000u,
            S_EPP_FAIL = 7000u,
            S_EPP_EXT_DRAM_NOT_FOUND = 7001u,
            S_EPP_EXT_DRAM_INIT_FAIL = 7002u,
            S_EPP_NO_EMI_CONFIG_PARAM_FAIL = 7003u,
            S_EPP_ERROR_END = 7004u,
            S_PL_MODE_UNSUPPORTED = 10001u,
            S_PL_MODE_FORBIDDEN = 10002u,
            S_PL_MODE_INVALID_ARGUMETS = 10003u,
            S_PL_READ_FAIL = 10004u,
            S_PL_WRITE_FAIL = 10005u,
            S_PL_READ_TIMEOUT = 10006u,
            S_PL_WRITE_TIMEOUT = 10007u,
            S_PL_DISC_CMD_NEEDED = 10008u,
            STATUS_ERR = 3221291009u,
            STATUS_ABORT = 3221291010u,
            STATUS_UNSUPPORT_CMD = 3221291011u,
            STATUS_UNSUPPORT_CTRL_CODE = 3221291012u,
            STATUS_PROTOCOL_ERR = 3221291013u,
            STATUS_PROTOCOL_BUFFER_OVERFLOW = 3221291014u,
            STATUS_INSUFFICIENT_BUFFER = 3221291015u,
            STATUS_USB_SCAN_ERR = 3221291016u,
            STATUS_INVALID_HSESSION = 3221291017u,
            STATUS_INVALID_SESSION = 3221291018u,
            STATUS_INVALID_STAGE = 3221291019u,
            STATUS_NOT_IMPLEMENTED = 3221291020u,
            STATUS_FILE_NOT_FOUND = 3221291021u,
            STATUS_OPEN_FILE_ERR = 3221291022u,
            STATUS_WRITE_FILE_ERR = 3221291023u,
            STATUS_READ_FILE_ERR = 3221291024u,
            STATUS_CREATE_FILE_ERR = 3221291025u,
            STATUS_UNSUPPORT_VERSION = 3221291026u,
            STATUS_MEM_ALLOC_FAIL = 3221291027u,
            STATUS_DEVICE_CTRL_EXCEPTION = 3221553153u,
            STATUS_SHUTDOWN_CMD_EXCEPTION = 3221553154u,
            STATUS_DOWNLOAD_EXCEPTION = 3221553155u,
            STATUS_UPLOAD_EXCEPTION = 3221553156u,
            STATUS_EXT_RAM_EXCEPTION = 3221553157u,
            STATUS_NOTIFY_SWITCH_USB_SPEED_EXCEPTION = 3221553158u,
            STATUS_READ_DATA_EXCEPTION = 3221553159u,
            STATUS_WRITE_DATA_EXCEPTION = 3221553160u,
            STATUS_FORMAT_EXCEPTION = 3221553161u,
            STATUS_OTP_OPERATION_EXCEPTION = 3221553162u,
            STATUS_SWITCH_USB_EXCEPTION = 3221553163u,
            STATUS_WRITE_EFUSE_EXCEPTION = 3221553164u,
            STATUS_READ_EFUSE_EXCEPTION = 3221553165u,
            STATUS_BROM_CMD_STARTCMD_FAIL = 3221618689u,
            STATUS_BROM_GET_BBCHIP_HW_VER_FAIL = 3221618690u,
            STATUS_BROM_CMD_SEND_DA_FAIL = 3221618691u,
            STATUS_BROM_CMD_JUMP_DA_FAIL = 3221618692u,
            STATUS_BROM_CMD_FAIL = 3221618693u,
            STATUS_CALLBACK_BROM_STAGE_FAIL = 3221618694u,
            STATUS_BROM_CONNECT_NOT_PL = 1074135041u,
            STATUS_PL_CMD_FAIL = 3221749761u,
            STATUS_DA_VERSION_INCORRECT = 3221684225u,
            STATUS_DA_NOT_FOUND = 3221684226u,
            STATUS_DA_SECTION_NOT_FOUND = 3221684227u,
            STATUS_DA_HASH_MISMATCH = 3221684228u,
            STATUS_DA_EXCEED_MAX_NUM = 3221684229u,
            STATUS_SCATTER_FILE_INVALID = 3221422081u,
            STATUS_DA_FILE_INVALID = 3221422082u,
            STATUS_DA_SELECTION_ERR = 3221422083u,
            STATUS_PRELOADER_INVALID = 3221422084u,
            STATUS_EMI_HDR_INVALID = 3221422085u,
            STATUS_STORAGE_MISMATCH = 3221422086u,
            STATUS_INVALID_PARAMETERS = 3221422087u,
            STATUS_INVALID_GPT = 3221422088u,
            STATUS_INVALID_PMT = 3221422089u,
            STATUS_LAYOUT_CHANGED = 3221422090u,
            STATUS_INVALID_FORMAT_PARAMETER = 3221422091u,
            STATUS_UNKNOWN_STORAGE_SECTION_TYPE = 3221422092u,
            STATUS_UNKNOWN_SCATTER_FIELD = 3221422093u,
            STATUS_PARTITION_TBL_NOT_EXIST = 3221422094u,
            STATUS_SCATTER_HW_CHIP_ID_MISMATCH = 3221422095u,
            STATUS_SEC_CERT_FILE_NOT_FOUND = 3221422096u,
            STATUS_SEC_AUTH_FILE_NOT_FOUND = 3221422097u,
            STATUS_SEC_AUTH_FILE_NEEDED = 3221422098u,
            STATUS_EMI_CONTAINER_FILE_NOT_FOUND = 3221422099u,
            STATUS_SCATTER_FILE_NOT_FOUND = 3221422100u,
            STATUS_XML_FILE_OP_ERR = 3221422101u,
            STATUS_UNSUPPORTED_PAGE_SIZE = 3221422102u,
            STATUS_EMI_INFO_LEN_OFFSET_INVALID = 3221422103u,
            STATUS_EMI_INFO_LEN_INVALID = 3221422104u,
            STATUS_UNSUPPORT_STORAGE = 3221422105u,
            STATUS_RSC_MAGIC_ERR = 3221422106u,
            STATUS_CERT_INVALID = 3221422107u,
            STATUS_SPARSE_INCOMPLETE = 1074003969u,
            STATUS_OTP_LOCKED = 1074003970u,
            STATUS_OTP_UNLOCKED = 1074003971u,
            STATUS_CONTINUE = 1074003972u,
            STATUS_COMPLETE = 1074003973u,
            STATUS_DRAM_REPAIR_COMPLETE = 1074003974u,
            STATUS_UNSUPPORT_OP = 3221487617u,
            STATUS_THREAD = 3221487618u,
            STATUS_CHECKSUM_ERR = 3221487619u,
            STATUS_TOO_LARGE = 3221487620u,
            STATUS_UNKNOWN_SPARSE_CHUNK_TYPE = 3221487621u,
            STATUS_PARTITON_NOT_FOUND = 3221487622u,
            STATUS_READ_PT_FAIL = 3221487623u,
            STATUS_EXCEED_MAX_PART_NUM = 3221487624u,
            STATUS_UNKNOWN_STORAGE_TYPE = 3221487625u,
            STATUS_DRAM_TEST_FAILED = 3221487626u,
            STATUS_EXCEED_AVALIABLE_RANGE = 3221487627u,
            STATUS_WRITE_SPARSE_IMAGE_FAIL = 3221487628u,
            STATUS_REG_PERMISSION_DENIED = 3221487629u,
            STATUS_MMC_ERR = 3221487664u,
            STATUS_NAND_ERR = 3221487680u,
            STATUS_NAND_IN_PROGRESS = 3221487681u,
            STATUS_NAND_TIMEOUT = 3221487682u,
            STATUS_NAND_BAD_BLOCK = 3221487683u,
            STATUS_NAND_ERASE_FAILED = 3221487684u,
            STATUS_NAND_PAGE_PROGRAM_FAILED = 3221487685u,
            STATUS_NAND_FTL_OFFSET_JUMP_NEXT = 3221487686u,
            STATUS_EMI_SETTING_VERSION_ERROR = 3221487696u,
            STATUS_DA_OTP_NOT_SUPPORT = 3221487872u,
            STATUS_DA_OTP_LOCK_FAIL = 3221487874u,
            STATUS_EFUSE_UNKNOWN_ERR = 3221488128u,
            STATUS_EFUSE_WRITE_TIMEOUT_WITHOUT_VERIFY = 3221488129u,
            STATUS_EFUSE_BLOWN_DONE = 3221488130u,
            STATUS_EFUSE_REVERT_BIT = 3221488131u,
            STATUS_EFUSE_BLOWN_PARTLY = 3221488132u,
            STATUS_EFUSE_INVALID_ARGUMENT = 3221488133u,
            STATUS_EFUSE_VALUE_IS_NOT_ZERO = 3221488134u,
            STATUS_EFUSE_BLOWN_INCORRECT_DATA = 3221488135u,
            STATUS_EFUSE_BROKEN = 3221488136u,
            STATUS_EFUSE_BLOW_ERR = 3221488137u,
            STATUS_EFUSE_DATA_PROCESS_ERR = 3221488138u,
            STATUS_EFUSE_UNLOCK_BPKEY_ERR = 3221488139u,
            STATUS_EFUSE_CREATE_LIST_ERR = 3221488140u,
            STATUS_EFUSE_WRITE_REGISTER_ERR = 3221488141u,
            STATUS_EFUSE_PADDING_TYPE_MISMATCH = 3221488142u,
            STATUS_EFUSE_CON_LOCKED = 3221488143u,
            STATUS_EFUSE_RSA_ERROR = 3221488144u,
            STATUS_DA_EMMC_OTP_NOT_SUPPORT = 3221487872u,
            STATUS_DA_EMMC_OTP_LOCKED = 1074003970u,
            STATUS_DA_EMMC_OTP_LOCK_FAIL = 3221487874u,
            STATUS_DA_EMMC_OTP_UNLOCKED = 1074003971u,
            STATUS_DA_NAND_OTP_NOT_SUPPORT = 3221487872u,
            STATUS_DA_NAND_OTP_LOCKED = 1074003970u,
            STATUS_DA_NAND_OTP_LOCK_FAIL = 3221487874u,
            STATUS_DA_NAND_OTP_UNLOCKED = 1074003971u,
            STATUS_SEC_ROM_INFO_NOT_FOUND = 3221356545u,
            STATUS_SEC_CUST_NAME_NOT_FOUND = 3221356546u,
            STATUS_SEC_ROM_INFO_DEVICE_NOT_SUPPORTED = 3221356547u,
            STATUS_SEC_DL_FORBIDDEN = 3221356548u,
            STATUS_SEC_IMG_TOO_LARGE = 3221356549u,
            STATUS_SEC_PL_VFY_FAIL = 3221356550u,
            STATUS_SEC_IMG_VFY_FAIL = 3221356551u,
            STATUS_SEC_HASH_OP_FAIL = 3221356552u,
            STATUS_SEC_HASH_BINDING_CHK_FAIL = 3221356553u,
            STATUS_SEC_INVALID_BUF = 3221356554u,
            STATUS_SEC_BINDING_HASH_NOT_AVAIL = 3221356555u,
            STATUS_SEC_WRITE_DATA_NOT_ALLOWED = 3221356556u,
            STATUS_SEC_FORMAT_NOT_ALLOWED = 3221356557u,
            STATUS_SEC_SV5_PUBK_AUTH_FAIL = 3221356558u,
            STATUS_SEC_SV5_HASH_VFY_FAIL = 3221356559u,
            STATUS_SEC_SV5_RSA_OP_FAIL = 3221356560u,
            STATUS_SEC_SV5_RSA_VFY_FAIL = 3221356561u,
            STATUS_SEC_SV5_GFH_NOT_FOUND = 3221356562u,
            STATUS_SEC_NOT_VALID_CERT1 = 3221356563u,
            STATUS_SEC_NOT_VALID_CERT2 = 3221356564u,
            STATUS_SEC_NOT_VALID_IMGHDR = 3221356565u,
            STATUS_SEC_SIG_SZ_NOT_VALID = 3221356566u,
            STATUS_SEC_PSS_OP_FAIL = 3221356567u,
            STATUS_SEC_CERT_AUTH_FAIL = 3221356568u,
            STATUS_SEC_PUBK_AUTH_MISMATCH_N_SIZE = 3221356569u,
            STATUS_SEC_PUBK_AUTH_MISMATCH_E_SIZE = 3221356570u,
            STATUS_SEC_PUBK_AUTH_MISMATCH_N = 3221356571u,
            STATUS_SEC_PUBK_AUTH_MISMATCH_E = 3221356572u,
            STATUS_SEC_PUBK_AUTH_MISMATCH_HASH = 3221356573u,
            STATUS_SEC_CERT_OBJ_NOT_FOUND = 3221356574u,
            STATUS_SEC_CERT_OID_NOT_FOUND = 3221356575u,
            STATUS_SEC_CERT_OUT_OF_RANGE = 3221356576u,
            STATUS_SEC_OID_NOT_MATCH = 3221356577u,
            STATUS_SEC_LEN_NOT_MATCH = 3221356578u,
            STATUS_SEC_ASN1_UNKNOWN_OP = 3221356579u,
            STATUS_SEC_OID_IDX_OUT_OF_RANGE = 3221356580u,
            STATUS_SEC_OID_TOO_LARGE = 3221356581u,
            STATUS_SEC_PUBK_SZ_MISMATCH = 3221356582u,
            STATUS_SEC_SWID_MISMATCH = 3221356583u,
            STATUS_SEC_HASH_SZ_MISMATCH = 3221356584u,
            STATUS_SEC_IMGHDR_TYPE_MISMATCH = 3221356585u,
            STATUS_SEC_IMG_TYPE_MISMATCH = 3221356586u,
            STATUS_SEC_IMGHDR_HASH_VFY_FAIL = 3221356587u,
            STATUS_SEC_IMG_HASH_VFY_FAIL = 3221356588u,
            STATUS_SEC_VIOLATE_ANTI_ROLLBACK = 3221356589u,
            STATUS_SEC_SECCFG_NOT_FOUND = 3221356590u,
            STATUS_SEC_SECCFG_MAGIC_INCORRECT = 3221356591u,
            STATUS_SEC_SECCFG_NOT_VALID = 3221356592u,
            STATUS_SEC_CIPHER_MODE_INVALID = 3221356593u,
            STATUS_SEC_CIPHER_KEY_INVALID = 3221356594u,
            STATUS_SEC_CIPHER_DATA_UNALIGNED = 3221356595u,
            STATUS_SEC_GFH_FILE_INFO_NOT_FOUND = 3221356596u,
            STATUS_SEC_GFH_ANTI_CLONE_NOT_FOUND = 3221356597u,
            STATUS_SEC_GFH_SEC_CFG_NOT_FOUND = 3221356598u,
            STATUS_SEC_UNSUPPORTED_SOURCE_TYPE = 3221356599u,
            STATUS_SEC_CUST_NAME_MISMATCH = 3221356600u,
            STATUS_SEC_INVALID_ADDRESS = 3221356601u,
            STATUS_SEC_CERT_VER_NOT_SYNC = 3221356608u,
            STATUS_SEC_SIG_NOT_SYNC = 3221356609u,
            STATUS_SEC_EXT_ALL_IN_ONE_SIG_REJECT = 3221356610u,
            STATUS_SEC_EXT_ALL_IN_ONE_SIG_NOT_EXIST = 3221356611u,
            STATUS_SEC_COMM_KEY_IS_NOT_SET = 3221356612u,
            STATUS_SEC_COMM_DEVINFO_CHK_FAIL = 3221356613u,
            STATUS_SEC_BOOTIMG_COUNT_OVERFLOW = 3221356614u,
            STATUS_SEC_SIG_NOT_FOUND = 3221356615u,
            STATUS_SEC_BOOTIMG_SPECIAL_HANDLE = 3221356616u,
            STATUS_SEC_REMOTE_SECURITY_POLICY_DISABLED = 3221356617u,
            STATUS_SEC_RSA_OAEP_FAIL = 3221356618u,
            STATUS_SEC_INSUFFICIENT_BUFFER = 3221356619u,
            STATUS_SEC_IMG_VER_NOT_SYNC = 3221356620u,
            STATUS_SEC_GET_OTP_VALUE_FAIL = 3221356621u,
            STATUS_SEC_INVALID_UNIT_SZ = 3221356622u,
            STATUS_SEC_INVALID_GROUP_IDX = 3221356623u,
            STATUS_SEC_IMG_VER_OVERFLOW = 3221356624u,
            STATUS_SEC_OTP_TABLE_NOT_INIT = 3221356625u,
            STATUS_SEC_INVALID_PART_NAME = 3221356626u,
            STATUS_SEC_INVALID_DA_VER = 3221356627u,
            S_MAX_STATUS_WIDTH = 2147483647u
        }
    }
}

﻿using System;
using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Security;

namespace SharpMTKClient_Engy.MediaTeK
{
    public class FlashToolLib
    {
        //Variables Solas
        public static IntPtr ACAFD09A;

        //Metodos de Llamados
        
        //Metodos Ordenados Alfabeticamente Para Mejor Ubicacion
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int AUTH_Create(ref IntPtr D793F306);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int AUTH_Destroy(ref IntPtr intptr_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int AUTH_IsReady(IntPtr intptr_0, ref ED93B522 ed93B522_0, bool bool_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int AUTH_Load(IntPtr intptr_0, byte[] byte_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int AUTH_Unload(IntPtr B53D3B38);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int Boot_FlashTool(IntPtr ********, ref F12CA73B f12CA73B_0, ref C28D96B7 CFAB9A3F, ref int int_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int Boot_META(IntPtr A0840DBA, ref _89265D32 FDAF16AE, ref C28D96B7 F285E511, ref int F21DB923);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int BootROM_BootMode(ref _42BD8819 _42BD8819_0);

        [DllImport("Flashtoollib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int Brom_Connect(ref IntPtr intptr_1, ref IntPtr C02DC585, ushort ushort_1, BB22AA0D bb22AA0D_0, EXT_CLOCK A58323BD, ref int ********, uint uint_0, uint uint_1);

        [DllImport("Flashtoollib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int Brom_Create(ref IntPtr intptr_1, IntPtr intptr_2, BB22AA0D B13E4D90, EXT_CLOCK EXT_CLOCK_0, uint uint_0, ref int int_0, uint F9836E84, uint AC07478E, ref C28D96B7 DD044BBB);

        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int Brom_DebugOn();

        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int Brom_Debug_SetLogFilename(byte[] DB890233);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern IntPtr ChipTypeToString(BB22AA0D bb22AA0D_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int DA_Create(ref IntPtr CD30B228);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int DA_Destroy(ref IntPtr D5A49ABE);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int DA_GetInfo(IntPtr E23FC50A, ref _3624F3BF _3624F3BF_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int DA_IsReady(IntPtr intptr_1, ref _3624F3BF E38498AE, bool bool_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, EntryPoint = "DA_IsReady", SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int DA_IsReady_1(IntPtr intptr_1, IntPtr intptr_2, bool A9BF1F26);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int DA_Load(IntPtr intptr_1, byte[] B98DB920, bool bool_0, bool bool_1);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int DA_Unload(IntPtr intptr_1);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_AddHandleToList(IntPtr intptr_5, IntPtr intptr_6);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_AutoLoadRomImages(IntPtr D52D1931, byte[] byte_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_BL_EXT_Load(IntPtr DC8B4608, ref byte[] ********);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_BL_Load(IntPtr intptr_5, ref byte[] BC0E7B9E);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Create(ref IntPtr intptr_5);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_CreateList(ref IntPtr intptr_5);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Destroy(ref IntPtr intptr_5);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_DestroyList(ref IntPtr intptr_5);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int DL_GetCount(IntPtr D6259A2E, out ushort ushort_1);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern IntPtr DL_GetDRAMSetting(IntPtr ********, ref _6E05740E _6E05740E_0, ref D893BE95 d893BE95_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int DL_GetFTHandle(ref IntPtr B93890B3, ref IntPtr BB36DA99);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int DL_GetPlatformInfo(IntPtr intptr_1, ref _40196033 DE04F832);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int DL_GetScatterInfo(IntPtr intptr_5, ref _46374508 _46374508_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int DL_GetScatterVersion(IntPtr intptr_5, ref string string_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int DL_GetScatterVersion(ref IntPtr intptr_1, ref string string_3);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_LoadScatter(IntPtr A7AE0318, byte[] A38DB883, byte[] byte_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int DL_Rom_GetInfoAll(IntPtr intptr_1, IntPtr intptr_2, ushort E29D7F27);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Rom_Load(IntPtr D135E13C, ushort ********, byte[] byte_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Rom_SetEnableAttr(IntPtr intptr_5, ushort ushort_0, bool FB8F3CB7);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Rom_Unload(IntPtr intptr_5, ushort E901DF07);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Rom_UnloadAll(IntPtr D92A7A10);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_SetChecksumLevel(IntPtr intptr_5, _470114B8 _470114B8_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int DL_SetStopLoadFlag(IntPtr E9AA41BF, ref int C999E017);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_VerifyROMMemBuf(IntPtr intptr_5, ref BFB94637 C6025C00, ref A11A76BA a11A76BA_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int FlashReadback(ref _8A0D82B1 _8A0D82B1_0, ref _08BEFE15 _08BEFE15_0, ref C1B09017 F1A2F313, ref _932F52AC _932F52AC_0, ref _9530CBB6 D804292C, ref ******** BA2CB4B5, ref int int_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_ChangeCOM_Ex(ref IntPtr intptr_1, ref _1BAE2FAF _1BAE2FAF_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_Check_Battery(IntPtr intptr_1, ref AC9454A8 DC3BF5BE, ref _00118F26 _00118F26_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_CheckUSBStatus(IntPtr AF9FFF89, ref _8D8AE980 F88DDB90, ref C0A092A9 c0A092A9_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern bool FlashTool_Chip_isOldArch(byte[] byte_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_Connect(short short_0, ref _1B168808 _1B168808_0, ref _31956C37 _31956C37_0, ref ******** F5125B98, int D0306D2D, ref IntPtr intptr_1, bool F80695B1 = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_Connect_BROM(short FF886898, ref _1B168808 E1AA7AAE, ref IntPtr intptr_1, IntPtr intptr_2, bool bool_0 = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_Connect_BROM_ByName(byte[] E7B10D16, ref _1B168808 _1B168808_0, ref IntPtr intptr_1, ref int BB098413, bool bool_0 = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int FlashTool_Connect_Download_DA(ref _1B168808 _1B168808_0, ref IntPtr intptr_1, ref _31956C37 _31956C37_0, ref int E1B527B2, bool C915293B = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, EntryPoint = "FlashTool_Connect_Download_DA", SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_Connect_Download_DA_1(ref _1B168808 _1B168808_0, ref IntPtr intptr_1, ref _31956C37 _31956C37_0, IntPtr B53A2831, bool bool_0 = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int FlashTool_Device_Control(IntPtr intptr_1, _40159E1B _40159E1B_0, IntPtr E5250ABB, uint BF3CE895, IntPtr intptr_2, uint uint_0, uint E0BA3C0C);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int FlashTool_Disconnect(ref IntPtr B9953D22);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int FlashTool_Disconnect_BROM(ref IntPtr intptr_1);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int FlashTool_Download(IntPtr A337D70B, ref ******** C132F28A, ref EDB22AA5 ED989613);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int FlashTool_EnableWatchDogTimeout(IntPtr CA0FF915, ref FA28852A ECAAF634);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_FirmwareUpdate(IntPtr intptr_1, byte[] DE376E9C, string string_3, uint ********);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int FlashTool_Format(IntPtr C1B3D32C, ref DC0F78AE dc0F78AE_0, ref _10843D3A C736FB14);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_Format_Partition(IntPtr intptr_1, byte[] byte_0, ref _0400A61C D682FBAD);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern IntPtr FlashTool_GetBootResult(ref IntPtr FB1E26AD);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_GetDLHandle(IntPtr intptr_5, ref IntPtr intptr_6);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_GetIMEI_PID_SWV_Info(IntPtr intptr_1, ref B0A4EC14 b0A4EC14_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int FlashTool_GetReservedRomSize(IntPtr intptr_1, IntPtr C3B0B892, ref ulong ulong_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_IsConnectWithBootRom(IntPtr A81B653F, ref bool E22DA8BD);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_MemoryTest(IntPtr intptr_1, ref F9B9578D f9B9578D_0, ref _9A34C712 B029DF31);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int FlashTool_NandUtil_Connect(byte C385E914, ref _1B168808 _1B168808_0, ref _31956C37 _31956C37_0, ref int int_0, ref IntPtr intptr_1);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_ReadFlashMemory(IntPtr B695BE2C, ref C7BBB58C c7BBB58C_0, ref _172B3509 _172B3509_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int FlashTool_ReadPartitionCount(IntPtr A010628D, ref uint uint_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int FlashTool_ReadPartitionInfo(IntPtr intptr_1, IntPtr intptr_2, uint D2BAF808);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_Readback(IntPtr intptr_1, ref _932F52AC _932F52AC_0, ref A385AEA5 a385AEA5_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_RomGetCount(ref IntPtr intptr_1, ref IntPtr ********, uint D5B45C86, bool bool_0 = false);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int FlashTool_RomGetInfoAll(IntPtr intptr_1, IntPtr EA9C1E01, ref _9510799D _9510799D_0, uint uint_0, bool bool_0 = false);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int FlashTool_SetUFSConfig(IntPtr A1A38EB7, ref _63A2481B AE8F9B08);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_SetupUSBDL(ref IntPtr intptr_1, byte byte_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int FlashTool_WriteFlashMemory(IntPtr intptr_1, ref D09F3E0E d09F3E0E_0, bool bool_0 = false);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        public static extern int GetCOMPortWithFilter(ref _5B80A500 _5B80A500_0, ref _369BDF9D B4BC691F, ref int int_0, double double_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int GetIncrementCOMPortWithFilter(ref _979F6D31 F791E9B0, ref EB30BFB0 E5A7F3BD, ref IntPtr intptr_1, bool bool_0, ref int BD26CD15, double double_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern string GetNandFlashNameByTypeId(ushort ushort_1);

        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int GetSpecialCOMPortWithFilter(ref _939493B3 _939493B3_0, int EC121032, ref _192AFD94 _192AFD94_0, IntPtr B911BF9A, double B822300E);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int RB_ClearAll(IntPtr intptr_1);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int SCERT_Create(ref IntPtr intptr_1);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int SCERT_Destroy(ref IntPtr CC9A30A6);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int SCERT_Load(IntPtr intptr_1, byte[] byte_0);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int SCERT_Unload(IntPtr A6B65FA9);


        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern IntPtr StatusToString(int B605198D);




        //[SecurityCritical]
        //[HandleProcessCorruptedStateExceptions]
        //public static void smethod_2()
        //{
        //    string eD98EC = A439B98F.B887F816.********[1133](B209F7AD._14A12009, "\\bin\\lib\\FlashtoollibEx.dll");
        //    string eD98EC2 = A439B98F.B887F816.********[1133](B209F7AD._14A12009, "\\bin\\lib\\FlashToolLib.v1.dll");
        //    string eD98EC3 = A439B98F.B887F816.********[1133](B209F7AD._14A12009, "\\bin\\lib\\FlashToolLib.dll");
        //    string eD98EC4 = A439B98F.B887F816.********[1133](B209F7AD._14A12009, "\\bin\\lib\\Lib\\SLA_Challenge.dll");
        //    if (A439B98F.B887F816.********[413](_12131084, A439B98F.B887F816.********[1042]()))
        //    {
        //        _12131084 = E09DE21A.LoadLibrary(eD98EC2);
        //    }
        //    if (A439B98F.B887F816.********[413](_77937313, A439B98F.B887F816.********[1042]()))
        //    {
        //        _77937313 = E09DE21A.LoadLibrary(eD98EC);
        //    }
        //    if (A439B98F.B887F816.********[413](_2628EAA8, A439B98F.B887F816.********[1042]()))
        //    {
        //        _2628EAA8 = E09DE21A.LoadLibrary(eD98EC4);
        //    }
        //    if (A439B98F.B887F816.********[413](_7E8B6EBF, A439B98F.B887F816.********[1042]()))
        //    {
        //        _7E8B6EBF = E09DE21A.LoadLibrary(eD98EC3);
        //    }
        //    int num = -1;
        //    num = DL_Create(ref A2B8C703);
        //    if (num != 0)
        //    {
        //        string string_ = _6B243FB0.F935FF0F(num);
        //        string_ = _6B243FB0.EB250D26(string_, num);
        //    }
        //}







    }
}

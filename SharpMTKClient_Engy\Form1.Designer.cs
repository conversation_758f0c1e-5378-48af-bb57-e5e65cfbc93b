﻿namespace SharpMTKClient_Engy
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.txtScatter = new System.Windows.Forms.TextBox();
            this.txtDa = new System.Windows.Forms.TextBox();
            this.txtAuth = new System.Windows.Forms.TextBox();
            this.button1 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.button3 = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.button4 = new System.Windows.Forms.Button();
            this.btnReadPartitions = new System.Windows.Forms.Button();
            this.btnRebootMeta = new System.Windows.Forms.Button();
            this.btnAdvancedOps = new System.Windows.Forms.Button();
            this.btnErasePartition = new System.Windows.Forms.Button();
            this.btnUnlockBootloader = new System.Windows.Forms.Button();
            this.btnRelockBootloader = new System.Windows.Forms.Button();
            this.btnNVOperations = new System.Windows.Forms.Button();
            this.btnRPMBOperations = new System.Windows.Forms.Button();
            this.btnGPTOperations = new System.Windows.Forms.Button();
            this.btnRawFirmware = new System.Windows.Forms.Button();
            this.btnPreloaderOps = new System.Windows.Forms.Button();
            this.btnFlashModes = new System.Windows.Forms.Button();
            this.timer_updateStatus = new System.Windows.Forms.Timer(this.components);
            this.RomList_FilesFromScatter = new System.Windows.Forms.DataGridView();
            this.CheckPartition = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.NamePartition = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.RegionPartition = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.BeginAddrPartition = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.EndAddrPartition = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.FilePartition = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.FlashModeSelector = new System.Windows.Forms.ComboBox();
            this.progressBar1 = new System.Windows.Forms.ProgressBar();
            this.label4 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.RomList_FilesFromScatter)).BeginInit();
            this.SuspendLayout();
            // 
            // txtScatter
            // 
            this.txtScatter.Location = new System.Drawing.Point(104, 22);
            this.txtScatter.Name = "txtScatter";
            this.txtScatter.Size = new System.Drawing.Size(442, 20);
            this.txtScatter.TabIndex = 0;
            // 
            // txtDa
            // 
            this.txtDa.Location = new System.Drawing.Point(104, 48);
            this.txtDa.Name = "txtDa";
            this.txtDa.Size = new System.Drawing.Size(442, 20);
            this.txtDa.TabIndex = 0;
            // 
            // txtAuth
            // 
            this.txtAuth.Location = new System.Drawing.Point(104, 74);
            this.txtAuth.Name = "txtAuth";
            this.txtAuth.Size = new System.Drawing.Size(442, 20);
            this.txtAuth.TabIndex = 0;
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(552, 20);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 1;
            this.button1.Text = "...";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(552, 45);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(75, 23);
            this.button2.TabIndex = 1;
            this.button2.Text = "...";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(552, 71);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(75, 23);
            this.button3.TabIndex = 1;
            this.button3.Text = "...";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(63, 13);
            this.label1.TabIndex = 2;
            this.label1.Text = "Scatter File:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 51);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(89, 13);
            this.label2.TabIndex = 2;
            this.label2.Text = "Download Agent:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 77);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(51, 13);
            this.label3.TabIndex = 2;
            this.label3.Text = "Auth File:";
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(655, 19);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(121, 23);
            this.button4.TabIndex = 1;
            this.button4.Text = "Download";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // btnReadPartitions
            // 
            this.btnReadPartitions.Location = new System.Drawing.Point(655, 72);
            this.btnReadPartitions.Name = "btnReadPartitions";
            this.btnReadPartitions.Size = new System.Drawing.Size(121, 23);
            this.btnReadPartitions.TabIndex = 7;
            this.btnReadPartitions.Text = "Read Partitions";
            this.btnReadPartitions.UseVisualStyleBackColor = true;
            this.btnReadPartitions.Click += new System.EventHandler(this.btnReadPartitions_Click);
            // 
            // btnRebootMeta
            //
            this.btnRebootMeta.Location = new System.Drawing.Point(655, 101);
            this.btnRebootMeta.Name = "btnRebootMeta";
            this.btnRebootMeta.Size = new System.Drawing.Size(121, 23);
            this.btnRebootMeta.TabIndex = 8;
            this.btnRebootMeta.Text = "Reboot to Meta";
            this.btnRebootMeta.UseVisualStyleBackColor = true;
            this.btnRebootMeta.Click += new System.EventHandler(this.btnRebootMeta_Click);
            //
            // btnAdvancedOps
            //
            this.btnAdvancedOps.Location = new System.Drawing.Point(920, 19);
            this.btnAdvancedOps.Name = "btnAdvancedOps";
            this.btnAdvancedOps.Size = new System.Drawing.Size(121, 23);
            this.btnAdvancedOps.TabIndex = 9;
            this.btnAdvancedOps.Text = "Advanced Ops";
            this.btnAdvancedOps.UseVisualStyleBackColor = true;
            this.btnAdvancedOps.Click += new System.EventHandler(this.btnAdvancedOps_Click);
            //
            // btnErasePartition
            //
            this.btnErasePartition.Location = new System.Drawing.Point(782, 72);
            this.btnErasePartition.Name = "btnErasePartition";
            this.btnErasePartition.Size = new System.Drawing.Size(121, 23);
            this.btnErasePartition.TabIndex = 10;
            this.btnErasePartition.Text = "Erase Partition";
            this.btnErasePartition.UseVisualStyleBackColor = true;
            this.btnErasePartition.Click += new System.EventHandler(this.btnErasePartition_Click);
            //
            // btnUnlockBootloader
            //
            this.btnUnlockBootloader.Location = new System.Drawing.Point(920, 72);
            this.btnUnlockBootloader.Name = "btnUnlockBootloader";
            this.btnUnlockBootloader.Size = new System.Drawing.Size(121, 23);
            this.btnUnlockBootloader.TabIndex = 11;
            this.btnUnlockBootloader.Text = "Unlock Bootloader";
            this.btnUnlockBootloader.UseVisualStyleBackColor = true;
            this.btnUnlockBootloader.Click += new System.EventHandler(this.btnUnlockBootloader_Click);
            //
            // btnRelockBootloader
            //
            this.btnRelockBootloader.Location = new System.Drawing.Point(1060, 19);
            this.btnRelockBootloader.Name = "btnRelockBootloader";
            this.btnRelockBootloader.Size = new System.Drawing.Size(121, 23);
            this.btnRelockBootloader.TabIndex = 12;
            this.btnRelockBootloader.Text = "Relock Bootloader";
            this.btnRelockBootloader.UseVisualStyleBackColor = true;
            this.btnRelockBootloader.Click += new System.EventHandler(this.btnRelockBootloader_Click);
            //
            // btnNVOperations
            //
            this.btnNVOperations.Location = new System.Drawing.Point(1060, 72);
            this.btnNVOperations.Name = "btnNVOperations";
            this.btnNVOperations.Size = new System.Drawing.Size(121, 23);
            this.btnNVOperations.TabIndex = 13;
            this.btnNVOperations.Text = "NV Operations";
            this.btnNVOperations.UseVisualStyleBackColor = true;
            this.btnNVOperations.Click += new System.EventHandler(this.btnNVOperations_Click);
            //
            // btnRPMBOperations
            //
            this.btnRPMBOperations.Location = new System.Drawing.Point(655, 101);
            this.btnRPMBOperations.Name = "btnRPMBOperations";
            this.btnRPMBOperations.Size = new System.Drawing.Size(121, 23);
            this.btnRPMBOperations.TabIndex = 14;
            this.btnRPMBOperations.Text = "RPMB Operations";
            this.btnRPMBOperations.UseVisualStyleBackColor = true;
            this.btnRPMBOperations.Click += new System.EventHandler(this.btnRPMBOperations_Click);
            //
            // btnGPTOperations
            //
            this.btnGPTOperations.Location = new System.Drawing.Point(782, 101);
            this.btnGPTOperations.Name = "btnGPTOperations";
            this.btnGPTOperations.Size = new System.Drawing.Size(121, 23);
            this.btnGPTOperations.TabIndex = 15;
            this.btnGPTOperations.Text = "GPT Operations";
            this.btnGPTOperations.UseVisualStyleBackColor = true;
            this.btnGPTOperations.Click += new System.EventHandler(this.btnGPTOperations_Click);
            //
            // btnRawFirmware
            //
            this.btnRawFirmware.Location = new System.Drawing.Point(920, 101);
            this.btnRawFirmware.Name = "btnRawFirmware";
            this.btnRawFirmware.Size = new System.Drawing.Size(121, 23);
            this.btnRawFirmware.TabIndex = 16;
            this.btnRawFirmware.Text = "Raw Firmware";
            this.btnRawFirmware.UseVisualStyleBackColor = true;
            this.btnRawFirmware.Click += new System.EventHandler(this.btnRawFirmware_Click);
            //
            // btnPreloaderOps
            //
            this.btnPreloaderOps.Location = new System.Drawing.Point(1060, 101);
            this.btnPreloaderOps.Name = "btnPreloaderOps";
            this.btnPreloaderOps.Size = new System.Drawing.Size(121, 23);
            this.btnPreloaderOps.TabIndex = 17;
            this.btnPreloaderOps.Text = "Preloader Ops";
            this.btnPreloaderOps.UseVisualStyleBackColor = true;
            this.btnPreloaderOps.Click += new System.EventHandler(this.btnPreloaderOps_Click);
            //
            // btnFlashModes
            //
            this.btnFlashModes.Location = new System.Drawing.Point(655, 130);
            this.btnFlashModes.Name = "btnFlashModes";
            this.btnFlashModes.Size = new System.Drawing.Size(121, 23);
            this.btnFlashModes.TabIndex = 18;
            this.btnFlashModes.Text = "Flash Modes";
            this.btnFlashModes.UseVisualStyleBackColor = true;
            this.btnFlashModes.Click += new System.EventHandler(this.btnFlashModes_Click);
            // 
            // RomList_FilesFromScatter
            // 
            this.RomList_FilesFromScatter.AllowUserToAddRows = false;
            this.RomList_FilesFromScatter.AllowUserToDeleteRows = false;
            this.RomList_FilesFromScatter.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.RomList_FilesFromScatter.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.RomList_FilesFromScatter.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.CheckPartition,
            this.NamePartition,
            this.RegionPartition,
            this.BeginAddrPartition,
            this.EndAddrPartition,
            this.FilePartition});
            this.RomList_FilesFromScatter.Location = new System.Drawing.Point(12, 100);
            this.RomList_FilesFromScatter.Name = "RomList_FilesFromScatter";
            this.RomList_FilesFromScatter.RowHeadersVisible = false;
            this.RomList_FilesFromScatter.Size = new System.Drawing.Size(615, 315);
            this.RomList_FilesFromScatter.TabIndex = 3;
            this.RomList_FilesFromScatter.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.RomList_FilesFromScatter_CellContentClick);
            // 
            // CheckPartition
            // 
            this.CheckPartition.HeaderText = "";
            this.CheckPartition.Name = "CheckPartition";
            this.CheckPartition.Width = 5;
            // 
            // NamePartition
            // 
            this.NamePartition.HeaderText = "Name";
            this.NamePartition.Name = "NamePartition";
            this.NamePartition.Width = 60;
            // 
            // RegionPartition
            // 
            this.RegionPartition.HeaderText = "Region";
            this.RegionPartition.Name = "RegionPartition";
            this.RegionPartition.Width = 66;
            // 
            // BeginAddrPartition
            // 
            this.BeginAddrPartition.HeaderText = "BeginAddr";
            this.BeginAddrPartition.Name = "BeginAddrPartition";
            this.BeginAddrPartition.Width = 81;
            // 
            // EndAddrPartition
            // 
            this.EndAddrPartition.HeaderText = "EndAddr";
            this.EndAddrPartition.Name = "EndAddrPartition";
            this.EndAddrPartition.Width = 73;
            // 
            // FilePartition
            // 
            this.FilePartition.HeaderText = "File";
            this.FilePartition.Name = "FilePartition";
            this.FilePartition.Width = 48;
            // 
            // FlashModeSelector
            // 
            this.FlashModeSelector.FormattingEnabled = true;
            this.FlashModeSelector.Items.AddRange(new object[] {
            "Download Only",
            "Firmware Upgrade",
            "Format All + Download"});
            this.FlashModeSelector.Location = new System.Drawing.Point(655, 45);
            this.FlashModeSelector.Name = "FlashModeSelector";
            this.FlashModeSelector.Size = new System.Drawing.Size(121, 21);
            this.FlashModeSelector.TabIndex = 4;
            // 
            // progressBar1
            // 
            this.progressBar1.Location = new System.Drawing.Point(12, 421);
            this.progressBar1.Name = "progressBar1";
            this.progressBar1.Size = new System.Drawing.Size(312, 23);
            this.progressBar1.TabIndex = 5;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(330, 426);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(35, 13);
            this.label4.TabIndex = 6;
            this.label4.Text = "label4";
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 520);
            this.Controls.Add(this.btnFlashModes);
            this.Controls.Add(this.btnPreloaderOps);
            this.Controls.Add(this.btnRawFirmware);
            this.Controls.Add(this.btnGPTOperations);
            this.Controls.Add(this.btnRPMBOperations);
            this.Controls.Add(this.btnNVOperations);
            this.Controls.Add(this.btnRelockBootloader);
            this.Controls.Add(this.btnUnlockBootloader);
            this.Controls.Add(this.btnErasePartition);
            this.Controls.Add(this.btnAdvancedOps);
            this.Controls.Add(this.btnRebootMeta);
            this.Controls.Add(this.btnReadPartitions);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.progressBar1);
            this.Controls.Add(this.FlashModeSelector);
            this.Controls.Add(this.RomList_FilesFromScatter);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.button3);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button4);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.txtAuth);
            this.Controls.Add(this.txtDa);
            this.Controls.Add(this.txtScatter);
            this.Name = "Form1";
            this.Text = "Form1";
            ((System.ComponentModel.ISupportInitialize)(this.RomList_FilesFromScatter)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TextBox txtScatter;
        private System.Windows.Forms.TextBox txtDa;
        private System.Windows.Forms.TextBox txtAuth;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.Timer timer_updateStatus;
        private System.Windows.Forms.DataGridView RomList_FilesFromScatter;
        private System.Windows.Forms.DataGridViewCheckBoxColumn CheckPartition;
        private System.Windows.Forms.DataGridViewTextBoxColumn NamePartition;
        private System.Windows.Forms.DataGridViewTextBoxColumn RegionPartition;
        private System.Windows.Forms.DataGridViewTextBoxColumn BeginAddrPartition;
        private System.Windows.Forms.DataGridViewTextBoxColumn EndAddrPartition;
        private System.Windows.Forms.DataGridViewTextBoxColumn FilePartition;
        private System.Windows.Forms.ComboBox FlashModeSelector;
        private System.Windows.Forms.ProgressBar progressBar1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnReadPartitions;
        private System.Windows.Forms.Button btnRebootMeta;
        private System.Windows.Forms.Button btnAdvancedOps;
        private System.Windows.Forms.Button btnErasePartition;
        private System.Windows.Forms.Button btnUnlockBootloader;
        private System.Windows.Forms.Button btnRelockBootloader;
        private System.Windows.Forms.Button btnNVOperations;
        private System.Windows.Forms.Button btnRPMBOperations;
        private System.Windows.Forms.Button btnGPTOperations;
        private System.Windows.Forms.Button btnRawFirmware;
        private System.Windows.Forms.Button btnPreloaderOps;
        private System.Windows.Forms.Button btnFlashModes;
    }
}


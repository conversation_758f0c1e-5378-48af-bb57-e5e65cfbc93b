﻿using SharpMTKClient_Engy.CSharpMTK_Parsed.Data;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Utility;
using System;
using System.Collections.Generic;
using System.Threading;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed.BL
{
    public class MTKDevice : DeviceCtrl
    {
        public int comPortIndex;

        public int chkSum;

        public int sort;

        public int bootromPort;

        public int preloaderPort;

        public int idx;

        public int getDeviceTimeout;

        public List<MTK_DL.ROM_INFO> rom_info_list = new List<MTK_DL.ROM_INFO>();

        private bool needFirmwarewrite;

        public string logname = "";

        public string DownloadMode = "downloadonly";

        public override void flash()
        {
            bool flag = false;
            MTK_FlashTool mTK_FlashTool = new MTK_FlashTool();
            try
            {
                Console.WriteLine(comPortIndex.ToString() + $" flash in thread name:{Thread.CurrentThread.Name}, id:{Thread.CurrentThread.ManagedThreadId.ToString()}");
                Console.WriteLine(comPortIndex.ToString() + " idx: " + idx + " find device: comPortIndex: " + comPortIndex);
                flag = true;
                mTK_FlashTool.SetLogName($"com={comPortIndex.ToString()}");
                logname = $"com={comPortIndex.ToString()}";
                if (mTK_FlashTool.DAConnect_new(idx, (short)comPortIndex) != 0)
                {
                    return;
                }
                if (FlashGlobal.IsFirmwarewrite)
                {
                    while (true)
                    {
                        if (mTK_FlashTool.DoFfu(out needFirmwarewrite) != 0)
                        {
                            Console.WriteLine(comPortIndex.ToString() + "DoFfu error");
                            return;
                        }
                        if (!needFirmwarewrite)
                        {
                            break;
                        }
                        Console.WriteLine(logname + "firmwarewrite success, now reboot");
                        mTK_FlashTool.DoReboot(idx);
                        mTK_FlashTool.DADisConnect(idx);
                        Thread.Sleep(3000);
                        int num = 200;
                        string text = "";
                        int com_port = 0;
                        while (num-- > 0)
                        {
                            if (ComPortCtrl.getDevicesMtk(out com_port))
                            {
                                Console.WriteLine(deviceName + "getDevicesMtk count:" + num);
                                text = $"{com_port} restart successfully";
                                Console.WriteLine(logname + text);
                                break;
                            }
                            Thread.Sleep(100);
                            text = $"waiting for {comPortIndex} restart";
                            Console.WriteLine(logname + text);
                        }
                        if (num <= 0)
                        {
                            text = $"{comPortIndex} restart failed";
                            Console.WriteLine(logname + text);
                            return;
                        }
                        if (mTK_FlashTool.DAConnect_new(idx, (short)com_port) != 0)
                        {
                            return;
                        }
                    }
                }
                if (FlashGlobal.IsFirmwarewrite)
                {
                    if (mTK_FlashTool.DoWB() != 0)
                    {
                        Console.WriteLine(logname + "DoWB error");
                        return;
                    }
                    Console.WriteLine(logname + "wb success, now reboot");
                    mTK_FlashTool.DoReboot(idx);
                    mTK_FlashTool.DADisConnect(idx);
                    Thread.Sleep(3000);
                    int num2 = 200;
                    string text2 = "";
                    int com_port2 = 0;
                    while (num2-- > 0)
                    {
                        if (ComPortCtrl.getDevicesMtk(out com_port2))
                        {
                            Console.WriteLine(deviceName + "getDevicesMtk count:" + num2);
                            text2 = $"{com_port2} restart successfully";
                            Console.WriteLine(logname + text2);
                            break;
                        }
                        Thread.Sleep(100);
                        text2 = $"waiting for {comPortIndex} restart";
                        Console.WriteLine(logname + text2);
                    }
                    if (num2 <= 0)
                    {
                        text2 = $"{comPortIndex} restart failed";
                        Console.WriteLine(logname + text2);
                        return;
                    }
                    if (mTK_FlashTool.DAConnect_new(idx, (short)com_port2) != 0)
                    {
                        return;
                    }
                }
                Console.WriteLine(logname + " begin Download");
                if (DownloadMode == "Format All + Download")
                {
                    if (mTK_FlashTool.DoFormatAllNew() == 0 && mTK_FlashTool.Download(idx) == 0)
                    {
                        mTK_FlashTool.DoReboot(idx);
                        Console.WriteLine("Format All + Download Done");
                    }
                }
                else if (DownloadMode == "Firmware Upgrade")
                {
                    mTK_FlashTool.DoFormatFRP();
                }
                else
                {
                    //Download Only
                    if (mTK_FlashTool.Download(idx) == 0)
                    {
                        mTK_FlashTool.DoReboot(idx);
                        Console.WriteLine("Download Done");
                    }
                }
                //if (mTK_FlashTool.DoFormatAllNew() == 0 && mTK_FlashTool.Download(idx) == 0 && mTK_FlashTool.WriteATMBootModeFlag(idx, rom_info_list) == 0)
                //{
                //    mTK_FlashTool.DoReboot(idx);
                //    Console.WriteLine("Flash Done");
                //    ////FlashingDevice.UpdateDeviceStatus(deviceName, 1f, "flash done", "success", isDone: true);
                //}
            }
            catch (Exception ex)
            {
                Console.WriteLine("error: " + ex.Message);
                ////FlashingDevice.UpdateDeviceStatus(deviceName, null, ex.Message, "error", isDone: true);
                //Console.WriteLine(ex.Message);
            }
            finally
            {
                if (flag)
                {
                    mTK_FlashTool.DADisConnect(idx);
                }
            }
        }

        public bool getMtkdevice()
        {
            while (!ComPortCtrl.getSpecialDevice(bootromPort, preloaderPort, out comPortIndex))
            {
            }
            //FlashingDevice.flashDeviceList.Add(new Device
            //{
            //    Index = idx,
            //    Name = $"com={comPortIndex.ToString()}",
            //    DeviceCtrl = new MTKDevice(),
            //    StartTime = DateTime.Now
            //});
            Console.WriteLine(comPortIndex.ToString(), "find device: " + comPortIndex);
            return true;
        }

        public bool getMtkdeviceAuto()
        {
            while (!ComPortCtrl.getDevicesMtk(out comPortIndex))
            {
            }
            //FlashingDevice.flashDeviceList.Add(new Device
            //{
            //    Index = idx,
            //    Name = $"com={comPortIndex.ToString()}",
            //    DeviceCtrl = new MTKDevice(),
            //    StartTime = DateTime.Now
            //});
            Console.WriteLine(comPortIndex.ToString(), "find device: " + comPortIndex);
            return true;
        }

        public override string[] getDevice()
        {
            throw new NotImplementedException();
        }

        public override void CheckSha256()
        {
            throw new NotImplementedException();
        }
    }
}

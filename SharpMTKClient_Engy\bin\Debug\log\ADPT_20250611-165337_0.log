[0000001] [16:53:37:848182] [Tid0x0000fd60] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [16:53:37:848182] [Tid0x0000fd60] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [16:53:56:309231] [Tid0x0000fd60] [debug] -->[C2] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000004] [16:53:56:309231] [Tid0x0000fd60] [debug] <--[C2] DL_SetChecksumLevel
[0000005] [16:54:13:317154] [Tid0x0000fd60] [debug] -->[C3] FlashTool_Connect #(api.cpp, line:883)
[0000006] [16:54:13:317154] [Tid0x0000fd60] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000007] [16:54:13:317154] [Tid0x0000fd60] [debug] -->[C4] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000008] [16:54:13:317154] [Tid0x0000fd60] [debug] bCheckScatter: 1
[0000009] [16:54:13:323484] [Tid0x0000fd60] [debug] -->[C5] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000010] [16:54:13:323484] [Tid0x0000fd60] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000011] [16:54:13:323484] [Tid0x0000fd60] [debug] have load scatter already #(api.cpp, line:1943)
[0000012] [16:54:13:323484] [Tid0x0000fd60] [debug] libversion 2 #(api.cpp, line:1960)
[0000013] [16:54:13:323484] [Tid0x0000fd60] [debug] -->[C6] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000014] [16:54:13:770394] [Tid0x0000fd60] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2552)
[0000015] [16:54:13:770394] [Tid0x0000fd60] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000016] [16:54:13:785908] [Tid0x0000fd60] [debug] <--[C6] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000017] [16:54:13:785908] [Tid0x0000fd60] [debug] <--[C5] FlashTool_Connect_BROM_Ex
[0000018] [16:54:13:785908] [Tid0x0000fd60] [debug] <--[C4] FlashTool_Connect_Ex
[0000019] [16:54:13:785908] [Tid0x0000fd60] [debug] <--[C3] FlashTool_Connect

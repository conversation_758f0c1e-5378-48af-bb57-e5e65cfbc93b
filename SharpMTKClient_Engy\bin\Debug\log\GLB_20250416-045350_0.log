[00000001] [04:53:50:167118] [Tid0x0000e898] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [04:53:50:167118] [Tid0x0000e898] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [04:53:50:167118] [Tid0x0000e898] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [04:53:50:167118] [Tid0x0000e898] [info] create new hsession 0x1224a2e0 #(kernel.cpp, line:92)
[00000005] [04:53:50:167118] [Tid0x0000e898] [debug] <--[C3] kernel::create_new_session
[00000006] [04:53:50:167118] [Tid0x0000e898] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [04:53:50:167118] [Tid0x0000e898] [debug] <--[C4] boot_rom::boot_rom
[00000008] [04:53:50:169157] [Tid0x0000c54c] [info] -->[C5] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000009] [04:53:50:174577] [Tid0x0000c54c] [debug] -->[C6] connection::create_session #(connection.cpp, line:43)
[00000010] [04:53:50:174577] [Tid0x0000e898] [debug] -->[C7] device_instance::device_instance #(device_instance.cpp, line:22)
[00000011] [04:53:50:174577] [Tid0x0000c54c] [debug] -->[C8] kernel::create_new_session #(kernel.cpp, line:76)
[00000012] [04:53:50:174577] [Tid0x0000e898] [debug] -->[C9] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000013] [04:53:50:174577] [Tid0x0000c54c] [info] create new hsession 0x1224aa18 #(kernel.cpp, line:92)
[00000014] [04:53:50:174577] [Tid0x0000e898] [debug] <--[C9] device_log_source::device_log_source
[00000015] [04:53:50:174577] [Tid0x0000c54c] [debug] <--[C8] kernel::create_new_session
[00000016] [04:53:50:174577] [Tid0x0000e898] [debug] -->[C10] data_mux::data_mux #(data_mux.cpp, line:10)
[00000017] [04:53:50:174577] [Tid0x0000c54c] [debug] -->[C11] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000018] [04:53:50:174577] [Tid0x0000e898] [debug] <--[C10] data_mux::data_mux
[00000019] [04:53:50:174577] [Tid0x0000c54c] [debug] <--[C11] boot_rom::boot_rom
[00000020] [04:53:50:174577] [Tid0x0000e898] [debug] <--[C7] device_instance::device_instance
[00000021] [04:53:50:174577] [Tid0x0000c54c] [debug] -->[C12] device_instance::device_instance #(device_instance.cpp, line:22)
[00000022] [04:53:50:174577] [Tid0x0000e898] [debug] <--[C2] connection::create_session
[00000023] [04:53:50:174577] [Tid0x0000e898] [info] <--[C1] flashtool_create_session_with_handle
[00000024] [04:53:50:174577] [Tid0x0000c54c] [debug] -->[C13] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000025] [04:53:50:174577] [Tid0x0000c54c] [debug] <--[C13] device_log_source::device_log_source
[00000026] [04:53:50:174577] [Tid0x0000c54c] [debug] -->[C14] data_mux::data_mux #(data_mux.cpp, line:10)
[00000027] [04:53:50:174577] [Tid0x0000c54c] [debug] <--[C14] data_mux::data_mux
[00000028] [04:53:50:174577] [Tid0x0000c54c] [debug] <--[C12] device_instance::device_instance
[00000029] [04:53:50:174577] [Tid0x0000e898] [info] -->[C15] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000030] [04:53:50:174577] [Tid0x0000c54c] [debug] <--[C6] connection::create_session
[00000031] [04:53:50:174577] [Tid0x0000e898] [debug] -->[C16] connection::connect_brom #(connection.cpp, line:94)
[00000032] [04:53:50:174577] [Tid0x0000c54c] [info] <--[C5] flashtool_create_session_with_handle
[00000033] [04:53:50:174577] [Tid0x0000e898] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000034] [04:53:50:174577] [Tid0x0000e898] [debug] -->[C17] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000035] [04:53:50:174577] [Tid0x0000e898] [debug] -->[C18] is_valid_ip #(engine_factory.cpp, line:13)
[00000036] [04:53:50:174577] [Tid0x0000c54c] [info] -->[C19] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000037] [04:53:50:175581] [Tid0x0000c54c] [debug] -->[C20] connection::connect_brom #(connection.cpp, line:94)
[00000038] [04:53:50:175581] [Tid0x0000c54c] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000039] [04:53:50:175581] [Tid0x0000c54c] [debug] -->[C21] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000040] [04:53:50:175581] [Tid0x0000c54c] [debug] -->[C22] is_valid_ip #(engine_factory.cpp, line:13)
[00000041] [04:53:50:175581] [Tid0x0000e898] [debug] <--[C18] is_valid_ip
[00000042] [04:53:50:175581] [Tid0x0000e898] [debug] -->[C23] is_lge_impl #(engine_factory.cpp, line:32)
[00000043] [04:53:50:175581] [Tid0x0000e898] [debug] <--[C23] is_lge_impl
[00000044] [04:53:50:175581] [Tid0x0000c54c] [debug] <--[C22] is_valid_ip
[00000045] [04:53:50:175581] [Tid0x0000e898] [debug] -->[C24] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000046] [04:53:50:175581] [Tid0x0000c54c] [debug] -->[C25] is_lge_impl #(engine_factory.cpp, line:32)
[00000047] [04:53:50:175581] [Tid0x0000c54c] [debug] <--[C25] is_lge_impl
[00000048] [04:53:50:175581] [Tid0x0000c54c] [debug] -->[C26] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000049] [04:53:50:175581] [Tid0x0000e898] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000051] [04:53:50:175581] [Tid0x0000e898] [debug] <--[C24] lib_config_parser::get_value
[00000050] [04:53:50:175581] [Tid0x0000c54c] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000052] [04:53:50:175581] [Tid0x0000e898] [debug] <--[C17] engine_factory::create_transmission_engine
[00000053] [04:53:50:175581] [Tid0x0000c54c] [debug] <--[C26] lib_config_parser::get_value
[00000054] [04:53:50:175581] [Tid0x0000e898] [info] -->[C27] comm_engine::open #(comm_engine.cpp, line:63)
[00000055] [04:53:50:175581] [Tid0x0000c54c] [debug] <--[C21] engine_factory::create_transmission_engine
[00000057] [04:53:50:175581] [Tid0x0000c54c] [info] -->[C28] comm_engine::open #(comm_engine.cpp, line:63)
[00000056] [04:53:50:175581] [Tid0x0000e898] [info] try to open device: COM13 baud rate 115200 #(comm_engine.cpp, line:71)
[00000058] [04:53:50:175581] [Tid0x0000c54c] [info] try to open device: COM13 baud rate 115200 #(comm_engine.cpp, line:71)
[00000059] [04:53:50:176581] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000060] [04:53:50:176581] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000061] [04:53:50:235551] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000062] [04:53:50:235551] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000063] [04:53:50:297821] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000064] [04:53:50:297821] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000065] [04:53:50:361718] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000066] [04:53:50:361718] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000067] [04:53:50:423957] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000068] [04:53:50:423957] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000069] [04:53:50:485141] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000070] [04:53:50:485141] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000071] [04:53:50:546368] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000072] [04:53:50:546368] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000073] [04:53:50:608631] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000074] [04:53:50:608631] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000075] [04:53:50:669934] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000076] [04:53:50:669934] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000077] [04:53:50:711682] [Tid0x0000e898] [info] COM13 open complete. #(comm_engine.cpp, line:168)
[00000078] [04:53:50:711682] [Tid0x0000e898] [info] <--[C27] comm_engine::open
[00000079] [04:53:50:711682] [Tid0x0000e898] [debug] -->[C29] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000080] [04:53:50:711682] [Tid0x0000e898] [debug] <--[C29] boot_rom::set_transfer_channel
[00000081] [04:53:50:711682] [Tid0x0000e898] [debug] -->[C30] boot_rom::connect #(boot_rom.cpp, line:47)
[00000082] [04:53:50:711682] [Tid0x0000e898] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000083] [04:53:50:711682] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000084] [04:53:50:711682] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000085] [04:53:50:711682] [Tid0x0000e898] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000086] [04:53:50:711682] [Tid0x0000e898] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000087] [04:53:50:711682] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000088] [04:53:50:729940] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000089] [04:53:50:729940] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000090] [04:53:50:732039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000091] [04:53:50:732039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000092] [04:53:50:732039] [Tid0x0000e898] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000093] [04:53:50:732039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000094] [04:53:50:732039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000095] [04:53:50:732039] [Tid0x0000e898] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000096] [04:53:50:732039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000097] [04:53:50:732039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000098] [04:53:50:732039] [Tid0x0000e898] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000099] [04:53:50:732039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000100] [04:53:50:732039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000101] [04:53:50:732039] [Tid0x0000e898] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000102] [04:53:50:732039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000103] [04:53:50:733039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000104] [04:53:50:733039] [Tid0x0000e898] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000105] [04:53:50:733039] [Tid0x0000e898] [debug] <--[C30] boot_rom::connect
[00000106] [04:53:50:733039] [Tid0x0000e898] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000107] [04:53:50:733039] [Tid0x0000e898] [debug] -->[C45] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000108] [04:53:50:733039] [Tid0x0000e898] [debug] -->[C46] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000109] [04:53:50:733039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000110] [04:53:50:733039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000111] [04:53:50:733039] [Tid0x0000e898] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000112] [04:53:50:733039] [Tid0x0000e898] [debug] <--[C46] boot_rom::get_preloader_version
[00000113] [04:53:50:733039] [Tid0x0000e898] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000114] [04:53:50:733039] [Tid0x0000e898] [debug] <--[C45] boot_rom_logic::security_verify_connection
[00000115] [04:53:50:733039] [Tid0x0000e898] [debug] <--[C16] connection::connect_brom
[00000116] [04:53:50:733039] [Tid0x0000e898] [info] <--[C15] flashtool_connect_brom
[00000117] [04:53:50:733039] [Tid0x0000e898] [info] -->[C49] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000118] [04:53:50:733039] [Tid0x0000e898] [debug] -->[C50] connection::device_control #(connection.cpp, line:669)
[00000119] [04:53:50:733039] [Tid0x0000e898] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000120] [04:53:50:733039] [Tid0x0000e898] [debug] -->[C51] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000121] [04:53:50:733039] [Tid0x0000e898] [debug] -->[C52] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000122] [04:53:50:733039] [Tid0x0000e898] [info] get chip id  #(boot_rom.cpp, line:114)
[00000123] [04:53:50:733039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000124] [04:53:50:733039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000125] [04:53:50:733039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000126] [04:53:50:733039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000127] [04:53:50:733039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000128] [04:53:50:733039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000129] [04:53:50:733039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000130] [04:53:50:734040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000131] [04:53:50:734040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000132] [04:53:50:734040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000133] [04:53:50:734040] [Tid0x0000e898] [debug] -->[C63] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000134] [04:53:50:734040] [Tid0x0000e898] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000135] [04:53:50:734040] [Tid0x0000e898] [debug] <--[C63] lib_config_parser::get_value
[00000136] [04:53:50:734040] [Tid0x0000e898] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000137] [04:53:50:734040] [Tid0x0000e898] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000138] [04:53:50:734040] [Tid0x0000e898] [debug] <--[C52] boot_rom::get_chip_id
[00000139] [04:53:50:734040] [Tid0x0000e898] [debug] <--[C51] boot_rom::device_control
[00000140] [04:53:50:734040] [Tid0x0000e898] [debug] <--[C50] connection::device_control
[00000141] [04:53:50:734040] [Tid0x0000e898] [info] <--[C49] flashtool_device_control
[00000142] [04:53:50:734040] [Tid0x0000e898] [info] -->[C64] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000143] [04:53:50:734040] [Tid0x0000e898] [debug] -->[C65] connection::device_control #(connection.cpp, line:669)
[00000144] [04:53:50:734040] [Tid0x0000e898] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000145] [04:53:50:734040] [Tid0x0000e898] [debug] -->[C66] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000146] [04:53:50:734040] [Tid0x0000e898] [debug] -->[C67] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000147] [04:53:50:734040] [Tid0x0000e898] [info] get chip id  #(boot_rom.cpp, line:114)
[00000148] [04:53:50:734040] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000149] [04:53:50:734040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000150] [04:53:50:734040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000151] [04:53:50:734040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000152] [04:53:50:734040] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000153] [04:53:50:735040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000154] [04:53:50:735040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000155] [04:53:50:735040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000156] [04:53:50:735040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000157] [04:53:50:735040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000158] [04:53:50:735040] [Tid0x0000e898] [debug] -->[C78] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000159] [04:53:50:735040] [Tid0x0000e898] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000160] [04:53:50:735040] [Tid0x0000e898] [debug] <--[C78] lib_config_parser::get_value
[00000161] [04:53:50:735040] [Tid0x0000e898] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000162] [04:53:50:735040] [Tid0x0000e898] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000163] [04:53:50:735040] [Tid0x0000e898] [debug] <--[C67] boot_rom::get_chip_id
[00000164] [04:53:50:735040] [Tid0x0000e898] [debug] <--[C66] boot_rom::device_control
[00000165] [04:53:50:735040] [Tid0x0000e898] [debug] <--[C65] connection::device_control
[00000166] [04:53:50:735040] [Tid0x0000e898] [info] <--[C64] flashtool_device_control
[00000167] [04:53:50:735040] [Tid0x0000e898] [debug] -->[C79] flashtool_get_com_handle #(undocument_api.cpp, line:126)
[00000168] [04:53:50:735040] [Tid0x0000e898] [debug] <--[C79] flashtool_get_com_handle
[00000169] [04:53:50:736039] [Tid0x0000e898] [info] -->[C80] flashtool_connect_da #(flashtoolex_api.cpp, line:134)
[00000170] [04:53:50:736039] [Tid0x0000e898] [debug] -->[C81] connection::connect_da #(connection.cpp, line:258)
[00000171] [04:53:50:736039] [Tid0x0000e898] [info] (1/7)connecting DA. #(connection.cpp, line:261)
[00000172] [04:53:50:736039] [Tid0x0000e898] [info] (2/7)read DA config. #(connection.cpp, line:262)
[00000173] [04:53:50:736039] [Tid0x0000e898] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\device.cfg.xml #(da_config_parser.cpp, line:34)
[00000174] [04:53:50:736039] [Tid0x0000e898] [info] (3/7)read DA image file. #(connection.cpp, line:270)
[00000175] [04:53:50:736039] [Tid0x0000e898] [debug] -->[C82] da_image::load #(da_image.cpp, line:24)
[00000176] [04:53:50:736039] [Tid0x0000e898] [info] local file: C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin #(file_data_factory.cpp, line:22)
[00000177] [04:53:50:736039] [Tid0x0000e898] [debug] <--[C82] da_image::load
[00000178] [04:53:50:737040] [Tid0x0000e898] [info] (4/7)connecting 1st DA. #(connection.cpp, line:296)
[00000179] [04:53:50:737040] [Tid0x0000e898] [debug] -->[C83] connection::connect_1st_da #(connection.cpp, line:358)
[00000180] [04:53:50:737040] [Tid0x0000e898] [debug] -->[C84] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000181] [04:53:50:737040] [Tid0x0000e898] [info] get chip id  #(boot_rom.cpp, line:114)
[00000182] [04:53:50:737040] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000183] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000184] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000185] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000186] [04:53:50:737040] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000187] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000188] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000189] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000190] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000191] [04:53:50:737040] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000192] [04:53:50:737040] [Tid0x0000e898] [debug] -->[C95] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000193] [04:53:50:738039] [Tid0x0000e898] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000194] [04:53:50:738039] [Tid0x0000e898] [debug] <--[C95] lib_config_parser::get_value
[00000195] [04:53:50:738039] [Tid0x0000e898] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000196] [04:53:50:738039] [Tid0x0000e898] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000197] [04:53:50:738039] [Tid0x0000e898] [debug] <--[C84] boot_rom::get_chip_id
[00000198] [04:53:50:738039] [Tid0x0000e898] [info] select DA use chip id. #(connection.cpp, line:379)
[00000199] [04:53:50:738039] [Tid0x0000e898] [debug] -->[C96] da_image::select #(da_image.cpp, line:59)
[00000200] [04:53:50:738039] [Tid0x0000e898] [info] search DA: MT6765 #(da_image.cpp, line:65)
[00000201] [04:53:50:738039] [Tid0x0000e898] [info] da_description: MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327 #(da_image.cpp, line:67)
[00000202] [04:53:50:738039] [Tid0x0000e898] [info] use DA index: 0x0 #(da_parser_v04.cpp, line:29)
[00000203] [04:53:50:738039] [Tid0x0000e898] [debug] <--[C96] da_image::select
[00000204] [04:53:50:738039] [Tid0x0000e898] [info] get 1st DA data. #(connection.cpp, line:391)
[00000205] [04:53:50:738039] [Tid0x0000e898] [debug] -->[C97] da_image::get_section_data #(da_image.cpp, line:94)
[00000206] [04:53:50:738039] [Tid0x0000e898] [debug] <--[C97] da_image::get_section_data
[00000207] [04:53:50:738039] [Tid0x0000e898] [info] send 1st DA data to loader. #(connection.cpp, line:398)
[00000208] [04:53:50:738039] [Tid0x0000e898] [debug] -->[C98] boot_rom::send_da #(boot_rom.cpp, line:248)
[00000209] [04:53:50:738039] [Tid0x0000e898] [info] send DA data to boot rom.  #(boot_rom.cpp, line:249)
[00000210] [04:53:50:738039] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[d7 ]
[00000211] [04:53:50:738039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[d7 ]
[00000212] [04:53:50:738039] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 20 00 00 ]
[00000213] [04:53:50:738039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000004 Hex[00 20 00 00 ]
[00000214] [04:53:50:738039] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 03 9b 88 ]
[00000215] [04:53:50:738039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000004 Hex[00 03 9b 88 ]
[00000216] [04:53:50:738039] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 00 01 00 ]
[00000217] [04:53:50:738039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000004 Hex[00 00 01 00 ]
[00000218] [04:53:50:738039] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000219] [04:53:50:741547] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[ff ff ff ea cc 14 00 fb 00 00 0f e1 c0 10 a0 e3 ]
[00000220] [04:53:50:745548] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[03 f0 45 fa 25 e0 00 21 19 60 d3 e9 02 45 de e9 ]
[00000221] [04:53:50:748547] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[51 ec 30 0b fc f7 88 ef 80 46 89 46 fe f7 c4 f8 ]
[00000222] [04:53:50:752060] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[1b 88 e3 80 fc f7 c8 f8 02 28 03 d8 17 48 21 88 ]
[00000223] [04:53:50:755060] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[09 4a 00 93 b3 23 fd f7 6a fa fe e7 02 b0 10 bd ]
[00000224] [04:53:50:758060] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[4b 43 94 f8 32 11 59 43 fb f7 41 fa f8 f7 c4 f8 ]
[00000225] [04:53:50:762566] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[f7 b5 05 46 90 f8 4d 70 0c 46 08 46 4f f4 3c 72 ]
[00000226] [04:53:50:765566] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[28 46 62 65 23 66 63 4b 63 66 08 eb 01 03 83 f8 ]
[00000227] [04:53:50:768566] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[31 30 64 23 e3 61 06 e0 94 f8 31 30 02 2b 18 bf ]
[00000228] [04:53:50:772614] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[f2 60 1c b1 01 21 0a 46 0b 46 02 e0 01 22 21 46 ]
[00000229] [04:53:50:775614] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[90 45 1b d1 25 e0 3d 4a 00 23 99 45 08 bf 90 45 ]
[00000230] [04:53:50:779620] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[03 62 28 9b 1a 43 ad 4b 42 ea 07 42 07 f0 cf fc ]
[00000231] [04:53:50:782801] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[00 22 4f f0 00 43 09 04 01 f5 20 21 b0 31 05 f0 ]
[00000232] [04:53:50:785809] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[c6 49 4f f0 33 32 4f f0 77 33 03 f0 9f fc 20 46 ]
[00000233] [04:53:50:789801] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[13 46 78 49 01 f0 a2 fc 20 46 7b 49 4f f4 18 42 ]
[00000234] [04:53:50:790954] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000235] [04:53:50:790954] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000236] [04:53:50:792946] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[c0 21 34 31 ff f7 a2 fc 61 78 20 46 2a 46 03 23 ]
[00000237] [04:53:50:795946] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[0a f1 a8 03 ac 35 01 93 0a f1 ac 0a d9 f8 04 30 ]
[00000238] [04:53:50:799952] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[20 46 29 46 9d f8 0c 20 ff f7 1a fb e3 78 23 44 ]
[00000239] [04:53:50:803107] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[f9 f7 7a ff 20 46 84 21 19 22 f9 f7 75 ff 20 46 ]
[00000240] [04:53:50:806107] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[d2 b2 1a 43 f7 f7 98 fc 9d f8 94 20 9d f8 9b e0 ]
[00000241] [04:53:50:809609] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[4c 14 06 00 04 00 06 00 58 00 06 00 00 00 38 80 ]
[00000242] [04:53:50:812612] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[dd e9 06 23 cd e9 08 23 15 e0 01 22 30 46 8d e8 ]
[00000243] [04:53:50:816611] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[43 54 29 0a 00 62 61 74 74 65 72 79 5f 65 78 69 ]
[00000244] [04:53:50:819612] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[79 5d 6d 65 6d 63 70 79 20 74 6f 74 61 6c 20 73 ]
[00000245] [04:53:50:822697] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[20 20 20 20 3d 20 30 78 25 78 0a 00 52 65 67 5b ]
[00000246] [04:53:50:826697] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[44 52 20 69 73 20 69 6e 20 73 65 6c 66 2d 72 65 ]
[00000247] [04:53:50:830209] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[4c 50 33 20 66 6f 72 20 52 4b 25 64 0a 00 6d 69 ]
[00000248] [04:53:50:833210] [Tid0x0000e898] [debug] 			Tx->: 0x00002000 Hex[49 4f 43 4b 20 6a 69 74 74 65 72 20 6d 65 74 65 ]
[00000249] [04:53:50:836217] [Tid0x0000e898] [debug] 			Tx->: 0x00001b88 Hex[54 52 4c 30 20 69 6e 66 6f 72 6d 61 74 69 6f 6e ]
[00000250] [04:53:50:853240] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000251] [04:53:50:853240] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000252] [04:53:50:855244] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[74 34 ]
[00000253] [04:53:50:859743] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000254] [04:53:50:859743] [Tid0x0000e898] [debug] <--[C98] boot_rom::send_da
[00000255] [04:53:50:859743] [Tid0x0000e898] [info] jump to 1st DA. #(connection.cpp, line:405)
[00000256] [04:53:50:859743] [Tid0x0000e898] [debug] -->[C139] boot_rom::jump_to_da #(boot_rom.cpp, line:349)
[00000257] [04:53:50:859743] [Tid0x0000e898] [debug] -->[C140] boot_rom::jump_to #(boot_rom.cpp, line:396)
[00000258] [04:53:50:859743] [Tid0x0000e898] [info] boot rom jump to. #(boot_rom.cpp, line:397)
[00000259] [04:53:50:859743] [Tid0x0000e898] [debug] 			Tx->: 0x00000001 Hex[d5 ]
[00000260] [04:53:50:859743] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[d5 ]
[00000261] [04:53:50:859743] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 20 00 00 ]
[00000262] [04:53:50:859743] [Tid0x0000e898] [debug] 			<-Rx: 0x00000004 Hex[00 20 00 00 ]
[00000263] [04:53:50:859743] [Tid0x0000e898] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000264] [04:53:50:859743] [Tid0x0000e898] [debug] <--[C140] boot_rom::jump_to
[00000265] [04:53:50:859743] [Tid0x0000e898] [info] Begin receive DA 1st sync char #(boot_rom.cpp, line:357)
[00000266] [04:53:50:913907] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000267] [04:53:50:913907] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000268] [04:53:50:947429] [Tid0x0000e898] [debug] 			<-Rx: 0x00000001 Hex[c0 ]
[00000269] [04:53:50:947429] [Tid0x0000e898] [debug] <--[C139] boot_rom::jump_to_da
[00000270] [04:53:50:947429] [Tid0x0000e898] [debug] -->[C147] device_instance::reset #(device_instance.cpp, line:45)
[00000271] [04:53:50:947429] [Tid0x0000e898] [info] -->[C148] data_mux::stop #(data_mux.cpp, line:92)
[00000272] [04:53:50:947429] [Tid0x0000e898] [info] <--[C148] data_mux::stop
[00000273] [04:53:50:947429] [Tid0x0000e898] [debug] worker_thr id: 9044
[00000274] [04:53:50:947429] [Tid0x0000e898] [debug] monitor_thr id: 9d8c
[00000275] [04:53:50:947429] [Tid0x0000e898] [debug] -->[C149] device_log_source::reset #(device_log_source.cpp, line:17)
[00000276] [04:53:50:947429] [Tid0x0000e898] [info] -->[C150] device_log_source::stop #(device_log_source.cpp, line:29)
[00000277] [04:53:50:947429] [Tid0x0000e898] [info] <--[C150] device_log_source::stop
[00000278] [04:53:50:947429] [Tid0x0000e898] [debug] <--[C149] device_log_source::reset
[00000279] [04:53:50:948428] [Tid0x0000e898] [debug] <--[C147] device_instance::reset
[00000280] [04:53:50:948428] [Tid0x00009044] [info] -->[C151] data_mux::worker #(data_mux.cpp, line:163)
[00000281] [04:53:50:948428] [Tid0x0000e898] [info] Start send SYNC signal.  #(device_instance.cpp, line:116) #(device_instance.cpp, line:116)
[00000283] [04:53:50:948428] [Tid0x00009d8c] [info] -->[C154] data_mux::monitor #(data_mux.cpp, line:121)
[00000282] [04:53:50:948428] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000284] [04:53:50:948428] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[53 59 4e 43 ]
[00000285] [04:53:50:948428] [Tid0x0000e898] [info] setup device environment #(connection.cpp, line:420)
[00000286] [04:53:50:948428] [Tid0x0000e898] [info] setting da_log_level & da_log_channel & forbidden ufs provision by UI #(connection.cpp, line:423)
[00000287] [04:53:50:948428] [Tid0x0000e898] [info] -->[C156] device_instance::setup_device_environment #(device_instance.cpp, line:156)
[00000288] [04:53:50:948428] [Tid0x0000e898] [info] /CMD/: SPECIAL_CMD_SETUP_ENVIRONMENT=0x10100 #(device_instance.cpp, line:160)
[00000289] [04:53:50:948428] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000290] [04:53:50:948428] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 01 01 00 ]
[00000291] [04:53:50:948428] [Tid0x0000e898] [info] struct: (uint)da_log_level, (uint)log_channel, (uint)system_os, (uint)reserve2.bit[0][1]-ufs_provision, (uint)reserve3. #(device_instance.cpp, line:164)
[00000292] [04:53:50:948428] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 14 00 00 00 ]
[00000293] [04:53:50:948428] [Tid0x0000e898] [debug] 			Tx->: 0x00000014 Hex[02 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 ]
[00000294] [04:53:50:948428] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000295] [04:53:50:949932] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000296] [04:53:50:949932] [Tid0x0000e898] [info] [result]: STATUS_OK #(device_instance.cpp, line:192)
[00000297] [04:53:50:949932] [Tid0x0000e898] [info] <--[C156] device_instance::setup_device_environment
[00000298] [04:53:50:949932] [Tid0x0000e898] [info] get special device init parameters. #(connection.cpp, line:438)
[00000299] [04:53:50:949932] [Tid0x0000e898] [info] No device parameter config file. skip. #(dev_param_config_parser.cpp, line:109)
[00000300] [04:53:50:976534] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000301] [04:53:50:976534] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000302] [04:53:51:039867] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000303] [04:53:51:039867] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000304] [04:53:51:102561] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000305] [04:53:51:102561] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000306] [04:53:51:164721] [Tid0x0000e898] [info] setup special device init parameters. #(connection.cpp, line:448)
[00000307] [04:53:51:164721] [Tid0x0000e898] [info] -->[C163] device_instance::setup_device_init_parameters #(device_instance.cpp, line:198)
[00000308] [04:53:51:164721] [Tid0x0000e898] [info] /CMD/: SPECIAL_CMD_SETUP_HW_INIT_PARAMS=0x10101 #(device_instance.cpp, line:202)
[00000309] [04:53:51:164721] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000311] [04:53:51:164721] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000310] [04:53:51:164721] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000312] [04:53:51:164721] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[01 01 01 00 ]
[00000313] [04:53:51:164721] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000314] [04:53:51:164721] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000315] [04:53:51:165723] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000316] [04:53:51:166178] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000317] [04:53:51:166178] [Tid0x0000e898] [info] [result]: STATUS_OK #(device_instance.cpp, line:233)
[00000318] [04:53:51:166178] [Tid0x0000e898] [info] <--[C163] device_instance::setup_device_init_parameters
[00000319] [04:53:51:166178] [Tid0x0000e898] [info] wait for 1st DA stable sync signal. #(connection.cpp, line:456)
[00000320] [04:53:51:166178] [Tid0x0000e898] [info] Receive SYNC signal. #(device_instance.cpp, line:126)
[00000321] [04:53:51:225851] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000322] [04:53:51:225851] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000323] [04:53:51:287632] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000324] [04:53:51:287632] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000325] [04:53:51:303644] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000326] [04:53:51:304644] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[53 59 4e 43 ]
[00000327] [04:53:51:304644] [Tid0x0000e898] [debug] <--[C83] connection::connect_1st_da
[00000328] [04:53:51:304644] [Tid0x0000e898] [info] (5/7)send DA some parameters. #(connection.cpp, line:304)
[00000329] [04:53:51:304644] [Tid0x0000e898] [debug] -->[C172] connection::set_da_ctrls #(connection.cpp, line:463)
[00000330] [04:53:51:304644] [Tid0x0000e898] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000331] [04:53:51:304644] [Tid0x0000e898] [info] /control-code/: CC_GET_EXPIRE_DATE=0x40011 #(device_instance.cpp, line:350)
[00000332] [04:53:51:304644] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000333] [04:53:51:304644] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000334] [04:53:51:304644] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000335] [04:53:51:304644] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000336] [04:53:51:304644] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000337] [04:53:51:305644] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[11 00 04 00 ]
[00000338] [04:53:51:305644] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000339] [04:53:51:305644] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[04 00 01 c0 ]
[00000340] [04:53:51:305644] [Tid0x0000e898] [warning] device control code not support. #(device_instance.cpp, line:371)
[00000341] [04:53:51:305644] [Tid0x0000e898] [info] DA expired date: 2099.1.1 #(connection.cpp, line:494)
[00000342] [04:53:51:305644] [Tid0x0000e898] [info] send reset key setting: 0x0  ##default[0x0]. 1 key[0x50]. 2 key[0x68] #(connection.cpp, line:501)
[00000343] [04:53:51:305644] [Tid0x0000e898] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000344] [04:53:51:305644] [Tid0x0000e898] [info] /control-code/: CC_SET_RESET_KEY=0x20004 #(device_instance.cpp, line:350)
[00000345] [04:53:51:305644] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000346] [04:53:51:305644] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000347] [04:53:51:305644] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000348] [04:53:51:306644] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000349] [04:53:51:306644] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000350] [04:53:51:306644] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[04 00 02 00 ]
[00000351] [04:53:51:306644] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000352] [04:53:51:306644] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000353] [04:53:51:306644] [Tid0x0000e898] [info] device support this control code. #(device_instance.cpp, line:375)
[00000354] [04:53:51:306644] [Tid0x0000e898] [info] send parameters. #(device_instance.cpp, line:379)
[00000355] [04:53:51:306644] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000356] [04:53:51:306644] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000357] [04:53:51:306644] [Tid0x0000e898] [info] receive response. #(device_instance.cpp, line:389)
[00000358] [04:53:51:307644] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000359] [04:53:51:307644] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000360] [04:53:51:308644] [Tid0x0000e898] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000361] [04:53:51:308644] [Tid0x0000e898] [info] send battery setting: 0x0 ##battery[0x0]. USB power[0x1]. auto[0x2] #(connection.cpp, line:510)
[00000362] [04:53:51:308644] [Tid0x0000e898] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000363] [04:53:51:308644] [Tid0x0000e898] [info] /control-code/: CC_SET_BATTERY_OPT=0x20002 #(device_instance.cpp, line:350)
[00000364] [04:53:51:308644] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000365] [04:53:51:308644] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000366] [04:53:51:308644] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000367] [04:53:51:308644] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000368] [04:53:51:308644] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000369] [04:53:51:308644] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[02 00 02 00 ]
[00000370] [04:53:51:309645] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000371] [04:53:51:309645] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000372] [04:53:51:309645] [Tid0x0000e898] [info] device support this control code. #(device_instance.cpp, line:375)
[00000373] [04:53:51:309645] [Tid0x0000e898] [info] send parameters. #(device_instance.cpp, line:379)
[00000374] [04:53:51:309645] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000375] [04:53:51:309645] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000376] [04:53:51:309645] [Tid0x0000e898] [info] receive response. #(device_instance.cpp, line:389)
[00000377] [04:53:51:349609] [Tid0x0000c54c] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000378] [04:53:51:349609] [Tid0x0000c54c] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000379] [04:53:51:397682] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000380] [04:53:51:398682] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000381] [04:53:51:398682] [Tid0x0000e898] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000382] [04:53:51:398682] [Tid0x0000e898] [info] send checksum level setting: 0x3 ##none[0x0]. USB[0x1]. storage[0x2], both[0x3] #(connection.cpp, line:519)
[00000383] [04:53:51:398682] [Tid0x0000e898] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000384] [04:53:51:398682] [Tid0x0000e898] [info] /control-code/: CC_SET_CHECKSUM_LEVEL=0x20003 #(device_instance.cpp, line:350)
[00000385] [04:53:51:398682] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000386] [04:53:51:398682] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000387] [04:53:51:398682] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000388] [04:53:51:398682] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000389] [04:53:51:399682] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000390] [04:53:51:399682] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[03 00 02 00 ]
[00000391] [04:53:51:399682] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000392] [04:53:51:399682] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000393] [04:53:51:399682] [Tid0x0000e898] [info] device support this control code. #(device_instance.cpp, line:375)
[00000394] [04:53:51:399682] [Tid0x0000e898] [info] send parameters. #(device_instance.cpp, line:379)
[00000395] [04:53:51:399682] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000396] [04:53:51:399682] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[03 00 00 00 ]
[00000397] [04:53:51:399682] [Tid0x0000e898] [info] receive response. #(device_instance.cpp, line:389)
[00000398] [04:53:51:400867] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000399] [04:53:51:400867] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000400] [04:53:51:400867] [Tid0x0000e898] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000401] [04:53:51:400867] [Tid0x0000e898] [debug] <--[C172] connection::set_da_ctrls
[00000402] [04:53:51:400867] [Tid0x0000e898] [info] Check whether need to enter 2nd DA, need_2nd_da: 1 #(connection.cpp, line:323)
[00000403] [04:53:51:400867] [Tid0x0000e898] [info] (6/7)eanble DRAM #(connection.cpp, line:329)
[00000404] [04:53:51:400867] [Tid0x0000e898] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000405] [04:53:51:400867] [Tid0x0000e898] [info] /control-code/: CC_GET_CONNECTION_AGENT=0x4000a #(device_instance.cpp, line:350)
[00000406] [04:53:51:400867] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000407] [04:53:51:400867] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000408] [04:53:51:401879] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000409] [04:53:51:401879] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000410] [04:53:51:401879] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000411] [04:53:51:401879] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[0a 00 04 00 ]
[00000412] [04:53:51:401879] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000413] [04:53:51:401879] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000414] [04:53:51:401879] [Tid0x0000e898] [info] device support this control code. #(device_instance.cpp, line:375)
[00000415] [04:53:51:401879] [Tid0x0000e898] [info] receive results. #(device_instance.cpp, line:385)
[00000416] [04:53:51:402870] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 09 00 00 00 ]
[00000417] [04:53:51:403870] [Tid0x00009044] [debug] 			<-Rx: 0x00000009 Hex[70 72 65 6c 6f 61 64 65 72 ]
[00000418] [04:53:51:403870] [Tid0x0000e898] [info] receive response. #(device_instance.cpp, line:389)
[00000419] [04:53:51:403870] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000420] [04:53:51:403870] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000421] [04:53:51:403870] [Tid0x0000e898] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000422] [04:53:51:403870] [Tid0x0000e898] [info] preloader alive. skip initializing external DRAM. #(connection.cpp, line:216)
[00000423] [04:53:51:403870] [Tid0x0000e898] [info] (7/7)connecting 2nd DA. #(connection.cpp, line:337)
[00000424] [04:53:51:403870] [Tid0x0000e898] [debug] -->[C234] connection::connect_2nd_da #(connection.cpp, line:617)
[00000425] [04:53:51:403870] [Tid0x0000e898] [info] get 2nd DA data #(connection.cpp, line:629)
[00000426] [04:53:51:403870] [Tid0x0000e898] [debug] -->[C235] da_image::get_section_data #(da_image.cpp, line:94)
[00000427] [04:53:51:403870] [Tid0x0000e898] [debug] <--[C235] da_image::get_section_data
[00000428] [04:53:51:403870] [Tid0x0000e898] [info] jump to 2nd DA #(connection.cpp, line:637)
[00000429] [04:53:51:403870] [Tid0x0000e898] [debug] -->[C236] device_instance::boot_to #(device_instance.cpp, line:456)
[00000430] [04:53:51:403870] [Tid0x0000e898] [info] /CMD/: CMD_BOOT_TO=0x10008 #(device_instance.cpp, line:462)
[00000431] [04:53:51:403870] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000432] [04:53:51:403870] [Tid0x0000e898] [debug] 			Tx->: 0x00000004 Hex[08 00 01 00 ]
[00000433] [04:53:51:403870] [Tid0x0000e898] [info] receive response for command check. #(device_instance.cpp, line:466)
[00000434] [04:53:51:403870] [Tid0x00009044] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000435] [04:53:51:403870] [Tid0x00009044] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000436] [04:53:51:404870] [Tid0x0000e898] [info] send: at_address[0x40000000], length[0x4c0dc] #(device_instance.cpp, line:480)
[00000437] [04:53:51:404870] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 10 00 00 00 ]
[00000438] [04:53:51:404870] [Tid0x0000e898] [debug] 			Tx->: 0x00000010 Hex[00 00 00 40 00 00 00 00 dc c0 04 00 00 00 00 00 ]
[00000439] [04:53:51:404870] [Tid0x0000e898] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 dc c0 04 00 ]
[00000440] [04:53:51:411055] [Tid0x0000c54c] [info] <--[C28] comm_engine::open
[00000441] [04:53:51:411055] [Tid0x0000c54c] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000442] [04:53:51:411055] [Tid0x0000c54c] [debug] <--[C20] connection::connect_brom
[00000443] [04:53:51:411055] [Tid0x0000c54c] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000444] [04:53:51:411055] [Tid0x0000c54c] [info] <--[C19] flashtool_connect_brom
[00000445] [04:53:51:411055] [Tid0x0000c54c] [info] -->[C245] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000446] [04:53:51:411055] [Tid0x0000c54c] [debug] -->[C247] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000447] [04:53:51:411055] [Tid0x0000c54c] [info] -->[C248] device_log_source::stop #(device_log_source.cpp, line:29)
[00000448] [04:53:51:411055] [Tid0x0000c54c] [info] <--[C248] device_log_source::stop
[00000449] [04:53:51:411055] [Tid0x0000c54c] [info] -->[C249] data_mux::stop #(data_mux.cpp, line:92)
[00000450] [04:53:51:411055] [Tid0x0000c54c] [info] <--[C249] data_mux::stop
[00000451] [04:53:51:411055] [Tid0x0000c54c] [debug] <--[C247] device_instance::~device_instance
[00000452] [04:53:51:411055] [Tid0x0000c54c] [info] -->[C250] comm_engine::close #(comm_engine.cpp, line:382)
[00000453] [04:53:51:411055] [Tid0x0000c54c] [debug] -->[C251] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000454] [04:53:51:411055] [Tid0x0000c54c] [debug] <--[C251] comm_engine::cancel
[00000455] [04:53:51:411055] [Tid0x0000c54c] [info] <--[C250] comm_engine::close
[00000456] [04:53:51:411055] [Tid0x0000c54c] [info] delete hsession 0x1224aa18 #(kernel.cpp, line:102)
[00000457] [04:53:51:411055] [Tid0x0000c54c] [info] <--[C245] flashtool_destroy_session
[00000458] [04:53:51:412060] [Tid0x0000c54c] [info] -->[C252] flashtool_shutdown_device #(flashtoolex_api.cpp, line:149)
[00000459] [04:53:51:412060] [Tid0x0000c54c] [debug] -->[C253] connection::shutdown_device #(connection.cpp, line:989)
[00000460] [04:53:51:412060] [Tid0x0000c54c] [error] invalid session. #(connection.cpp, line:994)
[00000461] [04:53:51:412060] [Tid0x0000c54c] [debug] <--[C253] connection::shutdown_device
[00000462] [04:53:51:412060] [Tid0x0000c54c] [error] <ERR_CHECKPOINT>[811][error][0xc001000a]</ERR_CHECKPOINT>flashtool_shutdown_device fail #(flashtoolex_api.cpp, line:154)
[00000463] [04:53:51:412060] [Tid0x0000c54c] [info] <--[C252] flashtool_shutdown_device
[00000464] [04:53:51:412060] [Tid0x0000c54c] [info] -->[C254] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000465] [04:53:51:412060] [Tid0x0000c54c] [info] <--[C254] flashtool_destroy_session

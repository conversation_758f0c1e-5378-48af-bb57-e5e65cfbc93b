[0000001] [13:25:09:948529] [Tid0x0000a6fc] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [13:25:09:949533] [Tid0x0000a6fc] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [13:25:32:459228] [Tid0x0000a6fc] [debug] -->[C2] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000004] [13:25:32:459228] [Tid0x0000a6fc] [debug] <--[C2] DL_SetChecksumLevel
[0000005] [13:25:32:747077] [Tid0x0000a6fc] [debug] -->[C3] FlashTool_Connect #(api.cpp, line:883)
[0000006] [13:25:32:747077] [Tid0x0000a6fc] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000007] [13:25:32:747077] [Tid0x0000a6fc] [debug] -->[C4] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000008] [13:25:32:747077] [Tid0x0000a6fc] [debug] bCheckScatter: 1
[0000009] [13:25:32:753447] [Tid0x0000a6fc] [debug] -->[C5] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000010] [13:25:32:753447] [Tid0x0000a6fc] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000011] [13:25:32:753447] [Tid0x0000a6fc] [debug] have load scatter already #(api.cpp, line:1943)
[0000012] [13:25:32:753447] [Tid0x0000a6fc] [debug] libversion 2 #(api.cpp, line:1960)
[0000013] [13:25:32:753447] [Tid0x0000a6fc] [debug] -->[C6] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000014] [13:25:33:208836] [Tid0x0000a6fc] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2554)
[0000015] [13:25:33:208836] [Tid0x0000a6fc] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000016] [13:25:33:212342] [Tid0x0000a6fc] [debug] <--[C6] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000017] [13:25:33:212342] [Tid0x0000a6fc] [debug] <--[C5] FlashTool_Connect_BROM_Ex
[0000018] [13:25:33:212342] [Tid0x0000a6fc] [debug] <--[C4] FlashTool_Connect_Ex
[0000019] [13:25:33:212342] [Tid0x0000a6fc] [debug] <--[C3] FlashTool_Connect

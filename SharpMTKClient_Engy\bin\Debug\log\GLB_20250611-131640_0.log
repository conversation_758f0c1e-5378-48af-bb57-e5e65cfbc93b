[00000001] [13:16:40:731562] [Tid0x0000a268] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [13:16:40:731562] [Tid0x0000a268] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [13:16:40:731562] [Tid0x0000a268] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [13:16:40:731562] [Tid0x0000a268] [info] create new hsession 0x82d2748 #(kernel.cpp, line:92)
[00000005] [13:16:40:731562] [Tid0x0000a268] [debug] <--[C3] kernel::create_new_session
[00000006] [13:16:40:731562] [Tid0x0000a268] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [13:16:40:732561] [Tid0x0000a268] [debug] <--[C4] boot_rom::boot_rom
[00000008] [13:16:40:738519] [Tid0x0000a268] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [13:16:40:738519] [Tid0x0000a268] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [13:16:40:738519] [Tid0x0000a268] [debug] <--[C6] device_log_source::device_log_source
[00000011] [13:16:40:738519] [Tid0x0000a268] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [13:16:40:738519] [Tid0x0000a268] [debug] <--[C7] data_mux::data_mux
[00000013] [13:16:40:738519] [Tid0x0000a268] [debug] <--[C5] device_instance::device_instance
[00000014] [13:16:40:738519] [Tid0x0000a268] [debug] <--[C2] connection::create_session
[00000015] [13:16:40:738519] [Tid0x0000a268] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [13:16:40:738519] [Tid0x0000a268] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [13:16:40:738519] [Tid0x0000a268] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [13:16:40:738519] [Tid0x0000a268] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [13:16:40:738519] [Tid0x0000a268] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [13:16:40:738519] [Tid0x0000a268] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [13:16:40:739522] [Tid0x0000a268] [debug] <--[C11] is_valid_ip
[00000022] [13:16:40:739522] [Tid0x0000a268] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [13:16:40:739522] [Tid0x0000a268] [debug] <--[C12] is_lge_impl
[00000024] [13:16:40:739522] [Tid0x0000a268] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [13:16:40:739522] [Tid0x0000a268] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [13:16:40:739522] [Tid0x0000a268] [debug] <--[C13] lib_config_parser::get_value
[00000027] [13:16:40:739522] [Tid0x0000a268] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [13:16:40:739522] [Tid0x0000a268] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [13:16:40:739522] [Tid0x0000a268] [info] try to open device: COM5 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [13:16:41:160553] [Tid0x0000a268] [info] COM5 open complete. #(comm_engine.cpp, line:168)
[00000031] [13:16:41:160553] [Tid0x0000a268] [info] <--[C14] comm_engine::open
[00000032] [13:16:41:160553] [Tid0x0000a268] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000033] [13:16:41:160553] [Tid0x0000a268] [debug] <--[C15] boot_rom::set_transfer_channel
[00000034] [13:16:41:160553] [Tid0x0000a268] [debug] -->[C16] boot_rom::connect #(boot_rom.cpp, line:47)
[00000035] [13:16:41:160553] [Tid0x0000a268] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000036] [13:16:41:160553] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000037] [13:16:41:181072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000038] [13:16:41:181072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000039] [13:16:41:181072] [Tid0x0000a268] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000040] [13:16:41:181072] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000041] [13:16:41:181072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000042] [13:16:41:181072] [Tid0x0000a268] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000043] [13:16:41:181072] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000044] [13:16:41:181072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000045] [13:16:41:181072] [Tid0x0000a268] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000046] [13:16:41:181072] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000047] [13:16:41:181072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000048] [13:16:41:181072] [Tid0x0000a268] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000049] [13:16:41:181072] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000050] [13:16:41:181072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000051] [13:16:41:182072] [Tid0x0000a268] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000052] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C16] boot_rom::connect
[00000053] [13:16:41:182072] [Tid0x0000a268] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000054] [13:16:41:182072] [Tid0x0000a268] [debug] -->[C28] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000055] [13:16:41:182072] [Tid0x0000a268] [debug] -->[C29] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000056] [13:16:41:182072] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000057] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000058] [13:16:41:182072] [Tid0x0000a268] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000059] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C29] boot_rom::get_preloader_version
[00000060] [13:16:41:182072] [Tid0x0000a268] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000061] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C28] boot_rom_logic::security_verify_connection
[00000062] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C9] connection::connect_brom
[00000063] [13:16:41:182072] [Tid0x0000a268] [info] <--[C8] flashtool_connect_brom
[00000064] [13:16:41:182072] [Tid0x0000a268] [info] -->[C32] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000065] [13:16:41:182072] [Tid0x0000a268] [debug] -->[C33] connection::device_control #(connection.cpp, line:669)
[00000066] [13:16:41:182072] [Tid0x0000a268] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000067] [13:16:41:182072] [Tid0x0000a268] [debug] -->[C34] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000068] [13:16:41:182072] [Tid0x0000a268] [debug] -->[C35] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000069] [13:16:41:182072] [Tid0x0000a268] [info] get chip id  #(boot_rom.cpp, line:114)
[00000070] [13:16:41:182072] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000071] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000072] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000073] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000074] [13:16:41:182072] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000075] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000076] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000077] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000078] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000079] [13:16:41:182072] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000080] [13:16:41:182072] [Tid0x0000a268] [debug] -->[C46] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000081] [13:16:41:182072] [Tid0x0000a268] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000082] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C46] lib_config_parser::get_value
[00000083] [13:16:41:182072] [Tid0x0000a268] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000084] [13:16:41:182072] [Tid0x0000a268] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000085] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C35] boot_rom::get_chip_id
[00000086] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C34] boot_rom::device_control
[00000087] [13:16:41:182072] [Tid0x0000a268] [debug] <--[C33] connection::device_control
[00000088] [13:16:41:182072] [Tid0x0000a268] [info] <--[C32] flashtool_device_control
[00000089] [13:16:41:182072] [Tid0x0000a268] [info] -->[C47] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000090] [13:16:41:183574] [Tid0x0000a268] [debug] -->[C48] connection::device_control #(connection.cpp, line:669)
[00000091] [13:16:41:183574] [Tid0x0000a268] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000092] [13:16:41:183574] [Tid0x0000a268] [debug] -->[C49] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000093] [13:16:41:183574] [Tid0x0000a268] [debug] -->[C50] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000094] [13:16:41:183574] [Tid0x0000a268] [info] get chip id  #(boot_rom.cpp, line:114)
[00000095] [13:16:41:183574] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000096] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000097] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000098] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000099] [13:16:41:183574] [Tid0x0000a268] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000100] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000101] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000102] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000103] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000104] [13:16:41:183574] [Tid0x0000a268] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000105] [13:16:41:183574] [Tid0x0000a268] [debug] -->[C61] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000106] [13:16:41:183574] [Tid0x0000a268] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000107] [13:16:41:183574] [Tid0x0000a268] [debug] <--[C61] lib_config_parser::get_value
[00000108] [13:16:41:183574] [Tid0x0000a268] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000109] [13:16:41:183574] [Tid0x0000a268] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000110] [13:16:41:183574] [Tid0x0000a268] [debug] <--[C50] boot_rom::get_chip_id
[00000111] [13:16:41:183574] [Tid0x0000a268] [debug] <--[C49] boot_rom::device_control
[00000112] [13:16:41:183574] [Tid0x0000a268] [debug] <--[C48] connection::device_control
[00000113] [13:16:41:183574] [Tid0x0000a268] [info] <--[C47] flashtool_device_control
[00000114] [13:16:41:184576] [Tid0x0000a268] [error] Not Support platform: , type:  #(chip_mapping.cpp, line:229)
[00000115] [13:16:41:184576] [Tid0x0000a268] [info] -->[C62] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000116] [13:16:41:184576] [Tid0x0000a268] [debug] -->[C64] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000117] [13:16:41:184576] [Tid0x0000a268] [info] -->[C65] device_log_source::stop #(device_log_source.cpp, line:29)
[00000118] [13:16:41:184576] [Tid0x0000a268] [info] <--[C65] device_log_source::stop
[00000119] [13:16:41:184576] [Tid0x0000a268] [info] -->[C66] data_mux::stop #(data_mux.cpp, line:92)
[00000120] [13:16:41:184576] [Tid0x0000a268] [info] <--[C66] data_mux::stop
[00000121] [13:16:41:184576] [Tid0x0000a268] [debug] <--[C64] device_instance::~device_instance
[00000122] [13:16:41:184576] [Tid0x0000a268] [info] -->[C67] comm_engine::close #(comm_engine.cpp, line:382)
[00000123] [13:16:41:184576] [Tid0x0000a268] [debug] -->[C68] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000124] [13:16:41:184576] [Tid0x0000a268] [debug] <--[C68] comm_engine::cancel
[00000125] [13:16:41:196098] [Tid0x0000a268] [info] <--[C67] comm_engine::close
[00000126] [13:16:41:196098] [Tid0x0000a268] [info] delete hsession 0x82d2748 #(kernel.cpp, line:102)
[00000127] [13:16:41:196098] [Tid0x0000a268] [info] <--[C62] flashtool_destroy_session
[00000128] [13:18:41:368153] [Tid0x000064ac] [info] -->[C69] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000129] [13:18:41:368153] [Tid0x000064ac] [debug] -->[C70] connection::create_session #(connection.cpp, line:43)
[00000130] [13:18:41:368153] [Tid0x000064ac] [debug] -->[C71] kernel::create_new_session #(kernel.cpp, line:76)
[00000131] [13:18:41:368153] [Tid0x000064ac] [info] create new hsession 0x11dda2e0 #(kernel.cpp, line:92)
[00000132] [13:18:41:368153] [Tid0x000064ac] [debug] <--[C71] kernel::create_new_session
[00000133] [13:18:41:368153] [Tid0x000064ac] [debug] -->[C72] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000134] [13:18:41:368153] [Tid0x000064ac] [debug] <--[C72] boot_rom::boot_rom
[00000135] [13:18:41:368153] [Tid0x000064ac] [debug] -->[C73] device_instance::device_instance #(device_instance.cpp, line:22)
[00000136] [13:18:41:368153] [Tid0x000064ac] [debug] -->[C74] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000137] [13:18:41:368153] [Tid0x000064ac] [debug] <--[C74] device_log_source::device_log_source
[00000138] [13:18:41:368153] [Tid0x000064ac] [debug] -->[C75] data_mux::data_mux #(data_mux.cpp, line:10)
[00000139] [13:18:41:368153] [Tid0x000064ac] [debug] <--[C75] data_mux::data_mux
[00000140] [13:18:41:368153] [Tid0x000064ac] [debug] <--[C73] device_instance::device_instance
[00000141] [13:18:41:368153] [Tid0x000064ac] [debug] <--[C70] connection::create_session
[00000142] [13:18:41:368153] [Tid0x000064ac] [info] <--[C69] flashtool_create_session_with_handle
[00000143] [13:18:41:369152] [Tid0x000064ac] [info] -->[C76] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000144] [13:18:41:369152] [Tid0x000064ac] [debug] -->[C77] connection::connect_brom #(connection.cpp, line:94)
[00000145] [13:18:41:369152] [Tid0x000064ac] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000146] [13:18:41:369152] [Tid0x000064ac] [debug] -->[C78] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000147] [13:18:41:369152] [Tid0x000064ac] [debug] -->[C79] is_valid_ip #(engine_factory.cpp, line:13)
[00000148] [13:18:41:369152] [Tid0x000064ac] [debug] <--[C79] is_valid_ip
[00000149] [13:18:41:369152] [Tid0x000064ac] [debug] -->[C80] is_lge_impl #(engine_factory.cpp, line:32)
[00000150] [13:18:41:369152] [Tid0x000064ac] [debug] <--[C80] is_lge_impl
[00000151] [13:18:41:369152] [Tid0x000064ac] [debug] -->[C81] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000152] [13:18:41:370152] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000153] [13:18:41:370152] [Tid0x000064ac] [debug] <--[C81] lib_config_parser::get_value
[00000154] [13:18:41:370152] [Tid0x000064ac] [debug] <--[C78] engine_factory::create_transmission_engine
[00000155] [13:18:41:370152] [Tid0x000064ac] [info] -->[C82] comm_engine::open #(comm_engine.cpp, line:63)
[00000156] [13:18:41:370152] [Tid0x000064ac] [info] try to open device: COM18 baud rate 115200 #(comm_engine.cpp, line:71)
[00000157] [13:18:41:400088] [Tid0x000064ac] [info] COM18 open complete. #(comm_engine.cpp, line:168)
[00000158] [13:18:41:400088] [Tid0x000064ac] [info] <--[C82] comm_engine::open
[00000159] [13:18:41:400088] [Tid0x000064ac] [debug] -->[C83] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000160] [13:18:41:400088] [Tid0x000064ac] [debug] <--[C83] boot_rom::set_transfer_channel
[00000161] [13:18:41:400088] [Tid0x000064ac] [debug] -->[C84] boot_rom::connect #(boot_rom.cpp, line:47)
[00000162] [13:18:41:400088] [Tid0x000064ac] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000163] [13:18:41:400088] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000164] [13:18:41:457609] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000165] [13:18:41:457609] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000166] [13:18:41:464381] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000167] [13:18:41:520158] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000168] [13:18:41:520158] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000169] [13:18:41:520158] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000170] [13:18:41:582193] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000171] [13:18:41:582193] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000172] [13:18:41:582193] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000173] [13:18:41:643086] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000174] [13:18:41:643086] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000175] [13:18:41:643086] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000176] [13:18:41:704179] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000177] [13:18:41:704179] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000178] [13:18:41:704179] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000179] [13:18:41:766117] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000180] [13:18:41:766117] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000181] [13:18:41:766117] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000182] [13:18:41:827566] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000183] [13:18:41:827566] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000184] [13:18:41:827566] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000185] [13:18:41:889312] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000186] [13:18:41:889312] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000187] [13:18:41:889312] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000188] [13:18:41:952288] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000189] [13:18:41:952288] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000190] [13:18:41:952288] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000191] [13:18:42:015458] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000192] [13:18:42:015458] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000193] [13:18:42:015458] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000194] [13:18:42:078450] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000195] [13:18:42:078450] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000196] [13:18:42:078450] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000197] [13:18:42:140321] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000198] [13:18:42:140321] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000199] [13:18:42:140321] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000200] [13:18:42:202226] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000201] [13:18:42:202226] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000202] [13:18:42:203239] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000203] [13:18:42:265507] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000204] [13:18:42:265507] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000205] [13:18:42:265507] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000206] [13:18:42:327572] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000207] [13:18:42:327572] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000208] [13:18:42:327572] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000209] [13:18:42:389361] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000210] [13:18:42:389361] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000211] [13:18:42:389361] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000212] [13:18:42:452287] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000213] [13:18:42:452287] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000214] [13:18:42:452287] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000215] [13:18:42:515371] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000216] [13:18:42:515371] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000217] [13:18:42:515371] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000218] [13:18:42:577123] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000219] [13:18:42:577123] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000220] [13:18:42:577123] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000221] [13:18:42:639239] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000222] [13:18:42:639239] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000223] [13:18:42:639239] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000224] [13:18:42:702307] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000225] [13:18:42:702307] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000226] [13:18:42:702307] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000227] [13:18:42:765399] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000228] [13:18:42:765399] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000229] [13:18:42:765399] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000230] [13:18:42:828128] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000231] [13:18:42:828128] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000232] [13:18:42:829121] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000233] [13:18:42:889324] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000234] [13:18:42:889324] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000235] [13:18:42:890324] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000236] [13:18:42:951226] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000237] [13:18:42:951226] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000238] [13:18:42:951226] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000239] [13:18:43:013483] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000240] [13:18:43:013483] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000241] [13:18:43:013483] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000242] [13:18:43:075794] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000243] [13:18:43:075794] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000244] [13:18:43:075794] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000245] [13:18:43:138487] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000246] [13:18:43:138487] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000247] [13:18:43:138487] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000248] [13:18:43:201049] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000249] [13:18:43:201049] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000250] [13:18:43:201049] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000251] [13:18:43:263205] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000252] [13:18:43:263205] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000253] [13:18:43:263205] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000254] [13:18:43:325881] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000255] [13:18:43:325881] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000256] [13:18:43:325881] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000257] [13:18:43:388552] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000258] [13:18:43:388552] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000259] [13:18:43:389549] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000260] [13:18:43:450675] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000261] [13:18:43:450675] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000262] [13:18:43:450675] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000263] [13:18:43:512219] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000264] [13:18:43:512219] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000265] [13:18:43:512219] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000266] [13:18:43:574840] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000267] [13:18:43:574840] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000268] [13:18:43:574840] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000269] [13:18:43:637531] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000270] [13:18:43:637531] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000271] [13:18:43:637531] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000272] [13:18:43:699733] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000273] [13:18:43:699733] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000274] [13:18:43:699733] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000275] [13:18:43:762342] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000276] [13:18:43:762342] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000277] [13:18:43:762797] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000278] [13:18:43:824412] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000279] [13:18:43:824412] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000280] [13:18:43:824412] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000281] [13:18:43:886000] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000282] [13:18:43:886000] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000283] [13:18:43:886000] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000284] [13:18:43:949425] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000285] [13:18:43:949425] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000286] [13:18:43:949425] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000287] [13:18:44:011491] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000288] [13:18:44:011491] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000289] [13:18:44:011491] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000290] [13:18:44:073427] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000291] [13:18:44:073427] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000292] [13:18:44:073427] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000293] [13:18:44:136092] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000294] [13:18:44:136092] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000295] [13:18:44:136092] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000296] [13:18:44:198198] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000297] [13:18:44:198198] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000298] [13:18:44:198198] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000299] [13:18:44:260815] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000300] [13:18:44:260815] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000301] [13:18:44:260815] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000302] [13:18:44:323835] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000303] [13:18:44:323835] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000304] [13:18:44:323835] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000305] [13:18:44:385039] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000306] [13:18:44:385039] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000307] [13:18:44:385039] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000308] [13:18:44:447437] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000309] [13:18:44:447437] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000310] [13:18:44:447437] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000311] [13:18:44:510358] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000312] [13:18:44:510358] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000313] [13:18:44:510358] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000314] [13:18:44:572401] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000315] [13:18:44:572401] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000316] [13:18:44:572401] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000317] [13:18:44:634263] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000318] [13:18:44:634263] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000319] [13:18:44:634263] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000320] [13:18:44:695879] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000321] [13:18:44:695879] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000322] [13:18:44:695879] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000323] [13:18:44:759472] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000324] [13:18:44:759472] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000325] [13:18:44:760472] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000326] [13:18:44:821566] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000327] [13:18:44:821566] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000328] [13:18:44:822074] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000329] [13:18:44:883406] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000330] [13:18:44:883406] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000331] [13:18:44:883406] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000332] [13:18:44:946220] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000333] [13:18:44:946220] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000334] [13:18:44:946220] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000335] [13:18:45:008606] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000336] [13:18:45:008606] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000337] [13:18:45:008606] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000338] [13:18:45:070638] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000339] [13:18:45:070638] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000340] [13:18:45:070638] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000341] [13:18:45:133341] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000342] [13:18:45:133341] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000343] [13:18:45:133341] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000344] [13:18:45:196479] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000345] [13:18:45:196479] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000346] [13:18:45:197476] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000347] [13:18:45:258082] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000348] [13:18:45:258082] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000349] [13:18:45:259081] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000350] [13:18:45:319790] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000351] [13:18:45:319790] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000352] [13:18:45:319790] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000353] [13:18:45:382298] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000354] [13:18:45:382298] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000355] [13:18:45:382298] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000356] [13:18:45:444093] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000357] [13:18:45:444093] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000358] [13:18:45:444093] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000359] [13:18:45:504688] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000360] [13:18:45:504688] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000361] [13:18:45:504688] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000362] [13:18:45:568141] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000363] [13:18:45:568141] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000364] [13:18:45:568141] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000365] [13:18:45:630239] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000366] [13:18:45:630239] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000367] [13:18:45:630239] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000368] [13:18:45:692811] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000369] [13:18:45:692811] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000370] [13:18:45:692811] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000371] [13:18:45:755079] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000372] [13:18:45:755079] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000373] [13:18:45:755079] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000374] [13:18:45:817390] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000375] [13:18:45:817390] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000376] [13:18:45:817390] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000377] [13:18:45:880522] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000378] [13:18:45:880522] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000379] [13:18:45:880522] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000380] [13:18:45:942715] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000381] [13:18:45:942715] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000382] [13:18:45:942715] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000383] [13:18:46:003915] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000384] [13:18:46:003915] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000385] [13:18:46:003915] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000386] [13:18:46:067087] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000387] [13:18:46:067087] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000388] [13:18:46:067087] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000389] [13:18:46:129099] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000390] [13:18:46:129099] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000391] [13:18:46:129099] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000392] [13:18:46:191432] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000393] [13:18:46:191432] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000394] [13:18:46:191432] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000395] [13:18:46:253663] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000396] [13:18:46:253663] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000397] [13:18:46:253663] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000398] [13:18:46:316270] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000399] [13:18:46:316270] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000400] [13:18:46:316270] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000401] [13:18:46:377667] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000402] [13:18:46:377667] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000403] [13:18:46:377667] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000404] [13:18:46:440282] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000405] [13:18:46:440282] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000406] [13:18:46:441278] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000407] [13:18:46:502363] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000408] [13:18:46:502363] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000409] [13:18:46:502363] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000410] [13:18:46:564155] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000411] [13:18:46:564155] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000412] [13:18:46:564155] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000413] [13:18:46:626410] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000414] [13:18:46:626410] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000415] [13:18:46:626410] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000416] [13:18:46:688058] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000417] [13:18:46:688058] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000418] [13:18:46:688058] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000419] [13:18:46:749690] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000420] [13:18:46:749690] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000421] [13:18:46:749690] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000422] [13:18:46:813396] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000423] [13:18:46:813396] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000424] [13:18:46:813396] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000425] [13:18:46:875474] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000426] [13:18:46:875474] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000427] [13:18:46:875474] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000428] [13:18:46:937014] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000429] [13:18:46:937014] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000430] [13:18:46:937014] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000431] [13:18:46:999058] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000432] [13:18:46:999058] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000433] [13:18:46:999058] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000434] [13:18:47:061248] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000435] [13:18:47:061248] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000436] [13:18:47:061248] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000437] [13:18:47:124561] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000438] [13:18:47:124561] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000439] [13:18:47:124561] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000440] [13:18:47:186268] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000441] [13:18:47:186268] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000442] [13:18:47:186268] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000443] [13:18:47:248859] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000444] [13:18:47:248859] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000445] [13:18:47:248859] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000446] [13:18:47:310823] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000447] [13:18:47:310823] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000448] [13:18:47:310823] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000449] [13:18:47:373248] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000450] [13:18:47:373248] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000451] [13:18:47:373248] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000452] [13:18:47:435653] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000453] [13:18:47:435653] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000454] [13:18:47:435653] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000455] [13:18:47:497759] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000456] [13:18:47:497759] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000457] [13:18:47:497759] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000458] [13:18:47:560268] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000459] [13:18:47:560268] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000460] [13:18:47:560268] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000461] [13:18:47:623294] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000462] [13:18:47:623294] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000463] [13:18:47:623294] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000464] [13:18:47:685507] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000465] [13:18:47:685507] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000466] [13:18:47:685507] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000467] [13:18:47:748480] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000468] [13:18:47:748480] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000469] [13:18:47:748480] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000470] [13:18:47:811046] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000471] [13:18:47:811046] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000472] [13:18:47:811046] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000473] [13:18:47:937232] [Tid0x000064ac] [warning] Rx timeout in 50ms. #(comm_engine.cpp, line:212)
[00000474] [13:18:47:937232] [Tid0x000064ac] [warning] Rx xferd length[0x0] != expected length[0x1] #(comm_engine.cpp, line:242)
[00000475] [13:20:47:956074] [Tid0x000064ac] [warning] @Tx Timeout. #(comm_engine.cpp, line:273)
[00000476] [13:20:47:956074] [Tid0x000064ac] [warning] Tx xferd_write length[0x0] != expected length[0x1] #(comm_engine.cpp, line:339)
[00000477] [13:20:47:956074] [Tid0x000064ac] [error] brom connect exception:  #(boot_rom.cpp, line:103)
[00000478] [13:20:47:956074] [Tid0x000064ac] [error] ./arch/win/comm_engine.cpp(340): Throw in function unsigned int __thiscall comm_engine::send(const unsigned char *,unsigned int,unsigned int)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Tx data incomplete.
 #(boot_rom.cpp, line:104)
[00000479] [13:20:47:956074] [Tid0x000064ac] [debug] <--[C84] boot_rom::connect
[00000480] [13:20:47:956074] [Tid0x000064ac] [debug] <--[C77] connection::connect_brom
[00000481] [13:20:47:956074] [Tid0x000064ac] [error] <ERR_CHECKPOINT>[809][error][0xc0060001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000482] [13:20:47:956074] [Tid0x000064ac] [info] <--[C76] flashtool_connect_brom
[00000483] [13:20:47:956074] [Tid0x000064ac] [info] -->[C294] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000484] [13:20:47:957067] [Tid0x000064ac] [debug] -->[C296] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000485] [13:20:47:957067] [Tid0x000064ac] [info] -->[C297] device_log_source::stop #(device_log_source.cpp, line:29)
[00000486] [13:20:47:957067] [Tid0x000064ac] [info] <--[C297] device_log_source::stop
[00000487] [13:20:47:957067] [Tid0x000064ac] [info] -->[C298] data_mux::stop #(data_mux.cpp, line:92)
[00000488] [13:20:47:957067] [Tid0x000064ac] [info] <--[C298] data_mux::stop
[00000489] [13:20:47:957067] [Tid0x000064ac] [debug] <--[C296] device_instance::~device_instance
[00000490] [13:20:47:957067] [Tid0x000064ac] [info] -->[C299] comm_engine::close #(comm_engine.cpp, line:382)
[00000491] [13:20:47:957067] [Tid0x000064ac] [debug] -->[C300] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000492] [13:20:47:957067] [Tid0x000064ac] [debug] <--[C300] comm_engine::cancel
[00000493] [13:20:47:957067] [Tid0x000064ac] [info] <--[C299] comm_engine::close
[00000494] [13:20:47:957067] [Tid0x000064ac] [info] delete hsession 0x11dda2e0 #(kernel.cpp, line:102)
[00000495] [13:20:47:957067] [Tid0x000064ac] [info] <--[C294] flashtool_destroy_session
[00000496] [13:21:38:325150] [Tid0x000064ac] [info] -->[C301] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000497] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C302] connection::create_session #(connection.cpp, line:43)
[00000498] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C303] kernel::create_new_session #(kernel.cpp, line:76)
[00000499] [13:21:38:325150] [Tid0x000064ac] [info] create new hsession 0x11ddb398 #(kernel.cpp, line:92)
[00000500] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C303] kernel::create_new_session
[00000501] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C304] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000502] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C304] boot_rom::boot_rom
[00000503] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C305] device_instance::device_instance #(device_instance.cpp, line:22)
[00000504] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C306] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000505] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C306] device_log_source::device_log_source
[00000506] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C307] data_mux::data_mux #(data_mux.cpp, line:10)
[00000507] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C307] data_mux::data_mux
[00000508] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C305] device_instance::device_instance
[00000509] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C302] connection::create_session
[00000510] [13:21:38:325150] [Tid0x000064ac] [info] <--[C301] flashtool_create_session_with_handle
[00000511] [13:21:38:325150] [Tid0x000064ac] [info] -->[C308] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000512] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C309] connection::connect_brom #(connection.cpp, line:94)
[00000513] [13:21:38:325150] [Tid0x000064ac] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000514] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C310] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000515] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C311] is_valid_ip #(engine_factory.cpp, line:13)
[00000516] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C311] is_valid_ip
[00000517] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C312] is_lge_impl #(engine_factory.cpp, line:32)
[00000518] [13:21:38:325150] [Tid0x000064ac] [debug] <--[C312] is_lge_impl
[00000519] [13:21:38:325150] [Tid0x000064ac] [debug] -->[C313] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000520] [13:21:38:326152] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000521] [13:21:38:326152] [Tid0x000064ac] [debug] <--[C313] lib_config_parser::get_value
[00000522] [13:21:38:326152] [Tid0x000064ac] [debug] <--[C310] engine_factory::create_transmission_engine
[00000523] [13:21:38:326152] [Tid0x000064ac] [info] -->[C314] comm_engine::open #(comm_engine.cpp, line:63)
[00000524] [13:21:38:326152] [Tid0x000064ac] [info] try to open device: COM5 baud rate 115200 #(comm_engine.cpp, line:71)
[00000525] [13:21:38:769032] [Tid0x000064ac] [info] COM5 open complete. #(comm_engine.cpp, line:168)
[00000526] [13:21:38:769032] [Tid0x000064ac] [info] <--[C314] comm_engine::open
[00000527] [13:21:38:769032] [Tid0x000064ac] [debug] -->[C315] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000528] [13:21:38:769032] [Tid0x000064ac] [debug] <--[C315] boot_rom::set_transfer_channel
[00000529] [13:21:38:769032] [Tid0x000064ac] [debug] -->[C316] boot_rom::connect #(boot_rom.cpp, line:47)
[00000530] [13:21:38:769032] [Tid0x000064ac] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000531] [13:21:38:769032] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000532] [13:21:38:769032] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000533] [13:21:38:769032] [Tid0x000064ac] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000534] [13:21:38:769032] [Tid0x000064ac] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000535] [13:21:38:769032] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000536] [13:21:38:789023] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000537] [13:21:38:789023] [Tid0x000064ac] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000538] [13:21:38:789023] [Tid0x000064ac] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000539] [13:21:38:789023] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000540] [13:21:38:789023] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000541] [13:21:38:789023] [Tid0x000064ac] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000542] [13:21:38:789023] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000543] [13:21:38:789023] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000544] [13:21:38:789023] [Tid0x000064ac] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000545] [13:21:38:789023] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000546] [13:21:38:789023] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000547] [13:21:38:789023] [Tid0x000064ac] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000548] [13:21:38:789023] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000549] [13:21:38:789023] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000550] [13:21:38:789023] [Tid0x000064ac] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000551] [13:21:38:789023] [Tid0x000064ac] [debug] <--[C316] boot_rom::connect
[00000552] [13:21:38:789023] [Tid0x000064ac] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000553] [13:21:38:789023] [Tid0x000064ac] [debug] -->[C331] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000554] [13:21:38:790024] [Tid0x000064ac] [debug] -->[C332] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000555] [13:21:38:790024] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000556] [13:21:38:790024] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000557] [13:21:38:790024] [Tid0x000064ac] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000558] [13:21:38:790024] [Tid0x000064ac] [debug] <--[C332] boot_rom::get_preloader_version
[00000559] [13:21:38:790024] [Tid0x000064ac] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000560] [13:21:38:790024] [Tid0x000064ac] [debug] <--[C331] boot_rom_logic::security_verify_connection
[00000561] [13:21:38:790024] [Tid0x000064ac] [debug] <--[C309] connection::connect_brom
[00000562] [13:21:38:790024] [Tid0x000064ac] [info] <--[C308] flashtool_connect_brom
[00000563] [13:21:38:790024] [Tid0x000064ac] [info] -->[C335] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000564] [13:21:38:790024] [Tid0x000064ac] [debug] -->[C336] connection::device_control #(connection.cpp, line:669)
[00000565] [13:21:38:790024] [Tid0x000064ac] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000566] [13:21:38:790024] [Tid0x000064ac] [debug] -->[C337] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000567] [13:21:38:790024] [Tid0x000064ac] [debug] -->[C338] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000568] [13:21:38:790024] [Tid0x000064ac] [info] get chip id  #(boot_rom.cpp, line:114)
[00000569] [13:21:38:790024] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000570] [13:21:38:790024] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000571] [13:21:38:790024] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000572] [13:21:38:790024] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000573] [13:21:38:790024] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000574] [13:21:38:790024] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000575] [13:21:38:790024] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000576] [13:21:38:790024] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000577] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000578] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000579] [13:21:38:791025] [Tid0x000064ac] [debug] -->[C349] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000580] [13:21:38:791025] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000581] [13:21:38:791025] [Tid0x000064ac] [debug] <--[C349] lib_config_parser::get_value
[00000582] [13:21:38:791025] [Tid0x000064ac] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000583] [13:21:38:791025] [Tid0x000064ac] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000584] [13:21:38:791025] [Tid0x000064ac] [debug] <--[C338] boot_rom::get_chip_id
[00000585] [13:21:38:791025] [Tid0x000064ac] [debug] <--[C337] boot_rom::device_control
[00000586] [13:21:38:791025] [Tid0x000064ac] [debug] <--[C336] connection::device_control
[00000587] [13:21:38:791025] [Tid0x000064ac] [info] <--[C335] flashtool_device_control
[00000588] [13:21:38:791025] [Tid0x000064ac] [info] -->[C350] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000589] [13:21:38:791025] [Tid0x000064ac] [debug] -->[C351] connection::device_control #(connection.cpp, line:669)
[00000590] [13:21:38:791025] [Tid0x000064ac] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000591] [13:21:38:791025] [Tid0x000064ac] [debug] -->[C352] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000592] [13:21:38:791025] [Tid0x000064ac] [debug] -->[C353] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000593] [13:21:38:791025] [Tid0x000064ac] [info] get chip id  #(boot_rom.cpp, line:114)
[00000594] [13:21:38:791025] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000595] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000596] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000597] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000598] [13:21:38:791025] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000599] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000600] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000601] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000602] [13:21:38:791025] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000603] [13:21:38:792022] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000604] [13:21:38:792022] [Tid0x000064ac] [debug] -->[C364] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000605] [13:21:38:792022] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000606] [13:21:38:792022] [Tid0x000064ac] [debug] <--[C364] lib_config_parser::get_value
[00000607] [13:21:38:792022] [Tid0x000064ac] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000608] [13:21:38:792022] [Tid0x000064ac] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000609] [13:21:38:792022] [Tid0x000064ac] [debug] <--[C353] boot_rom::get_chip_id
[00000610] [13:21:38:792022] [Tid0x000064ac] [debug] <--[C352] boot_rom::device_control
[00000611] [13:21:38:792022] [Tid0x000064ac] [debug] <--[C351] connection::device_control
[00000612] [13:21:38:792022] [Tid0x000064ac] [info] <--[C350] flashtool_device_control
[00000613] [13:21:38:792022] [Tid0x000064ac] [error] Not Support platform: , type:  #(chip_mapping.cpp, line:229)
[00000614] [13:21:38:792022] [Tid0x000064ac] [info] -->[C365] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000615] [13:21:38:792022] [Tid0x000064ac] [debug] -->[C367] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000616] [13:21:38:792022] [Tid0x000064ac] [info] -->[C368] device_log_source::stop #(device_log_source.cpp, line:29)
[00000617] [13:21:38:792022] [Tid0x000064ac] [info] <--[C368] device_log_source::stop
[00000618] [13:21:38:792022] [Tid0x000064ac] [info] -->[C369] data_mux::stop #(data_mux.cpp, line:92)
[00000619] [13:21:38:792022] [Tid0x000064ac] [info] <--[C369] data_mux::stop
[00000620] [13:21:38:792022] [Tid0x000064ac] [debug] <--[C367] device_instance::~device_instance
[00000621] [13:21:38:792022] [Tid0x000064ac] [info] -->[C370] comm_engine::close #(comm_engine.cpp, line:382)
[00000622] [13:21:38:792022] [Tid0x000064ac] [debug] -->[C371] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000623] [13:21:38:792022] [Tid0x000064ac] [debug] <--[C371] comm_engine::cancel
[00000624] [13:21:38:797530] [Tid0x000064ac] [info] <--[C370] comm_engine::close
[00000625] [13:21:38:797530] [Tid0x000064ac] [info] delete hsession 0x11ddb398 #(kernel.cpp, line:102)
[00000626] [13:21:38:797530] [Tid0x000064ac] [info] <--[C365] flashtool_destroy_session
[00000627] [13:23:34:640459] [Tid0x000064ac] [info] -->[C372] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000628] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C373] connection::create_session #(connection.cpp, line:43)
[00000629] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C374] kernel::create_new_session #(kernel.cpp, line:76)
[00000630] [13:23:34:640459] [Tid0x000064ac] [info] create new hsession 0x83020f8 #(kernel.cpp, line:92)
[00000631] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C374] kernel::create_new_session
[00000632] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C375] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000633] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C375] boot_rom::boot_rom
[00000634] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C376] device_instance::device_instance #(device_instance.cpp, line:22)
[00000635] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C377] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000636] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C377] device_log_source::device_log_source
[00000637] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C378] data_mux::data_mux #(data_mux.cpp, line:10)
[00000638] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C378] data_mux::data_mux
[00000639] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C376] device_instance::device_instance
[00000640] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C373] connection::create_session
[00000641] [13:23:34:640459] [Tid0x000064ac] [info] <--[C372] flashtool_create_session_with_handle
[00000642] [13:23:34:640459] [Tid0x000064ac] [info] -->[C379] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000643] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C380] connection::connect_brom #(connection.cpp, line:94)
[00000644] [13:23:34:640459] [Tid0x000064ac] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000645] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C381] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000646] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C382] is_valid_ip #(engine_factory.cpp, line:13)
[00000647] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C382] is_valid_ip
[00000648] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C383] is_lge_impl #(engine_factory.cpp, line:32)
[00000649] [13:23:34:640459] [Tid0x000064ac] [debug] <--[C383] is_lge_impl
[00000650] [13:23:34:640459] [Tid0x000064ac] [debug] -->[C384] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000651] [13:23:34:641459] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000652] [13:23:34:641459] [Tid0x000064ac] [debug] <--[C384] lib_config_parser::get_value
[00000653] [13:23:34:641459] [Tid0x000064ac] [debug] <--[C381] engine_factory::create_transmission_engine
[00000654] [13:23:34:641459] [Tid0x000064ac] [info] -->[C385] comm_engine::open #(comm_engine.cpp, line:63)
[00000655] [13:23:34:641459] [Tid0x000064ac] [info] try to open device: COM6 baud rate 115200 #(comm_engine.cpp, line:71)
[00000656] [13:23:34:642459] [Tid0x000064ac] [info] COM6 open complete. #(comm_engine.cpp, line:168)
[00000657] [13:23:34:642459] [Tid0x000064ac] [info] <--[C385] comm_engine::open
[00000658] [13:23:34:642459] [Tid0x000064ac] [debug] -->[C386] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000659] [13:23:34:642459] [Tid0x000064ac] [debug] <--[C386] boot_rom::set_transfer_channel
[00000660] [13:23:34:642459] [Tid0x000064ac] [debug] -->[C387] boot_rom::connect #(boot_rom.cpp, line:47)
[00000661] [13:23:34:642459] [Tid0x000064ac] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000662] [13:23:34:642459] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000663] [13:23:34:642459] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[a0 ]
[00000664] [13:23:34:642459] [Tid0x000064ac] [error] BRom protocol error: ACK 0x5F != 0xA0 #(boot_rom.cpp, line:94)
[00000665] [13:23:34:642459] [Tid0x000064ac] [error] brom connect exception:  #(boot_rom.cpp, line:103)
[00000666] [13:23:34:642459] [Tid0x000064ac] [error] ./brom/boot_rom.cpp(95): Throw in function int __thiscall boot_rom::connect(const struct callbacks_struct_t *)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: BRom pototcol error.
 #(boot_rom.cpp, line:104)
[00000667] [13:23:34:642459] [Tid0x000064ac] [debug] <--[C387] boot_rom::connect
[00000668] [13:23:34:643459] [Tid0x000064ac] [debug] <--[C380] connection::connect_brom
[00000669] [13:23:34:643459] [Tid0x000064ac] [error] <ERR_CHECKPOINT>[809][error][0xc0060001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000670] [13:23:34:643459] [Tid0x000064ac] [info] <--[C379] flashtool_connect_brom
[00000671] [13:23:34:643459] [Tid0x000064ac] [info] -->[C390] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000672] [13:23:34:643459] [Tid0x000064ac] [debug] -->[C392] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000673] [13:23:34:643459] [Tid0x000064ac] [info] -->[C393] device_log_source::stop #(device_log_source.cpp, line:29)
[00000674] [13:23:34:643459] [Tid0x000064ac] [info] <--[C393] device_log_source::stop
[00000675] [13:23:34:643459] [Tid0x000064ac] [info] -->[C394] data_mux::stop #(data_mux.cpp, line:92)
[00000676] [13:23:34:643459] [Tid0x000064ac] [info] <--[C394] data_mux::stop
[00000677] [13:23:34:643459] [Tid0x000064ac] [debug] <--[C392] device_instance::~device_instance
[00000678] [13:23:34:643459] [Tid0x000064ac] [info] -->[C395] comm_engine::close #(comm_engine.cpp, line:382)
[00000679] [13:23:34:643459] [Tid0x000064ac] [debug] -->[C396] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000680] [13:23:34:643459] [Tid0x000064ac] [debug] <--[C396] comm_engine::cancel
[00000681] [13:23:34:652491] [Tid0x000064ac] [info] <--[C395] comm_engine::close
[00000682] [13:23:34:652491] [Tid0x000064ac] [info] delete hsession 0x83020f8 #(kernel.cpp, line:102)
[00000683] [13:23:34:652491] [Tid0x000064ac] [info] <--[C390] flashtool_destroy_session
[00000684] [13:23:53:471173] [Tid0x000064ac] [info] -->[C397] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000685] [13:23:53:471173] [Tid0x000064ac] [debug] -->[C398] connection::create_session #(connection.cpp, line:43)
[00000686] [13:23:53:471173] [Tid0x000064ac] [debug] -->[C399] kernel::create_new_session #(kernel.cpp, line:76)
[00000687] [13:23:53:471173] [Tid0x000064ac] [info] create new hsession 0x8320b58 #(kernel.cpp, line:92)
[00000688] [13:23:53:471173] [Tid0x000064ac] [debug] <--[C399] kernel::create_new_session
[00000689] [13:23:53:471173] [Tid0x000064ac] [debug] -->[C400] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000690] [13:23:53:471173] [Tid0x000064ac] [debug] <--[C400] boot_rom::boot_rom
[00000691] [13:23:53:471173] [Tid0x000064ac] [debug] -->[C401] device_instance::device_instance #(device_instance.cpp, line:22)
[00000692] [13:23:53:471173] [Tid0x000064ac] [debug] -->[C402] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000693] [13:23:53:471173] [Tid0x000064ac] [debug] <--[C402] device_log_source::device_log_source
[00000694] [13:23:53:471173] [Tid0x000064ac] [debug] -->[C403] data_mux::data_mux #(data_mux.cpp, line:10)
[00000695] [13:23:53:471173] [Tid0x000064ac] [debug] <--[C403] data_mux::data_mux
[00000696] [13:23:53:471173] [Tid0x000064ac] [debug] <--[C401] device_instance::device_instance
[00000697] [13:23:53:471173] [Tid0x000064ac] [debug] <--[C398] connection::create_session
[00000698] [13:23:53:472174] [Tid0x000064ac] [info] <--[C397] flashtool_create_session_with_handle
[00000699] [13:23:53:472174] [Tid0x000064ac] [info] -->[C404] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000700] [13:23:53:472174] [Tid0x000064ac] [debug] -->[C405] connection::connect_brom #(connection.cpp, line:94)
[00000701] [13:23:53:472174] [Tid0x000064ac] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000702] [13:23:53:472174] [Tid0x000064ac] [debug] -->[C406] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000703] [13:23:53:472174] [Tid0x000064ac] [debug] -->[C407] is_valid_ip #(engine_factory.cpp, line:13)
[00000704] [13:23:53:472174] [Tid0x000064ac] [debug] <--[C407] is_valid_ip
[00000705] [13:23:53:472174] [Tid0x000064ac] [debug] -->[C408] is_lge_impl #(engine_factory.cpp, line:32)
[00000706] [13:23:53:472174] [Tid0x000064ac] [debug] <--[C408] is_lge_impl
[00000707] [13:23:53:472174] [Tid0x000064ac] [debug] -->[C409] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000708] [13:23:53:472174] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000709] [13:23:53:472174] [Tid0x000064ac] [debug] <--[C409] lib_config_parser::get_value
[00000710] [13:23:53:472174] [Tid0x000064ac] [debug] <--[C406] engine_factory::create_transmission_engine
[00000711] [13:23:53:472174] [Tid0x000064ac] [info] -->[C410] comm_engine::open #(comm_engine.cpp, line:63)
[00000712] [13:23:53:472174] [Tid0x000064ac] [info] try to open device: COM6 baud rate 115200 #(comm_engine.cpp, line:71)
[00000713] [13:23:53:473173] [Tid0x000064ac] [info] COM6 open complete. #(comm_engine.cpp, line:168)
[00000714] [13:23:53:473173] [Tid0x000064ac] [info] <--[C410] comm_engine::open
[00000715] [13:23:53:473173] [Tid0x000064ac] [debug] -->[C411] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000716] [13:23:53:473173] [Tid0x000064ac] [debug] <--[C411] boot_rom::set_transfer_channel
[00000717] [13:23:53:473173] [Tid0x000064ac] [debug] -->[C412] boot_rom::connect #(boot_rom.cpp, line:47)
[00000718] [13:23:53:473173] [Tid0x000064ac] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000719] [13:23:53:474172] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000720] [13:23:53:474172] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[a0 ]
[00000721] [13:23:53:474172] [Tid0x000064ac] [error] BRom protocol error: ACK 0x5F != 0xA0 #(boot_rom.cpp, line:94)
[00000722] [13:23:53:474172] [Tid0x000064ac] [error] brom connect exception:  #(boot_rom.cpp, line:103)
[00000723] [13:23:53:474172] [Tid0x000064ac] [error] ./brom/boot_rom.cpp(95): Throw in function int __thiscall boot_rom::connect(const struct callbacks_struct_t *)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: BRom pototcol error.
 #(boot_rom.cpp, line:104)
[00000724] [13:23:53:474172] [Tid0x000064ac] [debug] <--[C412] boot_rom::connect
[00000725] [13:23:53:474172] [Tid0x000064ac] [debug] <--[C405] connection::connect_brom
[00000726] [13:23:53:474172] [Tid0x000064ac] [error] <ERR_CHECKPOINT>[809][error][0xc0060001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000727] [13:23:53:474172] [Tid0x000064ac] [info] <--[C404] flashtool_connect_brom
[00000728] [13:23:53:474172] [Tid0x000064ac] [info] -->[C415] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000729] [13:23:53:474172] [Tid0x000064ac] [debug] -->[C417] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000730] [13:23:53:474172] [Tid0x000064ac] [info] -->[C418] device_log_source::stop #(device_log_source.cpp, line:29)
[00000731] [13:23:53:474172] [Tid0x000064ac] [info] <--[C418] device_log_source::stop
[00000732] [13:23:53:474172] [Tid0x000064ac] [info] -->[C419] data_mux::stop #(data_mux.cpp, line:92)
[00000733] [13:23:53:474172] [Tid0x000064ac] [info] <--[C419] data_mux::stop
[00000734] [13:23:53:474172] [Tid0x000064ac] [debug] <--[C417] device_instance::~device_instance
[00000735] [13:23:53:474172] [Tid0x000064ac] [info] -->[C420] comm_engine::close #(comm_engine.cpp, line:382)
[00000736] [13:23:53:474172] [Tid0x000064ac] [debug] -->[C421] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000737] [13:23:53:474172] [Tid0x000064ac] [debug] <--[C421] comm_engine::cancel
[00000738] [13:23:53:486183] [Tid0x000064ac] [info] <--[C420] comm_engine::close
[00000739] [13:23:53:486183] [Tid0x000064ac] [info] delete hsession 0x8320b58 #(kernel.cpp, line:102)
[00000740] [13:23:53:486183] [Tid0x000064ac] [info] <--[C415] flashtool_destroy_session
[00000741] [13:24:27:360573] [Tid0x000012f0] [info] -->[C422] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000742] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C423] connection::create_session #(connection.cpp, line:43)
[00000743] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C424] kernel::create_new_session #(kernel.cpp, line:76)
[00000744] [13:24:27:360573] [Tid0x000012f0] [info] create new hsession 0x82e8ee0 #(kernel.cpp, line:92)
[00000745] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C424] kernel::create_new_session
[00000746] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C425] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000747] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C425] boot_rom::boot_rom
[00000748] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C426] device_instance::device_instance #(device_instance.cpp, line:22)
[00000749] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C427] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000750] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C427] device_log_source::device_log_source
[00000751] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C428] data_mux::data_mux #(data_mux.cpp, line:10)
[00000752] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C428] data_mux::data_mux
[00000753] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C426] device_instance::device_instance
[00000754] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C423] connection::create_session
[00000755] [13:24:27:360573] [Tid0x000012f0] [info] <--[C422] flashtool_create_session_with_handle
[00000756] [13:24:27:360573] [Tid0x000012f0] [info] -->[C429] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000757] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C430] connection::connect_brom #(connection.cpp, line:94)
[00000758] [13:24:27:360573] [Tid0x000012f0] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000759] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C431] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000760] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C432] is_valid_ip #(engine_factory.cpp, line:13)
[00000761] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C432] is_valid_ip
[00000762] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C433] is_lge_impl #(engine_factory.cpp, line:32)
[00000763] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C433] is_lge_impl
[00000764] [13:24:27:360573] [Tid0x000012f0] [debug] -->[C434] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000765] [13:24:27:360573] [Tid0x000012f0] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000766] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C434] lib_config_parser::get_value
[00000767] [13:24:27:360573] [Tid0x000012f0] [debug] <--[C431] engine_factory::create_transmission_engine
[00000768] [13:24:27:360573] [Tid0x000012f0] [info] -->[C435] comm_engine::open #(comm_engine.cpp, line:63)
[00000769] [13:24:27:360573] [Tid0x000012f0] [info] try to open device: COM6 baud rate 115200 #(comm_engine.cpp, line:71)
[00000770] [13:24:27:361572] [Tid0x000012f0] [info] COM6 open complete. #(comm_engine.cpp, line:168)
[00000771] [13:24:27:361572] [Tid0x000012f0] [info] <--[C435] comm_engine::open
[00000772] [13:24:27:361572] [Tid0x000012f0] [debug] -->[C436] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000773] [13:24:27:362570] [Tid0x000012f0] [debug] <--[C436] boot_rom::set_transfer_channel
[00000774] [13:24:27:362570] [Tid0x000012f0] [debug] -->[C437] boot_rom::connect #(boot_rom.cpp, line:47)
[00000775] [13:24:27:362570] [Tid0x000012f0] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000776] [13:24:27:362570] [Tid0x000012f0] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000777] [13:24:27:362570] [Tid0x000012f0] [debug] 			<-Rx: 0x00000001 Hex[a0 ]
[00000778] [13:24:27:362570] [Tid0x000012f0] [error] BRom protocol error: ACK 0x5F != 0xA0 #(boot_rom.cpp, line:94)
[00000779] [13:24:27:362570] [Tid0x000012f0] [error] brom connect exception:  #(boot_rom.cpp, line:103)
[00000780] [13:24:27:362570] [Tid0x000012f0] [error] ./brom/boot_rom.cpp(95): Throw in function int __thiscall boot_rom::connect(const struct callbacks_struct_t *)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: BRom pototcol error.
 #(boot_rom.cpp, line:104)
[00000781] [13:24:27:362570] [Tid0x000012f0] [debug] <--[C437] boot_rom::connect
[00000782] [13:24:27:362570] [Tid0x000012f0] [debug] <--[C430] connection::connect_brom
[00000783] [13:24:27:362570] [Tid0x000012f0] [error] <ERR_CHECKPOINT>[809][error][0xc0060001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000784] [13:24:27:362570] [Tid0x000012f0] [info] <--[C429] flashtool_connect_brom
[00000785] [13:24:27:362570] [Tid0x000012f0] [info] -->[C440] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000786] [13:24:27:362570] [Tid0x000012f0] [debug] -->[C442] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000787] [13:24:27:362570] [Tid0x000012f0] [info] -->[C443] device_log_source::stop #(device_log_source.cpp, line:29)
[00000788] [13:24:27:362570] [Tid0x000012f0] [info] <--[C443] device_log_source::stop
[00000789] [13:24:27:362570] [Tid0x000012f0] [info] -->[C444] data_mux::stop #(data_mux.cpp, line:92)
[00000790] [13:24:27:362570] [Tid0x000012f0] [info] <--[C444] data_mux::stop
[00000791] [13:24:27:362570] [Tid0x000012f0] [debug] <--[C442] device_instance::~device_instance
[00000792] [13:24:27:362570] [Tid0x000012f0] [info] -->[C445] comm_engine::close #(comm_engine.cpp, line:382)
[00000793] [13:24:27:362570] [Tid0x000012f0] [debug] -->[C446] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000794] [13:24:27:362570] [Tid0x000012f0] [debug] <--[C446] comm_engine::cancel
[00000795] [13:24:27:379175] [Tid0x000012f0] [info] <--[C445] comm_engine::close
[00000796] [13:24:27:379175] [Tid0x000012f0] [info] delete hsession 0x82e8ee0 #(kernel.cpp, line:102)
[00000797] [13:24:27:379175] [Tid0x000012f0] [info] <--[C440] flashtool_destroy_session
[00000798] [13:24:56:496079] [Tid0x000064ac] [info] -->[C447] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000799] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C448] connection::create_session #(connection.cpp, line:43)
[00000800] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C449] kernel::create_new_session #(kernel.cpp, line:76)
[00000801] [13:24:56:496079] [Tid0x000064ac] [info] create new hsession 0x8394ce8 #(kernel.cpp, line:92)
[00000802] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C449] kernel::create_new_session
[00000803] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C450] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000804] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C450] boot_rom::boot_rom
[00000805] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C451] device_instance::device_instance #(device_instance.cpp, line:22)
[00000806] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C452] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000807] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C452] device_log_source::device_log_source
[00000808] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C453] data_mux::data_mux #(data_mux.cpp, line:10)
[00000809] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C453] data_mux::data_mux
[00000810] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C451] device_instance::device_instance
[00000811] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C448] connection::create_session
[00000812] [13:24:56:496079] [Tid0x000064ac] [info] <--[C447] flashtool_create_session_with_handle
[00000813] [13:24:56:496079] [Tid0x000064ac] [info] -->[C454] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000814] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C455] connection::connect_brom #(connection.cpp, line:94)
[00000815] [13:24:56:496079] [Tid0x000064ac] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000816] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C456] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000817] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C457] is_valid_ip #(engine_factory.cpp, line:13)
[00000818] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C457] is_valid_ip
[00000819] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C458] is_lge_impl #(engine_factory.cpp, line:32)
[00000820] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C458] is_lge_impl
[00000821] [13:24:56:496079] [Tid0x000064ac] [debug] -->[C459] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000822] [13:24:56:496079] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000823] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C459] lib_config_parser::get_value
[00000824] [13:24:56:496079] [Tid0x000064ac] [debug] <--[C456] engine_factory::create_transmission_engine
[00000825] [13:24:56:496079] [Tid0x000064ac] [info] -->[C460] comm_engine::open #(comm_engine.cpp, line:63)
[00000826] [13:24:56:496079] [Tid0x000064ac] [info] try to open device: COM5 baud rate 115200 #(comm_engine.cpp, line:71)
[00000827] [13:24:56:774479] [Tid0x000064ac] [info] COM5 open complete. #(comm_engine.cpp, line:168)
[00000828] [13:24:56:774479] [Tid0x000064ac] [info] <--[C460] comm_engine::open
[00000829] [13:24:56:774479] [Tid0x000064ac] [debug] -->[C461] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000830] [13:24:56:775479] [Tid0x000064ac] [debug] <--[C461] boot_rom::set_transfer_channel
[00000831] [13:24:56:775479] [Tid0x000064ac] [debug] -->[C462] boot_rom::connect #(boot_rom.cpp, line:47)
[00000832] [13:24:56:775479] [Tid0x000064ac] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000833] [13:24:56:775479] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000834] [13:24:56:795087] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000835] [13:24:56:795087] [Tid0x000064ac] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000836] [13:24:56:795087] [Tid0x000064ac] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000837] [13:24:56:795087] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000838] [13:24:56:795591] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000839] [13:24:56:795591] [Tid0x000064ac] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000840] [13:24:56:795591] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000841] [13:24:56:795591] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000842] [13:24:56:795591] [Tid0x000064ac] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000843] [13:24:56:795591] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000844] [13:24:56:795591] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000845] [13:24:56:795591] [Tid0x000064ac] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000846] [13:24:56:795591] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000847] [13:24:56:795591] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000848] [13:24:56:795591] [Tid0x000064ac] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000849] [13:24:56:795591] [Tid0x000064ac] [debug] <--[C462] boot_rom::connect
[00000850] [13:24:56:795591] [Tid0x000064ac] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000851] [13:24:56:795591] [Tid0x000064ac] [debug] -->[C474] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000852] [13:24:56:795591] [Tid0x000064ac] [debug] -->[C475] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000853] [13:24:56:795591] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000854] [13:24:56:795591] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000855] [13:24:56:795591] [Tid0x000064ac] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000856] [13:24:56:795591] [Tid0x000064ac] [debug] <--[C475] boot_rom::get_preloader_version
[00000857] [13:24:56:795591] [Tid0x000064ac] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000858] [13:24:56:795591] [Tid0x000064ac] [debug] <--[C474] boot_rom_logic::security_verify_connection
[00000859] [13:24:56:795591] [Tid0x000064ac] [debug] <--[C455] connection::connect_brom
[00000860] [13:24:56:795591] [Tid0x000064ac] [info] <--[C454] flashtool_connect_brom
[00000861] [13:24:56:795591] [Tid0x000064ac] [info] -->[C478] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000862] [13:24:56:795591] [Tid0x000064ac] [debug] -->[C479] connection::device_control #(connection.cpp, line:669)
[00000863] [13:24:56:796595] [Tid0x000064ac] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000864] [13:24:56:796595] [Tid0x000064ac] [debug] -->[C480] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000865] [13:24:56:796595] [Tid0x000064ac] [debug] -->[C481] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000866] [13:24:56:796595] [Tid0x000064ac] [info] get chip id  #(boot_rom.cpp, line:114)
[00000867] [13:24:56:796595] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000868] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000869] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000870] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000871] [13:24:56:796595] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000872] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000873] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000874] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000875] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000876] [13:24:56:796595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000877] [13:24:56:796595] [Tid0x000064ac] [debug] -->[C492] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000878] [13:24:56:796595] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000879] [13:24:56:796595] [Tid0x000064ac] [debug] <--[C492] lib_config_parser::get_value
[00000880] [13:24:56:796595] [Tid0x000064ac] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000881] [13:24:56:796595] [Tid0x000064ac] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000882] [13:24:56:796595] [Tid0x000064ac] [debug] <--[C481] boot_rom::get_chip_id
[00000883] [13:24:56:796595] [Tid0x000064ac] [debug] <--[C480] boot_rom::device_control
[00000884] [13:24:56:796595] [Tid0x000064ac] [debug] <--[C479] connection::device_control
[00000885] [13:24:56:796595] [Tid0x000064ac] [info] <--[C478] flashtool_device_control
[00000886] [13:24:56:796595] [Tid0x000064ac] [info] -->[C493] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000887] [13:24:56:796595] [Tid0x000064ac] [debug] -->[C494] connection::device_control #(connection.cpp, line:669)
[00000888] [13:24:56:797595] [Tid0x000064ac] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000889] [13:24:56:797595] [Tid0x000064ac] [debug] -->[C495] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000890] [13:24:56:797595] [Tid0x000064ac] [debug] -->[C496] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000891] [13:24:56:797595] [Tid0x000064ac] [info] get chip id  #(boot_rom.cpp, line:114)
[00000892] [13:24:56:797595] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000893] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000894] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000895] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000896] [13:24:56:797595] [Tid0x000064ac] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000897] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000898] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000899] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000900] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000901] [13:24:56:797595] [Tid0x000064ac] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000902] [13:24:56:797595] [Tid0x000064ac] [debug] -->[C507] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000903] [13:24:56:797595] [Tid0x000064ac] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000904] [13:24:56:797595] [Tid0x000064ac] [debug] <--[C507] lib_config_parser::get_value
[00000905] [13:24:56:797595] [Tid0x000064ac] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000906] [13:24:56:797595] [Tid0x000064ac] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000907] [13:24:56:797595] [Tid0x000064ac] [debug] <--[C496] boot_rom::get_chip_id
[00000908] [13:24:56:797595] [Tid0x000064ac] [debug] <--[C495] boot_rom::device_control
[00000909] [13:24:56:797595] [Tid0x000064ac] [debug] <--[C494] connection::device_control
[00000910] [13:24:56:797595] [Tid0x000064ac] [info] <--[C493] flashtool_device_control
[00000911] [13:24:56:797595] [Tid0x000064ac] [error] Not Support platform: , type:  #(chip_mapping.cpp, line:229)
[00000912] [13:24:56:797595] [Tid0x000064ac] [info] -->[C508] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000913] [13:24:56:798595] [Tid0x000064ac] [debug] -->[C510] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000914] [13:24:56:798595] [Tid0x000064ac] [info] -->[C511] device_log_source::stop #(device_log_source.cpp, line:29)
[00000915] [13:24:56:798595] [Tid0x000064ac] [info] <--[C511] device_log_source::stop
[00000916] [13:24:56:798595] [Tid0x000064ac] [info] -->[C512] data_mux::stop #(data_mux.cpp, line:92)
[00000917] [13:24:56:798595] [Tid0x000064ac] [info] <--[C512] data_mux::stop
[00000918] [13:24:56:798595] [Tid0x000064ac] [debug] <--[C510] device_instance::~device_instance
[00000919] [13:24:56:798595] [Tid0x000064ac] [info] -->[C513] comm_engine::close #(comm_engine.cpp, line:382)
[00000920] [13:24:56:798595] [Tid0x000064ac] [debug] -->[C514] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000921] [13:24:56:798595] [Tid0x000064ac] [debug] <--[C514] comm_engine::cancel
[00000922] [13:24:56:815375] [Tid0x000064ac] [info] <--[C513] comm_engine::close
[00000923] [13:24:56:815375] [Tid0x000064ac] [info] delete hsession 0x8394ce8 #(kernel.cpp, line:102)
[00000924] [13:24:56:815375] [Tid0x000064ac] [info] <--[C508] flashtool_destroy_session

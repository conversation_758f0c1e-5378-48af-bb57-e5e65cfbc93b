[00000001] [09:00:54:244192] [Tid0x0000dd94] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [09:00:54:244192] [Tid0x0000dd94] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [09:00:54:244192] [Tid0x0000dd94] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [09:00:54:244192] [Tid0x0000dd94] [info] create new hsession 0xb4ae5f8 #(kernel.cpp, line:92)
[00000005] [09:00:54:244192] [Tid0x0000dd94] [debug] <--[C3] kernel::create_new_session
[00000006] [09:00:54:244192] [Tid0x0000dd94] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [09:00:54:244192] [Tid0x0000dd94] [debug] <--[C4] boot_rom::boot_rom
[00000008] [09:00:54:250968] [Tid0x0000dd94] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [09:00:54:250968] [Tid0x0000dd94] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [09:00:54:250968] [Tid0x0000dd94] [debug] <--[C6] device_log_source::device_log_source
[00000011] [09:00:54:250968] [Tid0x0000dd94] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [09:00:54:250968] [Tid0x0000dd94] [debug] <--[C7] data_mux::data_mux
[00000013] [09:00:54:250968] [Tid0x0000dd94] [debug] <--[C5] device_instance::device_instance
[00000014] [09:00:54:250968] [Tid0x0000dd94] [debug] <--[C2] connection::create_session
[00000015] [09:00:54:250968] [Tid0x0000dd94] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [09:00:54:250968] [Tid0x0000dd94] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [09:00:54:250968] [Tid0x0000dd94] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [09:00:54:250968] [Tid0x0000dd94] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [09:00:54:250968] [Tid0x0000dd94] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [09:00:54:250968] [Tid0x0000dd94] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [09:00:54:251470] [Tid0x0000dd94] [debug] <--[C11] is_valid_ip
[00000022] [09:00:54:251470] [Tid0x0000dd94] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [09:00:54:251470] [Tid0x0000dd94] [debug] <--[C12] is_lge_impl
[00000024] [09:00:54:251470] [Tid0x0000dd94] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [09:00:54:251470] [Tid0x0000dd94] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [09:00:54:251470] [Tid0x0000dd94] [debug] <--[C13] lib_config_parser::get_value
[00000027] [09:00:54:251470] [Tid0x0000dd94] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [09:00:54:251470] [Tid0x0000dd94] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [09:00:54:251470] [Tid0x0000dd94] [info] try to open device: COM3 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [09:00:54:251470] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000031] [09:00:54:251470] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000032] [09:00:54:303983] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000033] [09:00:54:303983] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000034] [09:00:54:365983] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000035] [09:00:54:365983] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000036] [09:00:54:428490] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000037] [09:00:54:428490] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000038] [09:00:54:491005] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000039] [09:00:54:491005] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000040] [09:00:54:551998] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000041] [09:00:54:551998] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000042] [09:00:54:615505] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000043] [09:00:54:615505] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000044] [09:00:54:677507] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000045] [09:00:54:677507] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000046] [09:00:54:739022] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000047] [09:00:54:739022] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000048] [09:00:54:799542] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000049] [09:00:54:799542] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000050] [09:00:54:861083] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000051] [09:00:54:861083] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000052] [09:00:54:923620] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000053] [09:00:54:923620] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000054] [09:00:54:986621] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000055] [09:00:54:986621] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000056] [09:00:55:049132] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000057] [09:00:55:049132] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000058] [09:00:55:111670] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000059] [09:00:55:111670] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000060] [09:00:55:174671] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000061] [09:00:55:174671] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000062] [09:00:55:236197] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000063] [09:00:55:236197] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000064] [09:00:55:298035] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000065] [09:00:55:298035] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000066] [09:00:55:359035] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000067] [09:00:55:359035] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000068] [09:00:55:420256] [Tid0x0000dd94] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000069] [09:00:55:420256] [Tid0x0000dd94] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000070] [09:00:55:483254] [Tid0x0000dd94] [info] <--[C14] comm_engine::open
[00000071] [09:00:55:483254] [Tid0x0000dd94] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000072] [09:00:55:483254] [Tid0x0000dd94] [debug] <--[C9] connection::connect_brom
[00000073] [09:00:55:483254] [Tid0x0000dd94] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000074] [09:00:55:483254] [Tid0x0000dd94] [info] <--[C8] flashtool_connect_brom
[00000075] [09:00:55:483254] [Tid0x0000dd94] [info] -->[C15] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000076] [09:00:55:483254] [Tid0x0000dd94] [debug] -->[C17] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000077] [09:00:55:483254] [Tid0x0000dd94] [info] -->[C18] device_log_source::stop #(device_log_source.cpp, line:29)
[00000078] [09:00:55:483254] [Tid0x0000dd94] [info] <--[C18] device_log_source::stop
[00000079] [09:00:55:483254] [Tid0x0000dd94] [info] -->[C19] data_mux::stop #(data_mux.cpp, line:92)
[00000080] [09:00:55:483254] [Tid0x0000dd94] [info] <--[C19] data_mux::stop
[00000081] [09:00:55:483254] [Tid0x0000dd94] [debug] <--[C17] device_instance::~device_instance
[00000082] [09:00:55:483254] [Tid0x0000dd94] [info] -->[C20] comm_engine::close #(comm_engine.cpp, line:382)
[00000083] [09:00:55:483254] [Tid0x0000dd94] [debug] -->[C21] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000084] [09:00:55:483254] [Tid0x0000dd94] [debug] <--[C21] comm_engine::cancel
[00000085] [09:00:55:483254] [Tid0x0000dd94] [info] <--[C20] comm_engine::close
[00000086] [09:00:55:484255] [Tid0x0000dd94] [info] delete hsession 0xb4ae5f8 #(kernel.cpp, line:102)
[00000087] [09:00:55:484255] [Tid0x0000dd94] [info] <--[C15] flashtool_destroy_session
[00000088] [09:00:58:918536] [Tid0x00009910] [info] -->[C22] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000089] [09:00:58:918536] [Tid0x00009910] [debug] -->[C23] connection::create_session #(connection.cpp, line:43)
[00000090] [09:00:58:918536] [Tid0x00009910] [debug] -->[C24] kernel::create_new_session #(kernel.cpp, line:76)
[00000091] [09:00:58:918536] [Tid0x00009910] [info] create new hsession 0xb43b938 #(kernel.cpp, line:92)
[00000092] [09:00:58:918536] [Tid0x00009910] [debug] <--[C24] kernel::create_new_session
[00000093] [09:00:58:918536] [Tid0x00009910] [debug] -->[C25] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000094] [09:00:58:918536] [Tid0x00009910] [debug] <--[C25] boot_rom::boot_rom
[00000095] [09:00:58:918536] [Tid0x00009910] [debug] -->[C26] device_instance::device_instance #(device_instance.cpp, line:22)
[00000096] [09:00:58:918536] [Tid0x00009910] [debug] -->[C27] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000097] [09:00:58:918536] [Tid0x00009910] [debug] <--[C27] device_log_source::device_log_source
[00000098] [09:00:58:918536] [Tid0x00009910] [debug] -->[C28] data_mux::data_mux #(data_mux.cpp, line:10)
[00000099] [09:00:58:918536] [Tid0x00009910] [debug] <--[C28] data_mux::data_mux
[00000100] [09:00:58:918536] [Tid0x00009910] [debug] <--[C26] device_instance::device_instance
[00000101] [09:00:58:918536] [Tid0x00009910] [debug] <--[C23] connection::create_session
[00000102] [09:00:58:918536] [Tid0x00009910] [info] <--[C22] flashtool_create_session_with_handle
[00000103] [09:00:58:918536] [Tid0x00009910] [info] -->[C29] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000104] [09:00:58:918536] [Tid0x00009910] [debug] -->[C30] connection::connect_brom #(connection.cpp, line:94)
[00000105] [09:00:58:918536] [Tid0x00009910] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000106] [09:00:58:918536] [Tid0x00009910] [debug] -->[C31] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000107] [09:00:58:918536] [Tid0x00009910] [debug] -->[C32] is_valid_ip #(engine_factory.cpp, line:13)
[00000108] [09:00:58:918536] [Tid0x00009910] [debug] <--[C32] is_valid_ip
[00000109] [09:00:58:919536] [Tid0x00009910] [debug] -->[C33] is_lge_impl #(engine_factory.cpp, line:32)
[00000110] [09:00:58:919536] [Tid0x00009910] [debug] <--[C33] is_lge_impl
[00000111] [09:00:58:919536] [Tid0x00009910] [debug] -->[C34] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000112] [09:00:58:919536] [Tid0x00009910] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000113] [09:00:58:919536] [Tid0x00009910] [debug] <--[C34] lib_config_parser::get_value
[00000114] [09:00:58:919536] [Tid0x00009910] [debug] <--[C31] engine_factory::create_transmission_engine
[00000115] [09:00:58:919536] [Tid0x00009910] [info] -->[C35] comm_engine::open #(comm_engine.cpp, line:63)
[00000116] [09:00:58:919536] [Tid0x00009910] [info] try to open device: COM3 baud rate 115200 #(comm_engine.cpp, line:71)
[00000117] [09:00:58:919536] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000118] [09:00:58:919536] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000119] [09:00:58:982066] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000120] [09:00:58:982066] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000121] [09:00:59:043093] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000122] [09:00:59:043093] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000123] [09:00:59:105760] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000124] [09:00:59:105760] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000125] [09:00:59:168865] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000126] [09:00:59:168865] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000127] [09:00:59:230776] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000128] [09:00:59:230776] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000129] [09:00:59:292354] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000130] [09:00:59:292354] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000131] [09:00:59:354395] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000132] [09:00:59:354395] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000133] [09:00:59:416978] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000134] [09:00:59:416978] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000135] [09:00:59:479049] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000136] [09:00:59:479049] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000137] [09:00:59:540952] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000138] [09:00:59:540952] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000139] [09:00:59:603060] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000140] [09:00:59:603060] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000141] [09:00:59:666150] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000142] [09:00:59:666150] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000143] [09:00:59:727014] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000144] [09:00:59:727014] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000145] [09:00:59:788046] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000146] [09:00:59:788046] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000147] [09:00:59:851561] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000148] [09:00:59:851561] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000149] [09:00:59:914073] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000150] [09:00:59:914073] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000151] [09:00:59:975449] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000152] [09:00:59:975449] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000153] [09:01:00:037967] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000154] [09:01:00:037967] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000155] [09:01:00:099478] [Tid0x00009910] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000156] [09:01:00:099478] [Tid0x00009910] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000157] [09:01:00:162478] [Tid0x00009910] [info] <--[C35] comm_engine::open
[00000158] [09:01:00:162478] [Tid0x00009910] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000159] [09:01:00:162478] [Tid0x00009910] [debug] <--[C30] connection::connect_brom
[00000160] [09:01:00:162478] [Tid0x00009910] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000161] [09:01:00:162478] [Tid0x00009910] [info] <--[C29] flashtool_connect_brom
[00000162] [09:01:00:162478] [Tid0x00009910] [info] -->[C36] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000163] [09:01:00:162478] [Tid0x00009910] [debug] -->[C38] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000164] [09:01:00:162478] [Tid0x00009910] [info] -->[C39] device_log_source::stop #(device_log_source.cpp, line:29)
[00000165] [09:01:00:162478] [Tid0x00009910] [info] <--[C39] device_log_source::stop
[00000166] [09:01:00:162478] [Tid0x00009910] [info] -->[C40] data_mux::stop #(data_mux.cpp, line:92)
[00000167] [09:01:00:162478] [Tid0x00009910] [info] <--[C40] data_mux::stop
[00000168] [09:01:00:162478] [Tid0x00009910] [debug] <--[C38] device_instance::~device_instance
[00000169] [09:01:00:162478] [Tid0x00009910] [info] -->[C41] comm_engine::close #(comm_engine.cpp, line:382)
[00000170] [09:01:00:162478] [Tid0x00009910] [debug] -->[C42] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000171] [09:01:00:162478] [Tid0x00009910] [debug] <--[C42] comm_engine::cancel
[00000172] [09:01:00:162478] [Tid0x00009910] [info] <--[C41] comm_engine::close
[00000173] [09:01:00:162478] [Tid0x00009910] [info] delete hsession 0xb43b938 #(kernel.cpp, line:102)
[00000174] [09:01:00:162478] [Tid0x00009910] [info] <--[C36] flashtool_destroy_session
[00000175] [09:02:43:232880] [Tid0x0000d570] [info] -->[C43] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000176] [09:02:43:232880] [Tid0x0000d570] [debug] -->[C44] connection::create_session #(connection.cpp, line:43)
[00000177] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C45] kernel::create_new_session #(kernel.cpp, line:76)
[00000178] [09:02:43:233878] [Tid0x0000d570] [info] create new hsession 0xb443590 #(kernel.cpp, line:92)
[00000179] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C45] kernel::create_new_session
[00000180] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C46] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000181] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C46] boot_rom::boot_rom
[00000182] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C47] device_instance::device_instance #(device_instance.cpp, line:22)
[00000183] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C48] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000184] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C48] device_log_source::device_log_source
[00000185] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C49] data_mux::data_mux #(data_mux.cpp, line:10)
[00000186] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C49] data_mux::data_mux
[00000187] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C47] device_instance::device_instance
[00000188] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C44] connection::create_session
[00000189] [09:02:43:233878] [Tid0x0000d570] [info] <--[C43] flashtool_create_session_with_handle
[00000190] [09:02:43:233878] [Tid0x0000d570] [info] -->[C50] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000191] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C51] connection::connect_brom #(connection.cpp, line:94)
[00000192] [09:02:43:233878] [Tid0x0000d570] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000193] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C52] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000194] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C53] is_valid_ip #(engine_factory.cpp, line:13)
[00000195] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C53] is_valid_ip
[00000196] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C54] is_lge_impl #(engine_factory.cpp, line:32)
[00000197] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C54] is_lge_impl
[00000198] [09:02:43:233878] [Tid0x0000d570] [debug] -->[C55] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000199] [09:02:43:233878] [Tid0x0000d570] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000200] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C55] lib_config_parser::get_value
[00000201] [09:02:43:233878] [Tid0x0000d570] [debug] <--[C52] engine_factory::create_transmission_engine
[00000202] [09:02:43:233878] [Tid0x0000d570] [info] -->[C56] comm_engine::open #(comm_engine.cpp, line:63)
[00000203] [09:02:43:233878] [Tid0x0000d570] [info] try to open device: COM3 baud rate 115200 #(comm_engine.cpp, line:71)
[00000204] [09:02:43:233878] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000205] [09:02:43:233878] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000206] [09:02:43:292883] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000207] [09:02:43:292883] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000208] [09:02:43:355407] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000209] [09:02:43:355407] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000210] [09:02:43:419416] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000211] [09:02:43:419416] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000212] [09:02:43:480923] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000213] [09:02:43:480923] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000214] [09:02:43:543437] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000215] [09:02:43:543437] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000216] [09:02:43:606437] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000217] [09:02:43:606437] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000218] [09:02:43:666953] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000219] [09:02:43:666953] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000220] [09:02:43:726974] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000221] [09:02:43:726974] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000222] [09:02:43:788974] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000223] [09:02:43:788974] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000224] [09:02:43:851022] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000225] [09:02:43:851022] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000226] [09:02:43:913129] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000227] [09:02:43:913129] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000228] [09:02:43:975617] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000229] [09:02:43:975617] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000230] [09:02:44:038280] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000231] [09:02:44:038280] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000232] [09:02:44:100845] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000233] [09:02:44:100845] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000234] [09:02:44:161758] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000235] [09:02:44:161758] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000236] [09:02:44:225317] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000237] [09:02:44:225317] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000238] [09:02:44:288370] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000239] [09:02:44:288370] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000240] [09:02:44:350433] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000241] [09:02:44:350433] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000242] [09:02:44:411540] [Tid0x0000d570] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000243] [09:02:44:411540] [Tid0x0000d570] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000244] [09:02:44:474154] [Tid0x0000d570] [info] <--[C56] comm_engine::open
[00000245] [09:02:44:474154] [Tid0x0000d570] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000246] [09:02:44:474154] [Tid0x0000d570] [debug] <--[C51] connection::connect_brom
[00000247] [09:02:44:474154] [Tid0x0000d570] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000248] [09:02:44:474154] [Tid0x0000d570] [info] <--[C50] flashtool_connect_brom
[00000249] [09:02:44:474154] [Tid0x0000d570] [info] -->[C57] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000250] [09:02:44:474154] [Tid0x0000d570] [debug] -->[C59] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000251] [09:02:44:474154] [Tid0x0000d570] [info] -->[C60] device_log_source::stop #(device_log_source.cpp, line:29)
[00000252] [09:02:44:474154] [Tid0x0000d570] [info] <--[C60] device_log_source::stop
[00000253] [09:02:44:474154] [Tid0x0000d570] [info] -->[C61] data_mux::stop #(data_mux.cpp, line:92)
[00000254] [09:02:44:474154] [Tid0x0000d570] [info] <--[C61] data_mux::stop
[00000255] [09:02:44:474154] [Tid0x0000d570] [debug] <--[C59] device_instance::~device_instance
[00000256] [09:02:44:474154] [Tid0x0000d570] [info] -->[C62] comm_engine::close #(comm_engine.cpp, line:382)
[00000257] [09:02:44:474154] [Tid0x0000d570] [debug] -->[C63] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000258] [09:02:44:474154] [Tid0x0000d570] [debug] <--[C63] comm_engine::cancel
[00000259] [09:02:44:475146] [Tid0x0000d570] [info] <--[C62] comm_engine::close
[00000260] [09:02:44:475146] [Tid0x0000d570] [info] delete hsession 0xb443590 #(kernel.cpp, line:102)
[00000261] [09:02:44:475146] [Tid0x0000d570] [info] <--[C57] flashtool_destroy_session

[0000001] [17:01:08:953941] [Tid0x000107f8] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [17:01:08:953941] [Tid0x000107f8] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [17:01:33:716812] [Tid0x000107f8] [debug] -->[C2] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000004] [17:01:33:716812] [Tid0x000107f8] [debug] <--[C2] DL_SetChecksumLevel
[0000005] [17:01:33:784624] [Tid0x000107f8] [debug] -->[C3] FlashTool_Connect #(api.cpp, line:883)
[0000006] [17:01:33:784624] [Tid0x000107f8] [debug] bCheckScatter: 0 #(api.cpp, line:884)
[0000007] [17:01:33:784624] [Tid0x000107f8] [debug] -->[C4] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000008] [17:01:33:784624] [Tid0x000107f8] [debug] bCheckScatter: 0
[0000009] [17:01:33:790401] [Tid0x000107f8] [debug] -->[C5] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000010] [17:01:33:790401] [Tid0x000107f8] [debug] bCheckScatter: 0 #(api.cpp, line:1939)
[0000011] [17:01:33:790401] [Tid0x000107f8] [debug] Not load scatter yet #(api.cpp, line:1966)
[0000012] [17:01:34:588563] [Tid0x000107f8] [debug] chip: MT6765 #(api.cpp, line:1995)
[0000013] [17:01:34:588563] [Tid0x000107f8] [debug] used lib version: 2 #(api.cpp, line:2006)
[0000014] [17:01:34:588563] [Tid0x000107f8] [debug] -->[C6] FlashTool_CloseComport #(api.cpp, line:930)
[0000015] [17:01:34:603564] [Tid0x000107f8] [debug] <--[C6] FlashTool_CloseComport
[0000016] [17:01:34:603564] [Tid0x000107f8] [debug] -->[C7] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000017] [17:01:34:614714] [Tid0x000107f8] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2552)
[0000018] [17:01:34:614714] [Tid0x000107f8] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000019] [17:01:34:614714] [Tid0x000107f8] [debug] callback in brom stage #(cflashtool_api.cpp, line:1209)
[0000020] [17:01:34:614714] [Tid0x000107f8] [debug] m_cb_in_brom_stage run success #(cflashtool_api.cpp, line:1217)
[0000021] [17:01:34:614714] [Tid0x000107f8] [debug] connect_brom_ex OK #(cflashtool_api.cpp, line:1223)
[0000022] [17:01:34:614714] [Tid0x000107f8] [debug] <--[C7] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000023] [17:01:34:614714] [Tid0x000107f8] [debug] <--[C5] FlashTool_Connect_BROM_Ex
[0000024] [17:01:34:614714] [Tid0x000107f8] [debug] bRealCheckScatter: 0 #(api.cpp, line:968)
[0000025] [17:01:34:614714] [Tid0x000107f8] [debug] -->[C8] FlashTool_Connect_Download_DA #(api.cpp, line:2071)
[0000026] [17:01:34:614714] [Tid0x000107f8] [debug] bCheckScatter: 0 #(api.cpp, line:2072)
[0000027] [17:01:34:614714] [Tid0x000107f8] [debug] -->[C9] cflashtool_api::FlashTool_Connect_Download_DA #(cflashtool_api.cpp, line:1384)
[0000028] [17:01:34:614714] [Tid0x000107f8] [debug] bCheckScatter: 0 #(cflashtool_api.cpp, line:1385)
[0000029] [17:01:34:614714] [Tid0x000107f8] [debug] da_source type: 0 #(cflashtool_api.cpp, line:1414)
[0000030] [17:01:34:614714] [Tid0x000107f8] [debug] checksum_level: 3,  battery_setting: 0, reset_key_setting: 0 #(cflashtool_api.cpp, line:1527)
[0000031] [17:01:34:614714] [Tid0x000107f8] [debug] connect_da_end_stage: 2,  enable_dram_in_1st_da: 0, da_log_level: 1, da_log_channel: 1 #(cflashtool_api.cpp, line:1529)
[0000032] [17:01:34:618714] [Tid0x000107f8] [debug] <--[C9] cflashtool_api::FlashTool_Connect_Download_DA
[0000033] [17:01:34:618714] [Tid0x000107f8] [debug] <--[C8] FlashTool_Connect_Download_DA
[0000034] [17:01:34:618714] [Tid0x000107f8] [debug] <--[C4] FlashTool_Connect_Ex
[0000035] [17:01:34:618714] [Tid0x000107f8] [debug] <--[C3] FlashTool_Connect
[0000036] [17:07:10:826209] [Tid0x000107f8] [debug] -->[C10] DL_Rom_UnloadAll #(api.cpp, line:2904)
[0000037] [17:07:10:826209] [Tid0x000107f8] [debug] <--[C10] DL_Rom_UnloadAll

[00000001] [00:09:26:403607] [Tid0x00006f04] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [00:09:26:405607] [Tid0x00006f04] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [00:09:26:405607] [Tid0x00006f04] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [00:09:26:405607] [Tid0x00006f04] [info] create new hsession 0xb6d3a68 #(kernel.cpp, line:92)
[00000005] [00:09:26:405607] [Tid0x00006f04] [debug] <--[C3] kernel::create_new_session
[00000006] [00:09:26:406608] [Tid0x00006f04] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [00:09:26:406608] [Tid0x00006f04] [debug] <--[C4] boot_rom::boot_rom
[00000008] [00:09:26:406608] [Tid0x00006f04] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [00:09:26:406608] [Tid0x00006f04] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [00:09:26:406608] [Tid0x00006f04] [debug] <--[C6] device_log_source::device_log_source
[00000011] [00:09:26:406608] [Tid0x00006f04] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [00:09:26:406608] [Tid0x00006f04] [debug] <--[C7] data_mux::data_mux
[00000013] [00:09:26:406608] [Tid0x00006f04] [debug] <--[C5] device_instance::device_instance
[00000014] [00:09:26:406608] [Tid0x00006f04] [debug] <--[C2] connection::create_session
[00000015] [00:09:26:406608] [Tid0x00006f04] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [00:09:26:406608] [Tid0x00006f04] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [00:09:26:406608] [Tid0x00006f04] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [00:09:26:406608] [Tid0x00006f04] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [00:09:26:407608] [Tid0x00006f04] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [00:09:26:407608] [Tid0x00006f04] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [00:09:26:408607] [Tid0x00006f04] [debug] <--[C11] is_valid_ip
[00000022] [00:09:26:408607] [Tid0x00006f04] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [00:09:26:408607] [Tid0x00006f04] [debug] <--[C12] is_lge_impl
[00000024] [00:09:26:408607] [Tid0x00006f04] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [00:09:26:408607] [Tid0x00006f04] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [00:09:26:408607] [Tid0x00006f04] [debug] <--[C13] lib_config_parser::get_value
[00000027] [00:09:26:408607] [Tid0x00006f04] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [00:09:26:408607] [Tid0x00006f04] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [00:09:26:408607] [Tid0x00006f04] [info] try to open device: COM3 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [00:09:26:408607] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000031] [00:09:26:408607] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000032] [00:09:26:462116] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000033] [00:09:26:462116] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000034] [00:09:26:524623] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000035] [00:09:26:524623] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000036] [00:09:26:587640] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000037] [00:09:26:587640] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000038] [00:09:26:650208] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000039] [00:09:26:650208] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000040] [00:09:26:712685] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000041] [00:09:26:712685] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000042] [00:09:26:776685] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000043] [00:09:26:776685] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000044] [00:09:26:839193] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000045] [00:09:26:839193] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000046] [00:09:26:901197] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000047] [00:09:26:901197] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000048] [00:09:26:961504] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000049] [00:09:26:961504] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000050] [00:09:27:022819] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000051] [00:09:27:022819] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000052] [00:09:27:084826] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000053] [00:09:27:084826] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000054] [00:09:27:146357] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000055] [00:09:27:146357] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000056] [00:09:27:206029] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000057] [00:09:27:206029] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000058] [00:09:27:268061] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000059] [00:09:27:268061] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000060] [00:09:27:331581] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000061] [00:09:27:331581] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000062] [00:09:27:393586] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000063] [00:09:27:393586] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000064] [00:09:27:457103] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000065] [00:09:27:457103] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000066] [00:09:27:518771] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000067] [00:09:27:518771] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000068] [00:09:27:580771] [Tid0x00006f04] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000069] [00:09:27:580771] [Tid0x00006f04] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000070] [00:09:27:650785] [Tid0x00006f04] [info] <--[C14] comm_engine::open
[00000071] [00:09:27:650785] [Tid0x00006f04] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000072] [00:09:27:650785] [Tid0x00006f04] [debug] <--[C9] connection::connect_brom
[00000073] [00:09:27:650785] [Tid0x00006f04] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000074] [00:09:27:650785] [Tid0x00006f04] [info] <--[C8] flashtool_connect_brom
[00000075] [00:09:27:650785] [Tid0x00006f04] [info] -->[C15] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000076] [00:09:27:650785] [Tid0x00006f04] [debug] -->[C17] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000077] [00:09:27:650785] [Tid0x00006f04] [info] -->[C18] device_log_source::stop #(device_log_source.cpp, line:29)
[00000078] [00:09:27:650785] [Tid0x00006f04] [info] <--[C18] device_log_source::stop
[00000079] [00:09:27:650785] [Tid0x00006f04] [info] -->[C19] data_mux::stop #(data_mux.cpp, line:92)
[00000080] [00:09:27:650785] [Tid0x00006f04] [info] <--[C19] data_mux::stop
[00000081] [00:09:27:650785] [Tid0x00006f04] [debug] <--[C17] device_instance::~device_instance
[00000082] [00:09:27:650785] [Tid0x00006f04] [info] -->[C20] comm_engine::close #(comm_engine.cpp, line:382)
[00000083] [00:09:27:650785] [Tid0x00006f04] [debug] -->[C21] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000084] [00:09:27:650785] [Tid0x00006f04] [debug] <--[C21] comm_engine::cancel
[00000085] [00:09:27:650785] [Tid0x00006f04] [info] <--[C20] comm_engine::close
[00000086] [00:09:27:651786] [Tid0x00006f04] [info] delete hsession 0xb6d3a68 #(kernel.cpp, line:102)
[00000087] [00:09:27:651786] [Tid0x00006f04] [info] <--[C15] flashtool_destroy_session

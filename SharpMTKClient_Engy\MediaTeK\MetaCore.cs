﻿using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Security;

namespace SharpMTKClient_Engy.MediaTeK
{
    public class MetaCore
    {
        [DllImport("MetaCore.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int SP_META_Init(_973A1413 _973A1413_0);
        
        [DllImport("MetaCore.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        public static extern int SP_Preloader_BootMode(ref _42BD8819 _42BD8819_0);

    }
}

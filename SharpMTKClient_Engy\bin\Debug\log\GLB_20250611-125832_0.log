[00000001] [12:58:32:162068] [Tid0x0000d408] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [12:58:32:163076] [Tid0x0000d408] [info] create new hsession 0xbdf2648 #(kernel.cpp, line:92)
[00000005] [12:58:32:163076] [Tid0x0000d408] [debug] <--[C3] kernel::create_new_session
[00000006] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [12:58:32:163076] [Tid0x0000d408] [debug] <--[C4] boot_rom::boot_rom
[00000008] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [12:58:32:163076] [Tid0x0000d408] [debug] <--[C6] device_log_source::device_log_source
[00000011] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [12:58:32:163076] [Tid0x0000d408] [debug] <--[C7] data_mux::data_mux
[00000013] [12:58:32:163076] [Tid0x0000d408] [debug] <--[C5] device_instance::device_instance
[00000014] [12:58:32:163076] [Tid0x0000d408] [debug] <--[C2] connection::create_session
[00000015] [12:58:32:163076] [Tid0x0000d408] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [12:58:32:163076] [Tid0x0000d408] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [12:58:32:163076] [Tid0x0000d408] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [12:58:32:163076] [Tid0x0000d408] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [12:58:32:164076] [Tid0x0000d408] [debug] <--[C11] is_valid_ip
[00000022] [12:58:32:164076] [Tid0x0000d408] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [12:58:32:164076] [Tid0x0000d408] [debug] <--[C12] is_lge_impl
[00000024] [12:58:32:164076] [Tid0x0000d408] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [12:58:32:164076] [Tid0x0000d408] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [12:58:32:164076] [Tid0x0000d408] [debug] <--[C13] lib_config_parser::get_value
[00000027] [12:58:32:164076] [Tid0x0000d408] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [12:58:32:164076] [Tid0x0000d408] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [12:58:32:164076] [Tid0x0000d408] [info] try to open device: COM3 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [12:58:32:164076] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000031] [12:58:32:164076] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000032] [12:58:32:226328] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000033] [12:58:32:226328] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000034] [12:58:32:289416] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000035] [12:58:32:289416] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000036] [12:58:32:351201] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000037] [12:58:32:351201] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000038] [12:58:32:414144] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000039] [12:58:32:414144] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000040] [12:58:32:474163] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000041] [12:58:32:474163] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000042] [12:58:32:536697] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000043] [12:58:32:536697] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000044] [12:58:32:600187] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000045] [12:58:32:600187] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000046] [12:58:32:661117] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000047] [12:58:32:661117] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000048] [12:58:32:722096] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000049] [12:58:32:722096] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000050] [12:58:32:783576] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000051] [12:58:32:783576] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000052] [12:58:32:845134] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000053] [12:58:32:845134] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000054] [12:58:32:907782] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000055] [12:58:32:907782] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000056] [12:58:32:971952] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000057] [12:58:32:971952] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000058] [12:58:33:033793] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000059] [12:58:33:033793] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000060] [12:58:33:094893] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000061] [12:58:33:094893] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000062] [12:58:33:155901] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000063] [12:58:33:155901] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000064] [12:58:33:215951] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000065] [12:58:33:215951] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000066] [12:58:33:277074] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000067] [12:58:33:277074] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000068] [12:58:33:338369] [Tid0x0000d408] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000069] [12:58:33:338369] [Tid0x0000d408] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000070] [12:58:33:399971] [Tid0x0000d408] [info] <--[C14] comm_engine::open
[00000071] [12:58:33:399971] [Tid0x0000d408] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000072] [12:58:33:399971] [Tid0x0000d408] [debug] <--[C9] connection::connect_brom
[00000073] [12:58:33:399971] [Tid0x0000d408] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000074] [12:58:33:399971] [Tid0x0000d408] [info] <--[C8] flashtool_connect_brom
[00000075] [12:58:33:399971] [Tid0x0000d408] [info] -->[C15] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000076] [12:58:33:399971] [Tid0x0000d408] [debug] -->[C17] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000077] [12:58:33:399971] [Tid0x0000d408] [info] -->[C18] device_log_source::stop #(device_log_source.cpp, line:29)
[00000078] [12:58:33:399971] [Tid0x0000d408] [info] <--[C18] device_log_source::stop
[00000079] [12:58:33:399971] [Tid0x0000d408] [info] -->[C19] data_mux::stop #(data_mux.cpp, line:92)
[00000080] [12:58:33:399971] [Tid0x0000d408] [info] <--[C19] data_mux::stop
[00000081] [12:58:33:399971] [Tid0x0000d408] [debug] <--[C17] device_instance::~device_instance
[00000082] [12:58:33:399971] [Tid0x0000d408] [info] -->[C20] comm_engine::close #(comm_engine.cpp, line:382)
[00000083] [12:58:33:399971] [Tid0x0000d408] [debug] -->[C21] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000084] [12:58:33:399971] [Tid0x0000d408] [debug] <--[C21] comm_engine::cancel
[00000085] [12:58:33:399971] [Tid0x0000d408] [info] <--[C20] comm_engine::close
[00000086] [12:58:33:399971] [Tid0x0000d408] [info] delete hsession 0xbdf2648 #(kernel.cpp, line:102)
[00000087] [12:58:33:399971] [Tid0x0000d408] [info] <--[C15] flashtool_destroy_session

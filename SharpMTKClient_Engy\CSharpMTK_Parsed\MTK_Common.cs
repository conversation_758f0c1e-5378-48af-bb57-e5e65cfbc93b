﻿using System;
using System.Runtime.InteropServices;
using System.Text;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed
{
    public class MTK_Common
    {
        public enum FILTER_TYPE_E
        {
            WHITE_LIST,
            BLACK_LIST
        }

        public struct COM_FILTER_LIST_S
        {
            public uint m_uCount;

            public FILTER_TYPE_E m_eType;

            public HandleRef m_ppFilterID;

            public IntPtr m_bInterface;
        }

        public struct COM_PROPERTY_S
        {
            public int m_iFilterIndex;

            public uint m_uNumber;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_rFriendly;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_rInstanceID;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_rSymbolic;

            public bool m_bInterface;
        }

        public const ushort MAX_LOAD_SECTIONS = 128;

        public const int OFFSET_ATM = 533;

        public const int KEY1 = 87654321;

        public const int KEY2 = 87979987;

        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern string StatusToString(int err_code);

        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int Brom_Debug_SetLogFilename(byte[] filename);

        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int Brom_DebugOn();

        [DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int GetSpecialCOMPortWithFilter(ref COM_FILTER_LIST_S pCOMFilter, int dPreferComPort, ref COM_PROPERTY_S pCOMPorperty, IntPtr p_stopflag, double dTimeout);

        public static string decodeOut(string str)
        {
            char[] array = str.ToCharArray();
            Encoder encoder = Encoding.Unicode.GetEncoder();
            byte[] array2 = new byte[encoder.GetByteCount(array, 0, array.Length, flush: true)];
            encoder.GetBytes(array, 0, array.Length, array2, 0, flush: true);
            Decoder decoder = Encoding.UTF8.GetDecoder();
            char[] array3 = new char[decoder.GetCharCount(array2, 0, array2.Length)];
            decoder.GetChars(array2, 0, array2.Length, array3, 0);
            return new string(array3);
        }

        public static MTK_Status.STATUS_E IntConvertToEnum(uint i)
        {
            if (Enum.IsDefined(typeof(MTK_Status.STATUS_E), i))
            {
                return (MTK_Status.STATUS_E)Enum.ToObject(typeof(MTK_Status.STATUS_E), i);
            }
            return MTK_Status.STATUS_E.S_UNDEFINED_ERROR;
        }
    }
}

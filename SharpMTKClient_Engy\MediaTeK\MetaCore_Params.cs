﻿using System;
using System.Runtime.InteropServices;

namespace SharpMTKClient_Engy.MediaTeK
{
    public delegate int _67A2A210(IntPtr intptr_0, IntPtr F70083B7, uint uint_0, out IntPtr intptr_1, ref uint uint_1);
    public delegate int B987450F(IntPtr E11AD120, IntPtr intptr_0);
    public delegate int _973A1413(IntPtr intptr_0, IntPtr intptr_1);

    public struct _42BD8819
    {
        public BB22AA0D DF19B32B;

        public EXT_CLOCK A237ADA7;

        public uint _3C2E9C82;

        public uint _95266A32;

        public uint _77026BBA;

        public uint _602DDF2A;

        public uint _7694ABAA;

        public uint A42EC525;

        public int F59720BB;

        public bool _898ED02A;

        public bool _70317929;

        public bool FAA9F622;

        public bool _2B8CA0A4;

        public bool FB084C82;

        public E481559C _6207B13D;

        public ushort _250A8E0B;

        public uint _8C18858C;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string _421F3AB1;

        public IntPtr C9B9EB2C;

        public IntPtr _03325899;

        public _67A2A210 _6CA9738B;

        public IntPtr DE29DA26;

        public B987450F A496E721;

        public IntPtr _05209A97;

        public bool _45075713;
    }

    public enum BB22AA0D
    {
        _1333830E = 0,
        F318B529 = 1,
        CB028394 = 2,
        _4F27819A = 4,
        _7E0A4C25 = 5,
        _48256698 = 6,
        EA128A0A = 7,
        A0ACD084 = 8,
        _7888DD2C = 9,
        _2016AA07 = 10,
        B9BE589D = 11,
        DB33EFA9 = 12,
        F9BDA523 = 13,
        _9FA4DE9B = 14,
        _002BC31C = 15,
        _7C0FCA93 = 16,
        E7856C09 = 17,
        _6D198CAF = 18,
        _172B15BD = 19,
        _23219708 = 20,
        _5D0D9392 = 21,
        F023A693 = 22,
        _292D583E = 23,
        D0811492 = 24,
        E5BE5828 = 25,
        _9413C91D = 26,
        _72A28B87 = 27,
        _288CCC92 = 28,
        BF0D1039 = 29,
        _04807AB7 = 30,
        _833FEA99 = 32,
        B0A2528F = 33,
        _19B66A83 = 34,
        _70926BBC = 35,
        _7B2E2A82 = 128,
        _0F3E849F = 129,
        _56A0B2AE = 130,
        C738B71E = 131,
        _4D1FBA1B = 132,
        _4E05C985 = 133,
        _4A260BB1 = 134,
        FC05210E = 135,
        FE0F030C = 136,
        _18B453A8 = 137,
        _7986B3B7 = 138,
        _42B058B1 = 139,
        E524A81D = 140,
        FC365D84 = 141,
        _5D034393 = 142,
        _51863618 = 143,
        EB05C606 = 144,
        A7A86DB0 = 145,
        _9E9EECB7 = 146,
        A139FB88 = 147,
        CA0A3410 = 148,
        _2CBF939E = 149,
        _5A09BB38 = 150,
        ABA29683 = 151,
        _4F9AC3B9 = 152,
        _3E99A82E = 153,
        _5CB2D514 = 154,
        _2D9EDD1B = 135,
        _9AB77F20 = 155,
        _72B58F86 = 156,
        _6682BA86 = 157,
        _64A1CF8C = 158,
        _5B25EC3E = 159,
        _80137730 = 160,
        _69BD859E = 161,
        _51A6D923 = 180,
        _1487499F = 181,
        _80BD1601 = 182,
        C98D2AA2 = 183,
        F70E0DA5 = 184,
        _5B16D029 = 185,
        _2F35EE89 = 186,
        _9C33B739 = 187,
        _99206C07 = 188,
        _168FF3AB = 189,
        _28938C09 = 190,
        E9259399 = 191,
        FDBC1CA3 = 192,
        BF3CA0AE = 193,
        _2D3DC434 = 194,
        EA8BC204 = 195,
        AD28B282 = 196,
        _9FAB7E98 = 197,
        _5D96F635 = 198,
        _4CAE1E07 = 199,
        _32936A22 = 200,
        _150E2D8C = 201,
        _7D8C4B3F = 202,
        _6C059E82 = 203,
        _5933690B = 204,
        CDBF8A18 = 205,
        _320DF7A4 = 206,
        _99B6933D = 207,
        _701601AC = 208,
        _0B31E830 = 209,
        _42AF7933 = 210,
        _56B2E5BF = 211,
        _8792ECBC = 212,
        DF855721 = 213,
        D82FD6A1 = 214,
        _97045A2E = 215,
        B1A88FA6 = 216,
        _7085E022 = 254,
        BEA9E1B9 = 255
    }

    public enum EXT_CLOCK
    {
        EXT_13M = 1,
        EXT_26M = 2,
        EXT_39M = 3,
        EXT_52M = 4,
        EXT_CLOCK_END = 5,
        AUTO_DETECT_EXT_CLOCK = 254,
        UNKNOWN_EXT_CLOCK = 255
    }

    public enum E481559C
    {
        _8D25EB17 = 0,
        _87BEE281 = 1,
        _5AA79F83 = 2,
        _239DD99E = 3,
        C6836980 = 4,
        F5025AAC = 5,
        E32A1A3A = 6,
        _349294A5 = 7,
        BE18B0B2 = 8,
        E68349A0 = 99,
        _87B84A27 = 100,
        _53074E16 = 101
    }


}

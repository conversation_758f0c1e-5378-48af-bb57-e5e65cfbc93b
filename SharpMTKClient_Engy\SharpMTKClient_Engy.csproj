<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7988925B-7401-4173-8D2F-CB19CE601B43}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>SharpMTKClient_Engy</RootNamespace>
    <AssemblyName>SharpMTKClient_Engy</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CSharpMTK_Parsed\BL\DeviceCtrl.cs" />
    <Compile Include="CSharpMTK_Parsed\BL\MTKDevice.cs" />
    <Compile Include="CSharpMTK_Parsed\Data\FlashGlobal.cs" />
    <Compile Include="CSharpMTK_Parsed\Data\SaveFfu.cs" />
    <Compile Include="CSharpMTK_Parsed\IniFile.cs" />
    <Compile Include="CSharpMTK_Parsed\Module\Device.cs" />
    <Compile Include="CSharpMTK_Parsed\Module\Ffu.cs" />
    <Compile Include="CSharpMTK_Parsed\Module\MiProperty.cs" />
    <Compile Include="CSharpMTK_Parsed\Module\Script.cs" />
    <Compile Include="CSharpMTK_Parsed\Module\SoftwareImage.cs" />
    <Compile Include="CSharpMTK_Parsed\Module\SwDes.cs" />
    <Compile Include="CSharpMTK_Parsed\Module\Wb.cs" />
    <Compile Include="CSharpMTK_Parsed\MTK_AUTH.cs" />
    <Compile Include="CSharpMTK_Parsed\MTK_Common.cs" />
    <Compile Include="CSharpMTK_Parsed\MTK_DA.cs" />
    <Compile Include="CSharpMTK_Parsed\MTK_DL.cs" />
    <Compile Include="CSharpMTK_Parsed\MTK_FlashTool.cs" />
    <Compile Include="CSharpMTK_Parsed\MTK_SCERT.cs" />
    <Compile Include="CSharpMTK_Parsed\MTK_Status.cs" />
    <Compile Include="CSharpMTK_Parsed\Utility\Cmd.cs" />
    <Compile Include="CSharpMTK_Parsed\Utility\Comm.cs" />
    <Compile Include="CSharpMTK_Parsed\Utility\ComPortCtrl.cs" />
    <Compile Include="CSharpMTK_Parsed\Utility\DoHandler.cs" />
    <Compile Include="CSharpMTK_Parsed\Utility\DoHandlerScript.cs" />
    <Compile Include="CSharpMTK_Parsed\Utility\MiAppConfig.cs" />
    <Compile Include="CSharpMTK_Parsed\Utility\TimeOut.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="MediaTeK\FlashToolLib.cs" />
    <Compile Include="MediaTeK\FlashtoollibEx.cs" />
    <Compile Include="MediaTeK\FlashToolLibEx_Params.cs" />
    <Compile Include="MediaTeK\FlashToolLibV1.cs" />
    <Compile Include="MediaTeK\FlashToolLib_Params.cs" />
    <Compile Include="MediaTeK\MetaCore.cs" />
    <Compile Include="MediaTeK\MetaCore_Params.cs" />
    <Compile Include="MediaTeK\SLA_ChallengeLib.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
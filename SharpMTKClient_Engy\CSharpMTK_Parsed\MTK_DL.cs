﻿using System;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed
{
    public class MTK_DL
    {
        public enum REGION_ADDR_TYPE
        {
            ABSOLUTE_ADDR,
            OFFSET_VALUE
        }

        public enum ROM_TYPE
        {
            NORMAL_ROM = 1,
            SV5_BL_BIN = 7,
            YAFFS_IMG = 8,
            MBR_BIN = 9,
            UBI_IMG = 16,
            EXT4_IMG = 17,
            FAT_IMG = 18,
            F2FS_IMG = 19,
            FTL20_IMG = 32,
            UNKNOWN_BIN = 255
        }

        public enum Partition_Operation_E
        {
            OPER_BOOTLOADERS,
            OPER_UPDATE,
            OPER_INVISIBLE,
            OPER_PROTECTED,
            OPER_RESERVED,
            OPER_BINREGION,
            OPER_UNKNOWN,
            OPER_END
        }

        public struct ROM_INFO
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string name;

            public ulong region_addr;

            public REGION_ADDR_TYPE addr_type;

            public ulong begin_addr;

            public ulong end_addr;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string filepath;

            public ulong filesize;

            public ushort index;

            public ROM_TYPE rom_type;

            public bool enable;

            public bool item_is_visable;

            public bool is_reserved;

            public Partition_Operation_E operation_type;

            public uint part_id;

            public ulong partition_size;

            public bool combo_partsize_check;

        }

        public delegate int CALLBACK_ROM_MEM_CHECKSUM_PROGRESS_INIT(IntPtr usr_arg, string image_name);

        public delegate int CALLBACK_ROM_MEM_CHECKSUM_PROGRESS(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg);

        public struct RomMemChecksumArg
        {
            public ushort index;

            public uint chksum;

            public CALLBACK_ROM_MEM_CHECKSUM_PROGRESS_INIT m_cb_rom_mem_checksum_init;

            public IntPtr m_cb_rom_mem_checksum_init_arg;

            public CALLBACK_ROM_MEM_CHECKSUM_PROGRESS m_cb_rom_mem_checksum;

            public IntPtr m_cb_rom_mem_checksum_arg;

            public IntPtr p_stopflag;
        }

        public struct RomMemChecksumResult
        {
        }

        public struct DL_PlatformInfo
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string m_szBBChipType;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string m_szStorageType;

            public MTK_FlashTool.BBCHIP_TYPE m_bbchip_type;

            public MTK_FlashTool.HW_StorageType_E m_storage_type;
        }

        public static IntPtr g_dl_handle;

        public static IntPtr g_dl_handle_list;

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Create(ref IntPtr p_dl_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Rom_UnloadAll(IntPtr p_dl_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_SetChecksumLevel(IntPtr p_dl_handle, int level);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Destroy(ref IntPtr p_dl_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_LoadScatter(IntPtr p_dl_handle, byte[] scatter_file, byte[] chip_name);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_AutoLoadRomImages(IntPtr p_dl_handle, byte[] scatter_file);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_GetPlatformInfo(IntPtr p_dl_handle, ref DL_PlatformInfo pPlatformInfo);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_GetCount(IntPtr p_dl_handle, out ushort p_rom_count);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_Rom_GetInfoAll(IntPtr p_dl_handle, IntPtr p_rom_info, ushort max_rom_count);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_VerifyROMMemBuf(IntPtr p_dl_handle, ref RomMemChecksumArg p_rom_mem_check_arg, ref RomMemChecksumResult p_rom_mem_check_result);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_CreateList(ref IntPtr dl_handle_list);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_DestroyList(ref IntPtr dl_handle_list);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DL_AddHandleToList(IntPtr dl_handle_list, IntPtr p_dl_handle);

        public static void DLTest()
        {
            IntPtr p_dl_handle = default(IntPtr);
            IntPtr p_dl_handle2 = default(IntPtr);
            IntPtr dl_handle_list = default(IntPtr);
            int num = -1;
            string s = "D:\\刷机包\\G7\\begonia_factory_images_FACTORY-BEGONIA-1123_9.0\\images\\MT6785_Android_scatter.txt";
            num = DL_Create(ref p_dl_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_Create err" + str);
            }
            Console.WriteLine("DL_Create 0 ret" + num);
            num = DL_Create(ref p_dl_handle2);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("DL_Create err" + str2);
            }
            Console.WriteLine("DL_Create 1 ret" + num);
            num = DL_CreateList(ref dl_handle_list);
            if (num != 0)
            {
                string str3 = MTK_Common.StatusToString(num);
                str3 = MTK_Common.decodeOut(str3);
                Console.WriteLine("DL_CreateList err" + str3);
            }
            Console.WriteLine("DL_CreateList ret" + num);
            num = DL_LoadScatter(p_dl_handle, Encoding.Default.GetBytes(s), null);
            if (num != 0)
            {
                string str4 = MTK_Common.StatusToString(num);
                str4 = MTK_Common.decodeOut(str4);
                Console.WriteLine("DL_LoadScatter err" + str4);
            }
            Console.WriteLine("DL_LoadScatter 0 ret" + num);
            num = DL_AutoLoadRomImages(p_dl_handle, Encoding.Default.GetBytes(s));
            if (num != 0)
            {
                string str5 = MTK_Common.StatusToString(num);
                str5 = MTK_Common.decodeOut(str5);
                Console.WriteLine("DL_AutoLoadRomImages err" + str5);
            }
            Console.WriteLine("DL_AutoLoadRomImages 0 ret" + num);
            num = DL_LoadScatter(p_dl_handle2, Encoding.Default.GetBytes(s), null);
            if (num != 0)
            {
                string str6 = MTK_Common.StatusToString(num);
                str6 = MTK_Common.decodeOut(str6);
                Console.WriteLine("DL_LoadScatter err" + str6);
            }
            Console.WriteLine("DL_LoadScatter 1 ret" + num);
            num = DL_AutoLoadRomImages(p_dl_handle2, Encoding.Default.GetBytes(s));
            if (num != 0)
            {
                string str7 = MTK_Common.StatusToString(num);
                str7 = MTK_Common.decodeOut(str7);
                Console.WriteLine("DL_AutoLoadRomImages err" + str7);
            }
            Console.WriteLine("DL_AutoLoadRomImages 1 ret" + num);
            num = DL_AddHandleToList(dl_handle_list, p_dl_handle);
            if (num != 0)
            {
                string str8 = MTK_Common.StatusToString(num);
                str8 = MTK_Common.decodeOut(str8);
                Console.WriteLine("DL_AddHandleToList err" + str8);
            }
            Console.WriteLine("DL_AddHandleToList 0 ret" + num);
            num = DL_AddHandleToList(dl_handle_list, p_dl_handle2);
            if (num != 0)
            {
                string str9 = MTK_Common.StatusToString(num);
                str9 = MTK_Common.decodeOut(str9);
                Console.WriteLine("DL_AddHandleToList err" + str9);
            }
            Console.WriteLine("DL_AddHandleToList 1 ret" + num);
        }

        public static void DLHandle()
        {
            int num = -1;
            num = DL_Create(ref g_dl_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_Create err" + str);
            }
            Console.WriteLine("DL_Create ret" + num);
            num = DL_SetChecksumLevel(g_dl_handle, 3);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("DL_SetChecksumLevel err" + str2);
            }
            Console.WriteLine("DL_SetChecksumLevel ret" + num);
        }

        public static void DLHandleDestroy()
        {
            int num = -1;
            num = DL_Rom_UnloadAll(g_dl_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_Rom_UnloadAll err" + str);
            }
            Console.WriteLine("DL_Rom_UnloadAll ret" + num);
            num = DL_Destroy(ref g_dl_handle);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("DL_Destroy err" + str2);
            }
            Console.WriteLine("DL_Destroy ret" + num);
        }

        public static void DLLoadScatter(int idx, string scat_file)
        {
            int num = -1;
            num = DL_LoadScatter(g_dl_handle, Encoding.Default.GetBytes(scat_file), null);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_LoadScatter err" + str);
            }
            Console.WriteLine("DL_LoadScatter ret" + num);
        }

        public static void DLAutoLoadRomImages(int idx, string scat_file)
        {
            int num = -1;
            num = DL_AutoLoadRomImages(g_dl_handle, Encoding.Default.GetBytes(scat_file));
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_AutoLoadRomImages err" + str);
            }
            Console.WriteLine("DL_AutoLoadRomImages ret" + num);
        }

        public static DL_PlatformInfo DLGetPlatfromInfo(int idx)
        {
            int num = -1;
            DL_PlatformInfo pPlatformInfo = default(DL_PlatformInfo);
            num = DL_GetPlatformInfo(g_dl_handle, ref pPlatformInfo);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_GetPlatformInfo err" + str);
            }
            Console.WriteLine("DL_GetPlatformInfo ret" + num);
            Console.WriteLine("vboytest plat_info.m_storage_type: " + pPlatformInfo.m_storage_type);
            return pPlatformInfo;
        }

        public static List<ROM_INFO> GetAllRomInfo(int idx)
        {
            List<ROM_INFO> list = new List<ROM_INFO>();
            int num = -1;
            ROM_INFO[] array = new ROM_INFO[128]; // Fixed: Specify the array size
            int num2 = Marshal.SizeOf(typeof(ROM_INFO)) * 128;
            Console.WriteLine("size: " + num2);
            IntPtr intPtr = Marshal.AllocHGlobal(num2);
            num = DL_GetCount(g_dl_handle, out var p_rom_count);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_GetCount err" + str);
            }
            Console.WriteLine("  " + num + " rom_count: " + p_rom_count);
            num = DL_Rom_GetInfoAll(g_dl_handle, intPtr, 128);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("DL_Rom_GetInfoAll err" + str2);
            }
            Console.WriteLine("DL_Rom_GetInfoAll " + num);
            for (int i = 0; i < p_rom_count; i++)
            {
                IntPtr ptr = new IntPtr(intPtr.ToInt64() + Marshal.SizeOf(typeof(ROM_INFO)) * i);
                array[i] = (ROM_INFO)Marshal.PtrToStructure(ptr, typeof(ROM_INFO));
                list.Add(new ROM_INFO
                {
                    name = array[i].name,
                    region_addr = array[i].region_addr,
                    addr_type = array[i].addr_type,
                    begin_addr = array[i].begin_addr,
                    end_addr = array[i].end_addr,
                    filepath = array[i].filepath,
                    filesize = array[i].filesize,
                    index = array[i].index,
                    rom_type = array[i].rom_type,
                    enable = array[i].enable,
                    item_is_visable = array[i].item_is_visable,
                    is_reserved = array[i].is_reserved,
                    operation_type = array[i].operation_type,
                    part_id = array[i].part_id,
                    partition_size = array[i].partition_size,
                    combo_partsize_check = array[i].combo_partsize_check
                });
            }
            Marshal.FreeHGlobal(intPtr);
            return list;
        }

        public static bool DLVerifyROMMemBuf(int idx, string scat_file, List<ROM_INFO> rom_info_list)
        {
            Console.WriteLine("DLVerifyROMMemBuf beging scat_file： " + scat_file);
            string text = new DirectoryInfo(scat_file).Parent.FullName + "\\Checksum.ini";
            if (File.Exists(text))
            {
                foreach (ROM_INFO item in rom_info_list)
                {
                    if (!item.item_is_visable)
                    {
                        Console.WriteLine("skip chksum for rom: " + item.name);
                        continue;
                    }
                    string value = IniFile.GetValue("CheckSum", item.name, "", text);
                    RomMemChecksumArg p_rom_mem_check_arg = default(RomMemChecksumArg);
                    RomMemChecksumResult p_rom_mem_check_result = default(RomMemChecksumResult);
                    p_rom_mem_check_arg.index = item.index;
                    int chksum = Convert.ToInt32(value, 16);
                    p_rom_mem_check_arg.chksum = (uint)chksum;
                    int num = DL_VerifyROMMemBuf(g_dl_handle, ref p_rom_mem_check_arg, ref p_rom_mem_check_result);
                    if (num == 0)
                    {
                        continue;
                    }
                    string str = MTK_Common.StatusToString(num);
                    str = MTK_Common.decodeOut(str);
                    Console.WriteLine("DL_VerifyROMMemBuf err" + str);
                    return false;
                }
                Console.WriteLine("DLVerifyROMMemBuf pass ");
                return true;
            }
            Console.WriteLine("checkSumPath: " + text + " not exist");
            return false;
        }

        public static void DLHandleList()
        {
            int num = -1;
            num = DL_CreateList(ref g_dl_handle_list);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_CreateList err" + str);
            }
            Console.WriteLine("DL_CreateList ret" + num);
        }

        public static void DLHandleListDestroy()
        {
            int num = -1;
            num = DL_DestroyList(ref g_dl_handle_list);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_DestroyList err" + str);
            }
            Console.WriteLine("DL_DestroyList ret" + num);
        }

        public static void DLAddHandleToList(int idx)
        {
            Console.WriteLine("DLAddHandleToList idx：" + idx);
            int num = -1;
            num = DL_AddHandleToList(g_dl_handle_list, g_dl_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DL_AddHandleToList err" + str);
            }
            Console.WriteLine("DL_AddHandleToList ret" + num);
        }
    }
}

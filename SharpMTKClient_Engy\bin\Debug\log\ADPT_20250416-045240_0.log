[0000001] [04:52:40:435110] [Tid0x000039e4] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [04:52:40:435110] [Tid0x000039e4] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [04:52:43:493325] [Tid0x00002fe4] [debug] -->[C2] DL_LoadScatter #(api.cpp, line:2554)
[0000004] [04:52:43:510233] [Tid0x00002fe4] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000005] [04:52:43:510233] [Tid0x00002fe4] [debug] <--[C2] DL_LoadScatter
[0000006] [04:52:43:511240] [Tid0x00002fe4] [debug] -->[C3] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000007] [04:52:43:520248] [Tid0x00002fe4] [debug] <--[C3] DL_AutoLoadRomImages
[0000008] [04:52:43:521484] [Tid0x00002fe4] [debug] -->[C4] DL_GetCount #(api.cpp, line:2696)
[0000009] [04:52:43:527696] [Tid0x00002fe4] [debug] <--[C4] DL_GetCount
[0000010] [04:52:43:527696] [Tid0x00002fe4] [debug] -->[C5] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000011] [04:52:43:527696] [Tid0x00002fe4] [debug] <--[C5] DL_Rom_GetInfoAll
[0000012] [04:53:50:166118] [Tid0x0000e898] [debug] -->[C6] FlashTool_Connect #(api.cpp, line:883)
[0000013] [04:53:50:166118] [Tid0x0000e898] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000014] [04:53:50:166118] [Tid0x0000e898] [debug] -->[C7] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000015] [04:53:50:166118] [Tid0x0000e898] [debug] bCheckScatter: 1
[0000016] [04:53:50:166118] [Tid0x0000e898] [debug] -->[C8] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000017] [04:53:50:166118] [Tid0x0000e898] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000018] [04:53:50:166118] [Tid0x0000e898] [debug] have load scatter already #(api.cpp, line:1943)
[0000019] [04:53:50:166118] [Tid0x0000e898] [debug] libversion 2 #(api.cpp, line:1960)
[0000020] [04:53:50:166118] [Tid0x0000e898] [debug] -->[C9] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000021] [04:53:50:168117] [Tid0x0000c54c] [debug] -->[C10] FlashTool_Connect #(api.cpp, line:883)
[0000022] [04:53:50:168117] [Tid0x0000c54c] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000023] [04:53:50:168117] [Tid0x0000c54c] [debug] -->[C11] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000024] [04:53:50:168117] [Tid0x0000c54c] [debug] bCheckScatter: 1
[0000025] [04:53:50:168117] [Tid0x0000c54c] [debug] -->[C12] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000026] [04:53:50:168117] [Tid0x0000c54c] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000027] [04:53:50:168117] [Tid0x0000c54c] [debug] have load scatter already #(api.cpp, line:1943)
[0000028] [04:53:50:168117] [Tid0x0000c54c] [debug] libversion 2 #(api.cpp, line:1960)
[0000029] [04:53:50:168117] [Tid0x0000c54c] [debug] -->[C13] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000030] [04:53:50:734040] [Tid0x0000e898] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2554)
[0000031] [04:53:50:734040] [Tid0x0000e898] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000032] [04:53:50:735040] [Tid0x0000e898] [debug] callback in brom stage #(cflashtool_api.cpp, line:1209)
[0000033] [04:53:50:735040] [Tid0x0000e898] [debug] m_cb_in_brom_stage run success #(cflashtool_api.cpp, line:1217)
[0000034] [04:53:50:735040] [Tid0x0000e898] [debug] connect_brom_ex OK #(cflashtool_api.cpp, line:1223)
[0000035] [04:53:50:735040] [Tid0x0000e898] [debug] <--[C9] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000036] [04:53:50:735040] [Tid0x0000e898] [debug] <--[C8] FlashTool_Connect_BROM_Ex
[0000037] [04:53:50:735040] [Tid0x0000e898] [debug] bRealCheckScatter: 1 #(api.cpp, line:968)
[0000038] [04:53:50:735040] [Tid0x0000e898] [debug] -->[C14] FlashTool_Connect_Download_DA #(api.cpp, line:2071)
[0000039] [04:53:50:735040] [Tid0x0000e898] [debug] bCheckScatter: 1 #(api.cpp, line:2072)
[0000040] [04:53:50:735040] [Tid0x0000e898] [debug] -->[C15] cflashtool_api::FlashTool_Connect_Download_DA #(cflashtool_api.cpp, line:1384)
[0000041] [04:53:50:735040] [Tid0x0000e898] [debug] bCheckScatter: 1 #(cflashtool_api.cpp, line:1385)
[0000042] [04:53:50:736039] [Tid0x0000e898] [debug] da_source type: 0 #(cflashtool_api.cpp, line:1414)
[0000043] [04:53:50:736039] [Tid0x0000e898] [debug] checksum_level: 3,  battery_setting: 0, reset_key_setting: 0 #(cflashtool_api.cpp, line:1528)
[0000044] [04:53:50:736039] [Tid0x0000e898] [debug] connect_da_end_stage: 2,  enable_dram_in_1st_da: 0, da_log_level: 2, da_log_channel: 1, ufs_provision: 63347300 #(cflashtool_api.cpp, line:1531)
[0000045] [04:53:51:411055] [Tid0x0000c54c] [debug] <--[C13] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000046] [04:53:51:411055] [Tid0x0000c54c] [debug] <--[C12] FlashTool_Connect_BROM_Ex
[0000047] [04:53:51:411055] [Tid0x0000c54c] [debug] <--[C11] FlashTool_Connect_Ex
[0000048] [04:53:51:411055] [Tid0x0000c54c] [debug] <--[C10] FlashTool_Connect
[0000049] [04:53:51:412060] [Tid0x0000c54c] [debug] -->[C16] FlashTool_Disconnect #(api.cpp, line:1033)
[0000050] [04:53:51:412060] [Tid0x0000c54c] [debug] -->[C17] cflashtool_api::FlashTool_Disconnect #(cflashtool_api.cpp, line:33)
[0000051] [04:53:51:413058] [Tid0x0000c54c] [debug] -->[C18] DL_ClearFTHandle #(api.cpp, line:2618)
[0000052] [04:53:51:413058] [Tid0x0000c54c] [debug] <--[C18] DL_ClearFTHandle
[0000053] [04:53:51:413058] [Tid0x0000c54c] [debug] <--[C17] cflashtool_api::FlashTool_Disconnect
[0000054] [04:53:51:413058] [Tid0x0000c54c] [debug] <--[C16] FlashTool_Disconnect

[00000001] [13:25:32:753447] [Tid0x0000a6fc] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [13:25:32:754452] [Tid0x0000a6fc] [info] create new hsession 0xb001180 #(kernel.cpp, line:92)
[00000005] [13:25:32:754452] [Tid0x0000a6fc] [debug] <--[C3] kernel::create_new_session
[00000006] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [13:25:32:754452] [Tid0x0000a6fc] [debug] <--[C4] boot_rom::boot_rom
[00000008] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [13:25:32:754452] [Tid0x0000a6fc] [debug] <--[C6] device_log_source::device_log_source
[00000011] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [13:25:32:754452] [Tid0x0000a6fc] [debug] <--[C7] data_mux::data_mux
[00000013] [13:25:32:754452] [Tid0x0000a6fc] [debug] <--[C5] device_instance::device_instance
[00000014] [13:25:32:754452] [Tid0x0000a6fc] [debug] <--[C2] connection::create_session
[00000015] [13:25:32:754452] [Tid0x0000a6fc] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [13:25:32:754452] [Tid0x0000a6fc] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [13:25:32:754452] [Tid0x0000a6fc] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [13:25:32:754452] [Tid0x0000a6fc] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [13:25:32:755451] [Tid0x0000a6fc] [debug] <--[C11] is_valid_ip
[00000022] [13:25:32:755451] [Tid0x0000a6fc] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [13:25:32:755451] [Tid0x0000a6fc] [debug] <--[C12] is_lge_impl
[00000024] [13:25:32:755451] [Tid0x0000a6fc] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [13:25:32:755451] [Tid0x0000a6fc] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [13:25:32:755451] [Tid0x0000a6fc] [debug] <--[C13] lib_config_parser::get_value
[00000027] [13:25:32:755451] [Tid0x0000a6fc] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [13:25:32:755451] [Tid0x0000a6fc] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [13:25:32:755451] [Tid0x0000a6fc] [info] try to open device: COM5 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [13:25:33:186311] [Tid0x0000a6fc] [info] COM5 open complete. #(comm_engine.cpp, line:168)
[00000031] [13:25:33:186311] [Tid0x0000a6fc] [info] <--[C14] comm_engine::open
[00000032] [13:25:33:186311] [Tid0x0000a6fc] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000033] [13:25:33:186311] [Tid0x0000a6fc] [debug] <--[C15] boot_rom::set_transfer_channel
[00000034] [13:25:33:186311] [Tid0x0000a6fc] [debug] -->[C16] boot_rom::connect #(boot_rom.cpp, line:47)
[00000035] [13:25:33:186311] [Tid0x0000a6fc] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000036] [13:25:33:186311] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000037] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000038] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000039] [13:25:33:206836] [Tid0x0000a6fc] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000040] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000041] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000042] [13:25:33:206836] [Tid0x0000a6fc] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000043] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000044] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000045] [13:25:33:206836] [Tid0x0000a6fc] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000046] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000047] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000048] [13:25:33:206836] [Tid0x0000a6fc] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000049] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000050] [13:25:33:206836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000051] [13:25:33:206836] [Tid0x0000a6fc] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000052] [13:25:33:206836] [Tid0x0000a6fc] [debug] <--[C16] boot_rom::connect
[00000053] [13:25:33:206836] [Tid0x0000a6fc] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000054] [13:25:33:206836] [Tid0x0000a6fc] [debug] -->[C28] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000055] [13:25:33:207837] [Tid0x0000a6fc] [debug] -->[C29] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000056] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000057] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000058] [13:25:33:207837] [Tid0x0000a6fc] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000059] [13:25:33:207837] [Tid0x0000a6fc] [debug] <--[C29] boot_rom::get_preloader_version
[00000060] [13:25:33:207837] [Tid0x0000a6fc] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000061] [13:25:33:207837] [Tid0x0000a6fc] [debug] <--[C28] boot_rom_logic::security_verify_connection
[00000062] [13:25:33:207837] [Tid0x0000a6fc] [debug] <--[C9] connection::connect_brom
[00000063] [13:25:33:207837] [Tid0x0000a6fc] [info] <--[C8] flashtool_connect_brom
[00000064] [13:25:33:207837] [Tid0x0000a6fc] [info] -->[C32] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000065] [13:25:33:207837] [Tid0x0000a6fc] [debug] -->[C33] connection::device_control #(connection.cpp, line:669)
[00000066] [13:25:33:207837] [Tid0x0000a6fc] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000067] [13:25:33:207837] [Tid0x0000a6fc] [debug] -->[C34] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000068] [13:25:33:207837] [Tid0x0000a6fc] [debug] -->[C35] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000069] [13:25:33:207837] [Tid0x0000a6fc] [info] get chip id  #(boot_rom.cpp, line:114)
[00000070] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000071] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000072] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000073] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000074] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000075] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000076] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000077] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000078] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000079] [13:25:33:207837] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000080] [13:25:33:207837] [Tid0x0000a6fc] [debug] -->[C46] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000081] [13:25:33:208836] [Tid0x0000a6fc] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000082] [13:25:33:208836] [Tid0x0000a6fc] [debug] <--[C46] lib_config_parser::get_value
[00000083] [13:25:33:208836] [Tid0x0000a6fc] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000084] [13:25:33:208836] [Tid0x0000a6fc] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000085] [13:25:33:208836] [Tid0x0000a6fc] [debug] <--[C35] boot_rom::get_chip_id
[00000086] [13:25:33:208836] [Tid0x0000a6fc] [debug] <--[C34] boot_rom::device_control
[00000087] [13:25:33:208836] [Tid0x0000a6fc] [debug] <--[C33] connection::device_control
[00000088] [13:25:33:208836] [Tid0x0000a6fc] [info] <--[C32] flashtool_device_control
[00000089] [13:25:33:208836] [Tid0x0000a6fc] [info] -->[C47] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000090] [13:25:33:208836] [Tid0x0000a6fc] [debug] -->[C48] connection::device_control #(connection.cpp, line:669)
[00000091] [13:25:33:208836] [Tid0x0000a6fc] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000092] [13:25:33:208836] [Tid0x0000a6fc] [debug] -->[C49] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000093] [13:25:33:208836] [Tid0x0000a6fc] [debug] -->[C50] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000094] [13:25:33:208836] [Tid0x0000a6fc] [info] get chip id  #(boot_rom.cpp, line:114)
[00000095] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000096] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000097] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000098] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000099] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000100] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000101] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000102] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000103] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000104] [13:25:33:208836] [Tid0x0000a6fc] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000105] [13:25:33:208836] [Tid0x0000a6fc] [debug] -->[C61] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000106] [13:25:33:208836] [Tid0x0000a6fc] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000107] [13:25:33:209836] [Tid0x0000a6fc] [debug] <--[C61] lib_config_parser::get_value
[00000108] [13:25:33:209836] [Tid0x0000a6fc] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000109] [13:25:33:209836] [Tid0x0000a6fc] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000110] [13:25:33:209836] [Tid0x0000a6fc] [debug] <--[C50] boot_rom::get_chip_id
[00000111] [13:25:33:209836] [Tid0x0000a6fc] [debug] <--[C49] boot_rom::device_control
[00000112] [13:25:33:209836] [Tid0x0000a6fc] [debug] <--[C48] connection::device_control
[00000113] [13:25:33:209836] [Tid0x0000a6fc] [info] <--[C47] flashtool_device_control
[00000114] [13:25:33:209836] [Tid0x0000a6fc] [error] Not Support platform: , type:  #(chip_mapping.cpp, line:229)
[00000115] [13:25:33:209836] [Tid0x0000a6fc] [info] -->[C62] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000116] [13:25:33:209836] [Tid0x0000a6fc] [debug] -->[C64] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000117] [13:25:33:209836] [Tid0x0000a6fc] [info] -->[C65] device_log_source::stop #(device_log_source.cpp, line:29)
[00000118] [13:25:33:209836] [Tid0x0000a6fc] [info] <--[C65] device_log_source::stop
[00000119] [13:25:33:209836] [Tid0x0000a6fc] [info] -->[C66] data_mux::stop #(data_mux.cpp, line:92)
[00000120] [13:25:33:209836] [Tid0x0000a6fc] [info] <--[C66] data_mux::stop
[00000121] [13:25:33:209836] [Tid0x0000a6fc] [debug] <--[C64] device_instance::~device_instance
[00000122] [13:25:33:209836] [Tid0x0000a6fc] [info] -->[C67] comm_engine::close #(comm_engine.cpp, line:382)
[00000123] [13:25:33:209836] [Tid0x0000a6fc] [debug] -->[C68] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000124] [13:25:33:209836] [Tid0x0000a6fc] [debug] <--[C68] comm_engine::cancel
[00000125] [13:25:33:212342] [Tid0x0000a6fc] [info] <--[C67] comm_engine::close
[00000126] [13:25:33:212342] [Tid0x0000a6fc] [info] delete hsession 0xb001180 #(kernel.cpp, line:102)
[00000127] [13:25:33:212342] [Tid0x0000a6fc] [info] <--[C62] flashtool_destroy_session

[00000001] [19:29:59:135200] [Tid0x0000eca0] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [19:29:59:135200] [Tid0x0000eca0] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [19:29:59:135200] [Tid0x0000eca0] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [19:29:59:135200] [Tid0x0000eca0] [info] create new hsession 0xa88e920 #(kernel.cpp, line:92)
[00000005] [19:29:59:135200] [Tid0x0000eca0] [debug] <--[C3] kernel::create_new_session
[00000006] [19:29:59:135200] [Tid0x0000eca0] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [19:29:59:135200] [Tid0x0000eca0] [debug] <--[C4] boot_rom::boot_rom
[00000008] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C6] device_log_source::device_log_source
[00000011] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C7] data_mux::data_mux
[00000013] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C5] device_instance::device_instance
[00000014] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C2] connection::create_session
[00000015] [19:29:59:142225] [Tid0x0000eca0] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [19:29:59:142225] [Tid0x0000eca0] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [19:29:59:142225] [Tid0x0000eca0] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C11] is_valid_ip
[00000022] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C12] is_lge_impl
[00000024] [19:29:59:142225] [Tid0x0000eca0] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [19:29:59:142225] [Tid0x0000eca0] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C13] lib_config_parser::get_value
[00000027] [19:29:59:142225] [Tid0x0000eca0] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [19:29:59:142225] [Tid0x0000eca0] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [19:29:59:142225] [Tid0x0000eca0] [info] try to open device: COM13 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [19:29:59:143229] [Tid0x0000eca0] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000031] [19:29:59:143229] [Tid0x0000eca0] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000032] [19:29:59:472583] [Tid0x0000eca0] [info] COM13 open complete. #(comm_engine.cpp, line:168)
[00000033] [19:29:59:472583] [Tid0x0000eca0] [info] <--[C14] comm_engine::open
[00000034] [19:29:59:472583] [Tid0x0000eca0] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000035] [19:29:59:472583] [Tid0x0000eca0] [debug] <--[C15] boot_rom::set_transfer_channel
[00000036] [19:29:59:472583] [Tid0x0000eca0] [debug] -->[C16] boot_rom::connect #(boot_rom.cpp, line:47)
[00000037] [19:29:59:472583] [Tid0x0000eca0] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000038] [19:29:59:472583] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000039] [19:29:59:473575] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000040] [19:29:59:473575] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000041] [19:29:59:473575] [Tid0x0000eca0] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000042] [19:29:59:473575] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000043] [19:29:59:492578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000044] [19:29:59:492578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000045] [19:29:59:493578] [Tid0x0000eca0] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000046] [19:29:59:493578] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000047] [19:29:59:493578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000048] [19:29:59:493578] [Tid0x0000eca0] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000049] [19:29:59:493578] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000050] [19:29:59:493578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000051] [19:29:59:493578] [Tid0x0000eca0] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000052] [19:29:59:493578] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000053] [19:29:59:493578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000054] [19:29:59:493578] [Tid0x0000eca0] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000055] [19:29:59:493578] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000056] [19:29:59:493578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000057] [19:29:59:493578] [Tid0x0000eca0] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000058] [19:29:59:493578] [Tid0x0000eca0] [debug] <--[C16] boot_rom::connect
[00000059] [19:29:59:493578] [Tid0x0000eca0] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000060] [19:29:59:493578] [Tid0x0000eca0] [debug] -->[C31] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000061] [19:29:59:493578] [Tid0x0000eca0] [debug] -->[C32] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000062] [19:29:59:493578] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000063] [19:29:59:493578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000064] [19:29:59:493578] [Tid0x0000eca0] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000065] [19:29:59:493578] [Tid0x0000eca0] [debug] <--[C32] boot_rom::get_preloader_version
[00000066] [19:29:59:493578] [Tid0x0000eca0] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000067] [19:29:59:493578] [Tid0x0000eca0] [debug] <--[C31] boot_rom_logic::security_verify_connection
[00000068] [19:29:59:493578] [Tid0x0000eca0] [debug] <--[C9] connection::connect_brom
[00000069] [19:29:59:493578] [Tid0x0000eca0] [info] <--[C8] flashtool_connect_brom
[00000070] [19:29:59:493578] [Tid0x0000eca0] [info] -->[C35] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000071] [19:29:59:494578] [Tid0x0000eca0] [debug] -->[C36] connection::device_control #(connection.cpp, line:669)
[00000072] [19:29:59:494578] [Tid0x0000eca0] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000073] [19:29:59:494578] [Tid0x0000eca0] [debug] -->[C37] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000074] [19:29:59:494578] [Tid0x0000eca0] [debug] -->[C38] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000075] [19:29:59:494578] [Tid0x0000eca0] [info] get chip id  #(boot_rom.cpp, line:114)
[00000076] [19:29:59:494578] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000077] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000078] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000079] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000080] [19:29:59:494578] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000081] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000082] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000083] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000084] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000085] [19:29:59:494578] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000086] [19:29:59:494578] [Tid0x0000eca0] [debug] -->[C49] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000087] [19:29:59:494578] [Tid0x0000eca0] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000088] [19:29:59:494578] [Tid0x0000eca0] [debug] <--[C49] lib_config_parser::get_value
[00000089] [19:29:59:494578] [Tid0x0000eca0] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000090] [19:29:59:495579] [Tid0x0000eca0] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000091] [19:29:59:495579] [Tid0x0000eca0] [debug] <--[C38] boot_rom::get_chip_id
[00000092] [19:29:59:495579] [Tid0x0000eca0] [debug] <--[C37] boot_rom::device_control
[00000093] [19:29:59:495579] [Tid0x0000eca0] [debug] <--[C36] connection::device_control
[00000094] [19:29:59:495579] [Tid0x0000eca0] [info] <--[C35] flashtool_device_control
[00000095] [19:29:59:495579] [Tid0x0000eca0] [info] -->[C50] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000096] [19:29:59:495579] [Tid0x0000eca0] [debug] -->[C51] connection::device_control #(connection.cpp, line:669)
[00000097] [19:29:59:495579] [Tid0x0000eca0] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000098] [19:29:59:495579] [Tid0x0000eca0] [debug] -->[C52] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000099] [19:29:59:495579] [Tid0x0000eca0] [debug] -->[C53] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000100] [19:29:59:495579] [Tid0x0000eca0] [info] get chip id  #(boot_rom.cpp, line:114)
[00000101] [19:29:59:495579] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000102] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000103] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000104] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000105] [19:29:59:495579] [Tid0x0000eca0] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000106] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000107] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000108] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000109] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000110] [19:29:59:495579] [Tid0x0000eca0] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000111] [19:29:59:495579] [Tid0x0000eca0] [debug] -->[C64] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000112] [19:29:59:495579] [Tid0x0000eca0] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000113] [19:29:59:495579] [Tid0x0000eca0] [debug] <--[C64] lib_config_parser::get_value
[00000114] [19:29:59:495579] [Tid0x0000eca0] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000115] [19:29:59:496579] [Tid0x0000eca0] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000116] [19:29:59:496579] [Tid0x0000eca0] [debug] <--[C53] boot_rom::get_chip_id
[00000117] [19:29:59:496579] [Tid0x0000eca0] [debug] <--[C52] boot_rom::device_control
[00000118] [19:29:59:496579] [Tid0x0000eca0] [debug] <--[C51] connection::device_control
[00000119] [19:29:59:496579] [Tid0x0000eca0] [info] <--[C50] flashtool_device_control
[00000120] [19:29:59:496579] [Tid0x0000eca0] [error] Not Support platform: , type:  #(chip_mapping.cpp, line:229)
[00000121] [19:29:59:496579] [Tid0x0000eca0] [info] -->[C65] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000122] [19:29:59:496579] [Tid0x0000eca0] [debug] -->[C67] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000123] [19:29:59:496579] [Tid0x0000eca0] [info] -->[C68] device_log_source::stop #(device_log_source.cpp, line:29)
[00000124] [19:29:59:496579] [Tid0x0000eca0] [info] <--[C68] device_log_source::stop
[00000125] [19:29:59:496579] [Tid0x0000eca0] [info] -->[C69] data_mux::stop #(data_mux.cpp, line:92)
[00000126] [19:29:59:496579] [Tid0x0000eca0] [info] <--[C69] data_mux::stop
[00000127] [19:29:59:496579] [Tid0x0000eca0] [debug] <--[C67] device_instance::~device_instance
[00000128] [19:29:59:496579] [Tid0x0000eca0] [info] -->[C70] comm_engine::close #(comm_engine.cpp, line:382)
[00000129] [19:29:59:496579] [Tid0x0000eca0] [debug] -->[C71] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000130] [19:29:59:496579] [Tid0x0000eca0] [debug] <--[C71] comm_engine::cancel
[00000131] [19:29:59:509646] [Tid0x0000eca0] [info] <--[C70] comm_engine::close
[00000132] [19:29:59:509646] [Tid0x0000eca0] [info] delete hsession 0xa88e920 #(kernel.cpp, line:102)
[00000133] [19:29:59:509646] [Tid0x0000eca0] [info] <--[C65] flashtool_destroy_session
[00000134] [19:30:05:730510] [Tid0x0000eca0] [info] -->[C72] flashtool_shutdown_device #(flashtoolex_api.cpp, line:149)
[00000135] [19:30:05:730510] [Tid0x0000eca0] [debug] -->[C73] connection::shutdown_device #(connection.cpp, line:989)
[00000136] [19:30:05:730510] [Tid0x0000eca0] [error] invalid session. #(connection.cpp, line:994)
[00000137] [19:30:05:730510] [Tid0x0000eca0] [debug] <--[C73] connection::shutdown_device
[00000138] [19:30:05:730510] [Tid0x0000eca0] [error] <ERR_CHECKPOINT>[811][error][0xc001000a]</ERR_CHECKPOINT>flashtool_shutdown_device fail #(flashtoolex_api.cpp, line:154)
[00000139] [19:30:05:730510] [Tid0x0000eca0] [info] <--[C72] flashtool_shutdown_device
[00000140] [19:30:05:730510] [Tid0x0000eca0] [info] -->[C74] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000141] [19:30:05:730510] [Tid0x0000eca0] [info] <--[C74] flashtool_destroy_session

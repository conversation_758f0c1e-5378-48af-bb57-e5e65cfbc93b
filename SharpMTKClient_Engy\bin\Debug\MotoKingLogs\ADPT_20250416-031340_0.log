[0000001] [03:13:40:581488] [Tid0x00004d70] [debug] -->[C1] DL_LoadScatter #(api.cpp, line:2554)
[0000002] [03:13:40:600082] [Tid0x00004d70] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000003] [03:13:40:600082] [Tid0x00004d70] [debug] <--[C1] DL_LoadScatter
[0000004] [03:13:40:601074] [Tid0x00004d70] [debug] -->[C2] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000005] [03:13:40:609589] [Tid0x00004d70] [debug] <--[C2] DL_AutoLoadRomImages
[0000006] [03:13:40:610587] [Tid0x00004d70] [debug] -->[C3] DL_GetCount #(api.cpp, line:2696)
[0000007] [03:13:40:610587] [Tid0x00004d70] [debug] <--[C3] DL_GetCount
[0000008] [03:13:40:610587] [Tid0x00004d70] [debug] -->[C4] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000009] [03:13:40:610587] [Tid0x00004d70] [debug] <--[C4] DL_Rom_GetInfoAll
[0000010] [03:13:40:611596] [Tid0x00004d70] [debug] -->[C5] DL_GetScatterInfo #(api.cpp, line:2797)
[0000011] [03:13:40:611596] [Tid0x00004d70] [debug] <--[C5] DL_GetScatterInfo
[0000012] [03:16:18:541396] [Tid0x00004d70] [debug] -->[C6] DL_Rom_UnloadAll #(api.cpp, line:2904)
[0000013] [03:16:18:541396] [Tid0x00004d70] [debug] <--[C6] DL_Rom_UnloadAll
[0000014] [03:16:18:542398] [Tid0x00004d70] [debug] -->[C7] DL_LoadScatter #(api.cpp, line:2554)
[0000015] [03:16:18:559355] [Tid0x00004d70] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000016] [03:16:18:559355] [Tid0x00004d70] [debug] <--[C7] DL_LoadScatter
[0000017] [03:16:18:559355] [Tid0x00004d70] [debug] -->[C8] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000018] [03:16:18:572988] [Tid0x00004d70] [debug] <--[C8] DL_AutoLoadRomImages
[0000019] [03:16:18:572988] [Tid0x00004d70] [debug] -->[C9] DL_GetCount #(api.cpp, line:2696)
[0000020] [03:16:18:572988] [Tid0x00004d70] [debug] <--[C9] DL_GetCount
[0000021] [03:16:18:572988] [Tid0x00004d70] [debug] -->[C10] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000022] [03:16:18:572988] [Tid0x00004d70] [debug] <--[C10] DL_Rom_GetInfoAll
[0000023] [03:16:18:576978] [Tid0x00004d70] [debug] -->[C11] DL_GetScatterInfo #(api.cpp, line:2797)
[0000024] [03:16:18:576978] [Tid0x00004d70] [debug] <--[C11] DL_GetScatterInfo
[0000025] [05:01:36:163773] [Tid0x00004d70] [debug] -->[C12] DL_Rom_UnloadAll #(api.cpp, line:2904)
[0000026] [05:01:36:163773] [Tid0x00004d70] [debug] <--[C12] DL_Rom_UnloadAll
[0000027] [05:01:36:164765] [Tid0x00004d70] [debug] -->[C13] DL_LoadScatter #(api.cpp, line:2554)
[0000028] [05:01:36:179787] [Tid0x00004d70] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000029] [05:01:36:179787] [Tid0x00004d70] [debug] <--[C13] DL_LoadScatter
[0000030] [05:01:36:180797] [Tid0x00004d70] [debug] -->[C14] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000031] [05:01:36:190879] [Tid0x00004d70] [debug] <--[C14] DL_AutoLoadRomImages
[0000032] [05:01:36:191872] [Tid0x00004d70] [debug] -->[C15] DL_GetCount #(api.cpp, line:2696)
[0000033] [05:01:36:191872] [Tid0x00004d70] [debug] <--[C15] DL_GetCount
[0000034] [05:01:36:191872] [Tid0x00004d70] [debug] -->[C16] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000035] [05:01:36:191872] [Tid0x00004d70] [debug] <--[C16] DL_Rom_GetInfoAll
[0000036] [05:01:36:193871] [Tid0x00004d70] [debug] -->[C17] DL_GetScatterInfo #(api.cpp, line:2797)
[0000037] [05:01:36:193871] [Tid0x00004d70] [debug] <--[C17] DL_GetScatterInfo
[0000038] [05:01:48:323686] [Tid0x00008b74] [debug] -->[C18] DL_Rom_UnloadAll #(api.cpp, line:2904)
[0000039] [05:01:48:324694] [Tid0x00008b74] [debug] <--[C18] DL_Rom_UnloadAll
[0000040] [05:01:48:324694] [Tid0x00008b74] [debug] -->[C19] DL_LoadScatter #(api.cpp, line:2554)
[0000041] [05:01:48:340714] [Tid0x00008b74] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000042] [05:01:48:340714] [Tid0x00008b74] [debug] <--[C19] DL_LoadScatter
[0000043] [05:01:48:340714] [Tid0x00008b74] [debug] -->[C20] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000044] [05:01:48:348714] [Tid0x00008b74] [debug] <--[C20] DL_AutoLoadRomImages
[0000045] [05:01:48:348714] [Tid0x00008b74] [debug] -->[C21] DL_GetCount #(api.cpp, line:2696)
[0000046] [05:01:48:348714] [Tid0x00008b74] [debug] <--[C21] DL_GetCount
[0000047] [05:01:48:348714] [Tid0x00008b74] [debug] -->[C22] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000048] [05:01:48:348714] [Tid0x00008b74] [debug] <--[C22] DL_Rom_GetInfoAll
[0000049] [05:01:48:351785] [Tid0x00008b74] [debug] -->[C23] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000050] [05:01:48:351785] [Tid0x00008b74] [debug] <--[C23] DL_SetChecksumLevel
[0000051] [05:01:48:356949] [Tid0x00008b74] [debug] -->[C24] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000052] [05:01:48:356949] [Tid0x00008b74] [debug] <--[C24] DL_Rom_SetEnableAttr
[0000053] [05:01:48:360453] [Tid0x00008b74] [debug] -->[C25] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000054] [05:01:48:360453] [Tid0x00008b74] [debug] <--[C25] DL_Rom_SetEnableAttr
[0000055] [05:01:48:362453] [Tid0x00008b74] [debug] -->[C26] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000056] [05:01:48:362453] [Tid0x00008b74] [debug] <--[C26] DL_Rom_SetEnableAttr
[0000057] [05:01:48:365453] [Tid0x00008b74] [debug] -->[C27] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000058] [05:01:48:365453] [Tid0x00008b74] [debug] <--[C27] DL_Rom_SetEnableAttr
[0000059] [05:01:48:367455] [Tid0x00008b74] [debug] -->[C28] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000060] [05:01:48:367455] [Tid0x00008b74] [debug] <--[C28] DL_Rom_SetEnableAttr
[0000061] [05:01:48:369842] [Tid0x00008b74] [debug] -->[C29] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000062] [05:01:48:369842] [Tid0x00008b74] [debug] <--[C29] DL_Rom_SetEnableAttr
[0000063] [05:01:48:372842] [Tid0x00008b74] [debug] -->[C30] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000064] [05:01:48:372842] [Tid0x00008b74] [debug] <--[C30] DL_Rom_SetEnableAttr
[0000065] [05:01:48:374843] [Tid0x00008b74] [debug] -->[C31] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000066] [05:01:48:374843] [Tid0x00008b74] [debug] <--[C31] DL_Rom_SetEnableAttr
[0000067] [05:01:48:376842] [Tid0x00008b74] [debug] -->[C32] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000068] [05:01:48:376842] [Tid0x00008b74] [debug] <--[C32] DL_Rom_SetEnableAttr
[0000069] [05:01:48:380347] [Tid0x00008b74] [debug] -->[C33] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000070] [05:01:48:380347] [Tid0x00008b74] [debug] <--[C33] DL_Rom_SetEnableAttr
[0000071] [05:01:48:383347] [Tid0x00008b74] [debug] -->[C34] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000072] [05:01:48:383347] [Tid0x00008b74] [debug] <--[C34] DL_Rom_SetEnableAttr
[0000073] [05:01:48:385347] [Tid0x00008b74] [debug] -->[C35] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000074] [05:01:48:385347] [Tid0x00008b74] [debug] <--[C35] DL_Rom_SetEnableAttr
[0000075] [05:01:48:388347] [Tid0x00008b74] [debug] -->[C36] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000076] [05:01:48:388347] [Tid0x00008b74] [debug] <--[C36] DL_Rom_SetEnableAttr
[0000077] [05:01:48:390401] [Tid0x00008b74] [debug] -->[C37] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000078] [05:01:48:390401] [Tid0x00008b74] [debug] <--[C37] DL_Rom_SetEnableAttr
[0000079] [05:01:48:393401] [Tid0x00008b74] [debug] -->[C38] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000080] [05:01:48:393401] [Tid0x00008b74] [debug] <--[C38] DL_Rom_SetEnableAttr
[0000081] [05:01:48:395393] [Tid0x00008b74] [debug] -->[C39] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000082] [05:01:48:395393] [Tid0x00008b74] [debug] <--[C39] DL_Rom_SetEnableAttr
[0000083] [05:01:48:398393] [Tid0x00008b74] [debug] -->[C40] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000084] [05:01:48:398393] [Tid0x00008b74] [debug] <--[C40] DL_Rom_SetEnableAttr
[0000085] [05:01:48:400810] [Tid0x00008b74] [debug] -->[C41] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000086] [05:01:48:400810] [Tid0x00008b74] [debug] <--[C41] DL_Rom_SetEnableAttr
[0000087] [05:01:48:403809] [Tid0x00008b74] [debug] -->[C42] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000088] [05:01:48:403809] [Tid0x00008b74] [debug] <--[C42] DL_Rom_SetEnableAttr
[0000089] [05:01:48:406809] [Tid0x00008b74] [debug] -->[C43] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000090] [05:01:48:406809] [Tid0x00008b74] [debug] <--[C43] DL_Rom_SetEnableAttr
[0000091] [05:01:48:409314] [Tid0x00008b74] [debug] -->[C44] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000092] [05:01:48:409314] [Tid0x00008b74] [debug] <--[C44] DL_Rom_SetEnableAttr
[0000093] [05:01:48:409314] [Tid0x00008b74] [debug] -->[C45] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000094] [05:01:48:409314] [Tid0x00008b74] [debug] <--[C45] DL_Rom_SetEnableAttr
[0000095] [05:01:48:410314] [Tid0x00008b74] [debug] -->[C46] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000096] [05:01:48:410314] [Tid0x00008b74] [debug] <--[C46] DL_Rom_SetEnableAttr
[0000097] [05:01:48:410314] [Tid0x00008b74] [debug] -->[C47] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000098] [05:01:48:410314] [Tid0x00008b74] [debug] <--[C47] DL_Rom_SetEnableAttr
[0000099] [05:01:48:411314] [Tid0x00008b74] [debug] -->[C48] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000100] [05:01:48:411314] [Tid0x00008b74] [debug] <--[C48] DL_Rom_SetEnableAttr
[0000101] [05:01:48:412314] [Tid0x00008b74] [debug] -->[C49] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000102] [05:01:48:412314] [Tid0x00008b74] [debug] <--[C49] DL_Rom_SetEnableAttr
[0000103] [05:01:48:413315] [Tid0x00008b74] [debug] -->[C50] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000104] [05:01:48:413315] [Tid0x00008b74] [debug] <--[C50] DL_Rom_SetEnableAttr
[0000105] [05:01:48:415314] [Tid0x00008b74] [debug] -->[C51] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000106] [05:01:48:415314] [Tid0x00008b74] [debug] <--[C51] DL_Rom_SetEnableAttr
[0000107] [05:01:48:416315] [Tid0x00008b74] [debug] -->[C52] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000108] [05:01:48:416315] [Tid0x00008b74] [debug] <--[C52] DL_Rom_SetEnableAttr
[0000109] [05:01:48:418314] [Tid0x00008b74] [debug] -->[C53] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000110] [05:01:48:418314] [Tid0x00008b74] [debug] <--[C53] DL_Rom_SetEnableAttr
[0000111] [05:01:48:420763] [Tid0x00008b74] [debug] -->[C54] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000112] [05:01:48:420763] [Tid0x00008b74] [debug] <--[C54] DL_Rom_SetEnableAttr
[0000113] [05:01:48:422761] [Tid0x00008b74] [debug] -->[C55] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000114] [05:01:48:422761] [Tid0x00008b74] [debug] <--[C55] DL_Rom_SetEnableAttr
[0000115] [05:01:48:424763] [Tid0x00008b74] [debug] -->[C56] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000116] [05:01:48:424763] [Tid0x00008b74] [debug] <--[C56] DL_Rom_SetEnableAttr
[0000117] [05:01:48:426761] [Tid0x00008b74] [debug] -->[C57] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000118] [05:01:48:426761] [Tid0x00008b74] [debug] <--[C57] DL_Rom_SetEnableAttr
[0000119] [05:01:48:428763] [Tid0x00008b74] [debug] -->[C58] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000120] [05:01:48:428763] [Tid0x00008b74] [debug] <--[C58] DL_Rom_SetEnableAttr
[0000121] [05:01:48:431928] [Tid0x00008b74] [debug] -->[C59] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000122] [05:01:48:431928] [Tid0x00008b74] [debug] <--[C59] DL_Rom_SetEnableAttr
[0000123] [05:01:48:434927] [Tid0x00008b74] [debug] -->[C60] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000124] [05:01:48:434927] [Tid0x00008b74] [debug] <--[C60] DL_Rom_SetEnableAttr
[0000125] [05:01:48:436928] [Tid0x00008b74] [debug] -->[C61] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000126] [05:01:48:436928] [Tid0x00008b74] [debug] <--[C61] DL_Rom_SetEnableAttr
[0000127] [05:01:48:440434] [Tid0x00008b74] [debug] -->[C62] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000128] [05:01:48:440434] [Tid0x00008b74] [debug] <--[C62] DL_Rom_SetEnableAttr
[0000129] [05:01:48:442435] [Tid0x00008b74] [debug] -->[C63] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000130] [05:01:48:442435] [Tid0x00008b74] [debug] <--[C63] DL_Rom_SetEnableAttr
[0000131] [05:01:48:445435] [Tid0x00008b74] [debug] -->[C64] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000132] [05:01:48:445435] [Tid0x00008b74] [debug] <--[C64] DL_Rom_SetEnableAttr
[0000133] [05:01:48:447435] [Tid0x00008b74] [debug] -->[C65] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000134] [05:01:48:447435] [Tid0x00008b74] [debug] <--[C65] DL_Rom_SetEnableAttr
[0000135] [05:01:48:449882] [Tid0x00008b74] [debug] -->[C66] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000136] [05:01:48:449882] [Tid0x00008b74] [debug] <--[C66] DL_Rom_SetEnableAttr
[0000137] [05:01:48:452891] [Tid0x00008b74] [debug] -->[C67] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000138] [05:01:48:452891] [Tid0x00008b74] [debug] <--[C67] DL_Rom_SetEnableAttr
[0000139] [05:01:48:454908] [Tid0x00008b74] [debug] -->[C68] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000140] [05:01:48:454908] [Tid0x00008b74] [debug] <--[C68] DL_Rom_SetEnableAttr
[0000141] [05:01:48:456910] [Tid0x00008b74] [debug] -->[C69] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000142] [05:01:48:456910] [Tid0x00008b74] [debug] <--[C69] DL_Rom_SetEnableAttr
[0000143] [05:01:48:460418] [Tid0x00008b74] [debug] -->[C70] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000144] [05:01:48:460418] [Tid0x00008b74] [debug] <--[C70] DL_Rom_SetEnableAttr
[0000145] [05:01:48:462417] [Tid0x00008b74] [debug] -->[C71] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000146] [05:01:48:462417] [Tid0x00008b74] [debug] <--[C71] DL_Rom_SetEnableAttr
[0000147] [05:01:48:465417] [Tid0x00008b74] [debug] -->[C72] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000148] [05:01:48:465417] [Tid0x00008b74] [debug] <--[C72] DL_Rom_SetEnableAttr
[0000149] [05:01:48:467417] [Tid0x00008b74] [debug] -->[C73] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000150] [05:01:48:467417] [Tid0x00008b74] [debug] <--[C73] DL_Rom_SetEnableAttr
[0000151] [05:01:48:469830] [Tid0x00008b74] [debug] -->[C74] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000152] [05:01:48:469830] [Tid0x00008b74] [debug] <--[C74] DL_Rom_SetEnableAttr
[0000153] [05:01:48:472831] [Tid0x00008b74] [debug] -->[C75] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000154] [05:01:48:472831] [Tid0x00008b74] [debug] <--[C75] DL_Rom_SetEnableAttr
[0000155] [05:01:48:474831] [Tid0x00008b74] [debug] -->[C76] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000156] [05:01:48:474831] [Tid0x00008b74] [debug] <--[C76] DL_Rom_SetEnableAttr
[0000157] [05:01:50:453751] [Tid0x00008b74] [debug] -->[C77] FlashTool_Connect_BROM #(api.cpp, line:1827)
[0000158] [05:01:50:453751] [Tid0x00008b74] [debug] -->[C78] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000159] [05:01:50:453751] [Tid0x00008b74] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000160] [05:01:50:453751] [Tid0x00008b74] [debug] have load scatter already #(api.cpp, line:1943)
[0000161] [05:01:50:453751] [Tid0x00008b74] [debug] libversion 2 #(api.cpp, line:1960)
[0000162] [05:01:50:453751] [Tid0x00008b74] [debug] -->[C79] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000163] [05:01:50:470288] [Tid0x00008b74] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2552)
[0000164] [05:01:50:470288] [Tid0x00008b74] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000165] [05:01:50:472288] [Tid0x00008b74] [debug] callback in brom stage #(cflashtool_api.cpp, line:1209)
[0000166] [05:01:50:472288] [Tid0x00008b74] [debug] m_cb_in_brom_stage run success #(cflashtool_api.cpp, line:1217)
[0000167] [05:01:50:472288] [Tid0x00008b74] [debug] connect_brom_ex OK #(cflashtool_api.cpp, line:1223)
[0000168] [05:01:50:472288] [Tid0x00008b74] [debug] <--[C79] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000169] [05:01:50:472288] [Tid0x00008b74] [debug] <--[C78] FlashTool_Connect_BROM_Ex
[0000170] [05:01:50:472288] [Tid0x00008b74] [debug] <--[C77] FlashTool_Connect_BROM
[0000171] [05:01:50:472288] [Tid0x00008b74] [debug] -->[C80] FlashTool_GetBootResult #(api.cpp, line:2051)
[0000172] [05:01:50:472288] [Tid0x00008b74] [debug] <--[C80] FlashTool_GetBootResult
[0000173] [05:01:50:472288] [Tid0x00008b74] [debug] -->[C81] FlashTool_Connect_Download_DA #(api.cpp, line:2071)
[0000174] [05:01:50:472288] [Tid0x00008b74] [debug] bCheckScatter: 1 #(api.cpp, line:2072)
[0000175] [05:01:50:472288] [Tid0x00008b74] [debug] -->[C82] cflashtool_api::FlashTool_Connect_Download_DA #(cflashtool_api.cpp, line:1384)
[0000176] [05:01:50:472288] [Tid0x00008b74] [debug] bCheckScatter: 1 #(cflashtool_api.cpp, line:1385)
[0000177] [05:01:50:472288] [Tid0x00008b74] [debug] da_source type: 0 #(cflashtool_api.cpp, line:1414)
[0000178] [05:01:50:474289] [Tid0x00008b74] [debug] checksum_level: 3,  battery_setting: 0, reset_key_setting: 0 #(cflashtool_api.cpp, line:1527)
[0000179] [05:01:50:474289] [Tid0x00008b74] [debug] connect_da_end_stage: 2,  enable_dram_in_1st_da: 0, da_log_level: 2, da_log_channel: 1 #(cflashtool_api.cpp, line:1529)
[0000180] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_cid[0]: 0x410301d6 #(cflashtool_api.cpp, line:1644)
[0000181] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_cid[1]: 0x34344133 #(cflashtool_api.cpp, line:1644)
[0000182] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_cid[2]: 0xbf391032 #(cflashtool_api.cpp, line:1644)
[0000183] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_cid[3]: 0x25b824d7 #(cflashtool_api.cpp, line:1644)
[0000184] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[0]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000185] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[1]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000186] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[2]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000187] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[3]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000188] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[4]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000189] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[5]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000190] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[6]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000191] [05:01:55:362596] [Tid0x00008b74] [debug] m_emmc_fwver[7]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000192] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: BBCHIP_TYPE: MT6765 #(cflashtool_api.cpp, line:1279)
[0000193] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: BBCHIP: "MT6765", EXT_26M 
[0000194] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: BBCHIP: CODE(0x6765), VER(0xCA00), SW(0x0000)              
[0000195] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_ret="S_DA_INVALID_STORAGE_TYPE"(3178)                                  
[0000196] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_chip_select[0]="CS_0"(0x00)                   
[0000197] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_chip_select[1]="CS_0"(0x00)                   
[0000198] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_id(0x0000)="[AMD] AM29DL323D"                         
[0000199] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_size(0x00000000)                              
[0000200] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_1(0x0000)                        
[0000201] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_2(0x0000)                        
[0000202] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_3(0x0000)                        
[0000203] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_4(0x0000)                        
[0000204] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_otp_status="S_DONE"(0)                     
[0000205] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_otp_size(0x00000000)                          
[0000206] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_ret="S_DA_INVALID_STORAGE_TYPE"(3178)                                
[0000207] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_chip_select="CS_0"(0x00)                    
[0000208] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_id(0x0000)="[SAMSUNG] K9F5608Q0C"                       
[0000209] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_size(0x0000000000000000)                        
[0000210] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_id_count(0x0000)                        
[0000211] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_pagesize(0)                                  
[0000212] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_sparesize(0)                                 
[0000213] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_pages_per_block(0)                           
[0000214] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_io_interface( )                              
[0000215] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_addr_cycle( )                                
[0000216] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_bmt_exist( )                                
[0000217] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_ret="S_DONE"(0)          
[0000218] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_boot1_size(0x00400000)      
[0000219] [05:01:55:362596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_boot2_size(0x00400000)      
[0000220] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_rpmb_size(0x01000000)       
[0000221] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp1_size(0x00000000)        
[0000222] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp2_size(0x00000000)        
[0000223] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp3_size(0x00000000)        
[0000224] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp4_size(0x00000000)        
[0000225] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_ua_size(0x0000001CCF000000)      
[0000226] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_cid(0x410301D6 0x34344133 0xBF391032 0x25B824D7)      
[0000227] [05:01:55:363596] [Tid0x00008b74] [debug] DA_REPORT: eMMC: m_emmc_fwver(0x0  0x0  0x0  0x0  0x0  0x0  0x0  0x0 )      
[0000228] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_ret="S_DA_INVALID_STORAGE_TYPE"(3178)          
[0000229] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_ua_size(0x0000000000000000)      
[0000230] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_cid(0x00000000 0x00000000 0x00000000 0x00000000)      
[0000231] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: INT_RAM: m_int_sram_ret="S_DONE"(0)                         
[0000232] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: INT_RAM: m_int_sram_size(0x0003A000)                           
[0000233] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_ret="S_DONE"(0)                          
[0000234] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_type(0x02)="HW_RAM_DRAM"                     
[0000235] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_chip_select(0x00)="CS_0"              
[0000236] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_size(0x0000000100000000)                            
[0000237] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: RandomID: 0xA36C9DBB 0xC939517C 0x0D29B500 0x59B1A306
[0000238] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_ret="S_DA_INVALID_STORAGE_TYPE"(3178)          
[0000239] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu0_size(0x0000000000000000)      
[0000240] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu1_size(0x0000000000000000)      
[0000241] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu2_size(0x0000000000000000)      
[0000242] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_cid()      
[0000243] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_fwver()      
[0000244] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_sn()      
[0000245] [05:01:55:363596] [Tid0x00008b74] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_vendor_id(0x0)      
[0000246] [05:01:55:363596] [Tid0x00008b74] [debug] <--[C82] cflashtool_api::FlashTool_Connect_Download_DA
[0000247] [05:01:55:363596] [Tid0x00008b74] [debug] <--[C81] FlashTool_Connect_Download_DA
[0000248] [05:01:55:363596] [Tid0x00008b74] [debug] -->[C83] FlashTool_Check_Battery #(api.cpp, line:2147)
[0000249] [05:01:55:363596] [Tid0x00008b74] [debug] -->[C84] cflashtool_api::FlashTool_Check_Battery #(cflashtool_api.cpp, line:1787)
[0000250] [05:01:55:369916] [Tid0x00008b74] [debug] <--[C84] cflashtool_api::FlashTool_Check_Battery
[0000251] [05:01:55:369916] [Tid0x00008b74] [debug] <--[C83] FlashTool_Check_Battery
[0000252] [05:01:55:369916] [Tid0x00008b74] [debug] -->[C85] FlashTool_CheckUSBStatus #(api.cpp, line:1154)
[0000253] [05:01:55:369916] [Tid0x00008b74] [debug] -->[C86] cflashtool_api::FlashTool_CheckUSBStatus #(cflashtool_api.cpp, line:113)
[0000254] [05:01:55:373915] [Tid0x00008b74] [debug] <--[C86] cflashtool_api::FlashTool_CheckUSBStatus
[0000255] [05:01:55:373915] [Tid0x00008b74] [debug] <--[C85] FlashTool_CheckUSBStatus
[0000256] [05:01:55:373915] [Tid0x00008b74] [debug] -->[C87] FlashTool_Device_Control #(api.cpp, line:4064)
[0000257] [05:01:55:373915] [Tid0x00008b74] [debug] -->[C88] cflashtool_api::FlashTool_Device_Control #(cflashtool_api.cpp, line:2690)
[0000258] [05:01:55:381420] [Tid0x00008b74] [debug] <--[C88] cflashtool_api::FlashTool_Device_Control
[0000259] [05:01:55:381420] [Tid0x00008b74] [debug] <--[C87] FlashTool_Device_Control
[0000260] [05:01:55:382420] [Tid0x00008b74] [debug] -->[C89] FlashTool_Format #(api.cpp, line:1346)
[0000261] [05:01:55:382420] [Tid0x00008b74] [debug] -->[C90] cflashtool_api::FlashTool_Format #(cflashtool_api.cpp, line:673)
[0000262] [05:01:55:440315] [Tid0x00008b74] [debug] <--[C90] cflashtool_api::FlashTool_Format
[0000263] [05:01:55:440315] [Tid0x00008b74] [debug] <--[C89] FlashTool_Format
[0000264] [05:01:55:440315] [Tid0x00008b74] [debug] -->[C91] FlashTool_Format #(api.cpp, line:1346)
[0000265] [05:01:55:440315] [Tid0x00008b74] [debug] -->[C92] cflashtool_api::FlashTool_Format #(cflashtool_api.cpp, line:673)
[0000266] [05:01:55:480930] [Tid0x00008b74] [debug] <--[C92] cflashtool_api::FlashTool_Format
[0000267] [05:01:55:480930] [Tid0x00008b74] [debug] <--[C91] FlashTool_Format
[0000268] [05:01:55:480930] [Tid0x00008b74] [debug] -->[C93] FlashTool_Format #(api.cpp, line:1346)
[0000269] [05:01:55:480930] [Tid0x00008b74] [debug] -->[C94] cflashtool_api::FlashTool_Format #(cflashtool_api.cpp, line:673)
[0000270] [05:01:55:819377] [Tid0x00008b74] [debug] <--[C94] cflashtool_api::FlashTool_Format
[0000271] [05:01:55:819377] [Tid0x00008b74] [debug] <--[C93] FlashTool_Format
[0000272] [05:01:55:823377] [Tid0x00008b74] [debug] -->[C95] FlashTool_Download #(api.cpp, line:1300)
[0000273] [05:01:55:823377] [Tid0x00008b74] [debug] -->[C96] cflashtool_api::FlashTool_Download #(cflashtool_api.cpp, line:412)
[0000274] [05:01:55:823377] [Tid0x00008b74] [debug] -->[C97] DL_GetCount #(api.cpp, line:2696)
[0000275] [05:01:55:823377] [Tid0x00008b74] [debug] <--[C97] DL_GetCount
[0000276] [05:01:55:823377] [Tid0x00008b74] [debug] -->[C98] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000277] [05:01:55:824378] [Tid0x00008b74] [debug] <--[C98] DL_Rom_GetInfoAll
[0000278] [05:04:46:657528] [Tid0x00008b74] [debug] <--[C96] cflashtool_api::FlashTool_Download
[0000279] [05:04:46:657528] [Tid0x00008b74] [debug] <--[C95] FlashTool_Download
[0000280] [05:04:46:658528] [Tid0x00008b74] [debug] -->[C99] FlashTool_EnableWatchDogTimeout #(api.cpp, line:1220)
[0000281] [05:04:46:658528] [Tid0x00008b74] [debug] -->[C100] cflashtool_api::FlashTool_EnableWatchDogTimeout #(cflashtool_api.cpp, line:150)
[0000282] [05:04:46:664036] [Tid0x00008b74] [debug] <--[C100] cflashtool_api::FlashTool_EnableWatchDogTimeout
[0000283] [05:04:46:664036] [Tid0x00008b74] [debug] <--[C99] FlashTool_EnableWatchDogTimeout
[0000284] [05:04:46:665036] [Tid0x00008b74] [debug] -->[C101] FlashTool_Disconnect #(api.cpp, line:1033)
[0000285] [05:04:46:665036] [Tid0x00008b74] [debug] -->[C102] cflashtool_api::FlashTool_Disconnect #(cflashtool_api.cpp, line:33)
[0000286] [05:04:46:682051] [Tid0x00008b74] [debug] -->[C103] DL_ClearFTHandle #(api.cpp, line:2618)
[0000287] [05:04:46:682051] [Tid0x00008b74] [debug] <--[C103] DL_ClearFTHandle
[0000288] [05:04:46:682051] [Tid0x00008b74] [debug] <--[C102] cflashtool_api::FlashTool_Disconnect
[0000289] [05:04:46:682051] [Tid0x00008b74] [debug] <--[C101] FlashTool_Disconnect
[0000290] [19:19:39:496548] [Tid0x0000138c] [debug] -->[C104] DL_Rom_UnloadAll #(api.cpp, line:2904)
[0000291] [19:19:39:497944] [Tid0x0000138c] [debug] <--[C104] DL_Rom_UnloadAll
[0000292] [19:19:39:497944] [Tid0x0000138c] [debug] -->[C105] DL_LoadScatter #(api.cpp, line:2554)
[0000293] [19:19:39:514134] [Tid0x0000138c] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000294] [19:19:39:514134] [Tid0x0000138c] [debug] <--[C105] DL_LoadScatter
[0000295] [19:19:39:514134] [Tid0x0000138c] [debug] -->[C106] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000296] [19:19:39:521649] [Tid0x0000138c] [debug] <--[C106] DL_AutoLoadRomImages
[0000297] [19:19:39:521649] [Tid0x0000138c] [debug] -->[C107] DL_GetCount #(api.cpp, line:2696)
[0000298] [19:19:39:521649] [Tid0x0000138c] [debug] <--[C107] DL_GetCount
[0000299] [19:19:39:521649] [Tid0x0000138c] [debug] -->[C108] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000300] [19:19:39:521649] [Tid0x0000138c] [debug] <--[C108] DL_Rom_GetInfoAll
[0000301] [19:19:39:521649] [Tid0x0000138c] [debug] -->[C109] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000302] [19:19:39:521649] [Tid0x0000138c] [debug] <--[C109] DL_SetChecksumLevel
[0000303] [19:19:39:522706] [Tid0x0000138c] [debug] -->[C110] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000304] [19:19:39:522706] [Tid0x0000138c] [debug] <--[C110] DL_Rom_SetEnableAttr
[0000305] [19:19:39:525739] [Tid0x0000138c] [debug] -->[C111] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000306] [19:19:39:525739] [Tid0x0000138c] [debug] <--[C111] DL_Rom_SetEnableAttr
[0000307] [19:19:39:528742] [Tid0x0000138c] [debug] -->[C112] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000308] [19:19:39:528742] [Tid0x0000138c] [debug] <--[C112] DL_Rom_SetEnableAttr
[0000309] [19:19:39:531648] [Tid0x0000138c] [debug] -->[C113] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000310] [19:19:39:531648] [Tid0x0000138c] [debug] <--[C113] DL_Rom_SetEnableAttr
[0000311] [19:19:39:534648] [Tid0x0000138c] [debug] -->[C114] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000312] [19:19:39:534648] [Tid0x0000138c] [debug] <--[C114] DL_Rom_SetEnableAttr
[0000313] [19:19:39:536656] [Tid0x0000138c] [debug] -->[C115] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000314] [19:19:39:536656] [Tid0x0000138c] [debug] <--[C115] DL_Rom_SetEnableAttr
[0000315] [19:19:39:539649] [Tid0x0000138c] [debug] -->[C116] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000316] [19:19:39:539649] [Tid0x0000138c] [debug] <--[C116] DL_Rom_SetEnableAttr
[0000317] [19:19:39:542648] [Tid0x0000138c] [debug] -->[C117] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000318] [19:19:39:542648] [Tid0x0000138c] [debug] <--[C117] DL_Rom_SetEnableAttr
[0000319] [19:19:39:545649] [Tid0x0000138c] [debug] -->[C118] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000320] [19:19:39:545649] [Tid0x0000138c] [debug] <--[C118] DL_Rom_SetEnableAttr
[0000321] [19:19:39:548648] [Tid0x0000138c] [debug] -->[C119] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000322] [19:19:39:548648] [Tid0x0000138c] [debug] <--[C119] DL_Rom_SetEnableAttr
[0000323] [19:19:39:551648] [Tid0x0000138c] [debug] -->[C120] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000324] [19:19:39:551648] [Tid0x0000138c] [debug] <--[C120] DL_Rom_SetEnableAttr
[0000325] [19:19:39:553648] [Tid0x0000138c] [debug] -->[C121] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000326] [19:19:39:553648] [Tid0x0000138c] [debug] <--[C121] DL_Rom_SetEnableAttr
[0000327] [19:19:39:556649] [Tid0x0000138c] [debug] -->[C122] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000328] [19:19:39:556649] [Tid0x0000138c] [debug] <--[C122] DL_Rom_SetEnableAttr
[0000329] [19:19:39:559657] [Tid0x0000138c] [debug] -->[C123] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000330] [19:19:39:559657] [Tid0x0000138c] [debug] <--[C123] DL_Rom_SetEnableAttr
[0000331] [19:19:39:562656] [Tid0x0000138c] [debug] -->[C124] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000332] [19:19:39:562656] [Tid0x0000138c] [debug] <--[C124] DL_Rom_SetEnableAttr
[0000333] [19:19:39:565648] [Tid0x0000138c] [debug] -->[C125] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000334] [19:19:39:565648] [Tid0x0000138c] [debug] <--[C125] DL_Rom_SetEnableAttr
[0000335] [19:19:39:568656] [Tid0x0000138c] [debug] -->[C126] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000336] [19:19:39:568656] [Tid0x0000138c] [debug] <--[C126] DL_Rom_SetEnableAttr
[0000337] [19:19:39:570656] [Tid0x0000138c] [debug] -->[C127] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000338] [19:19:39:570656] [Tid0x0000138c] [debug] <--[C127] DL_Rom_SetEnableAttr
[0000339] [19:19:39:573656] [Tid0x0000138c] [debug] -->[C128] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000340] [19:19:39:573656] [Tid0x0000138c] [debug] <--[C128] DL_Rom_SetEnableAttr
[0000341] [19:19:39:576649] [Tid0x0000138c] [debug] -->[C129] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000342] [19:19:39:576649] [Tid0x0000138c] [debug] <--[C129] DL_Rom_SetEnableAttr
[0000343] [19:19:39:579652] [Tid0x0000138c] [debug] -->[C130] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000344] [19:19:39:579652] [Tid0x0000138c] [debug] <--[C130] DL_Rom_SetEnableAttr
[0000345] [19:19:39:580653] [Tid0x0000138c] [debug] -->[C131] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000346] [19:19:39:580653] [Tid0x0000138c] [debug] <--[C131] DL_Rom_SetEnableAttr
[0000347] [19:19:39:580653] [Tid0x0000138c] [debug] -->[C132] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000348] [19:19:39:580653] [Tid0x0000138c] [debug] <--[C132] DL_Rom_SetEnableAttr
[0000349] [19:19:39:581651] [Tid0x0000138c] [debug] -->[C133] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000350] [19:19:39:581651] [Tid0x0000138c] [debug] <--[C133] DL_Rom_SetEnableAttr
[0000351] [19:19:39:582651] [Tid0x0000138c] [debug] -->[C134] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000352] [19:19:39:582651] [Tid0x0000138c] [debug] <--[C134] DL_Rom_SetEnableAttr
[0000353] [19:19:39:584660] [Tid0x0000138c] [debug] -->[C135] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000354] [19:19:39:584660] [Tid0x0000138c] [debug] <--[C135] DL_Rom_SetEnableAttr
[0000355] [19:19:39:585660] [Tid0x0000138c] [debug] -->[C136] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000356] [19:19:39:585660] [Tid0x0000138c] [debug] <--[C136] DL_Rom_SetEnableAttr
[0000357] [19:19:39:586654] [Tid0x0000138c] [debug] -->[C137] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000358] [19:19:39:587659] [Tid0x0000138c] [debug] <--[C137] DL_Rom_SetEnableAttr
[0000359] [19:19:39:588659] [Tid0x0000138c] [debug] -->[C138] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000360] [19:19:39:588659] [Tid0x0000138c] [debug] <--[C138] DL_Rom_SetEnableAttr
[0000361] [19:19:39:591938] [Tid0x0000138c] [debug] -->[C139] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000362] [19:19:39:591938] [Tid0x0000138c] [debug] <--[C139] DL_Rom_SetEnableAttr
[0000363] [19:19:39:593983] [Tid0x0000138c] [debug] -->[C140] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000364] [19:19:39:593983] [Tid0x0000138c] [debug] <--[C140] DL_Rom_SetEnableAttr
[0000365] [19:19:39:595983] [Tid0x0000138c] [debug] -->[C141] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000366] [19:19:39:595983] [Tid0x0000138c] [debug] <--[C141] DL_Rom_SetEnableAttr
[0000367] [19:19:39:597983] [Tid0x0000138c] [debug] -->[C142] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000368] [19:19:39:597983] [Tid0x0000138c] [debug] <--[C142] DL_Rom_SetEnableAttr
[0000369] [19:19:39:600481] [Tid0x0000138c] [debug] -->[C143] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000370] [19:19:39:600481] [Tid0x0000138c] [debug] <--[C143] DL_Rom_SetEnableAttr
[0000371] [19:19:39:603484] [Tid0x0000138c] [debug] -->[C144] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000372] [19:19:39:603484] [Tid0x0000138c] [debug] <--[C144] DL_Rom_SetEnableAttr
[0000373] [19:19:39:608484] [Tid0x0000138c] [debug] -->[C145] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000374] [19:19:39:608484] [Tid0x0000138c] [debug] <--[C145] DL_Rom_SetEnableAttr
[0000375] [19:19:39:611491] [Tid0x0000138c] [debug] -->[C146] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000376] [19:19:39:611491] [Tid0x0000138c] [debug] <--[C146] DL_Rom_SetEnableAttr
[0000377] [19:19:39:615484] [Tid0x0000138c] [debug] -->[C147] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000378] [19:19:39:615484] [Tid0x0000138c] [debug] <--[C147] DL_Rom_SetEnableAttr
[0000379] [19:19:39:619484] [Tid0x0000138c] [debug] -->[C148] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000380] [19:19:39:619484] [Tid0x0000138c] [debug] <--[C148] DL_Rom_SetEnableAttr
[0000381] [19:19:39:622483] [Tid0x0000138c] [debug] -->[C149] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000382] [19:19:39:622483] [Tid0x0000138c] [debug] <--[C149] DL_Rom_SetEnableAttr
[0000383] [19:19:39:625500] [Tid0x0000138c] [debug] -->[C150] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000384] [19:19:39:625500] [Tid0x0000138c] [debug] <--[C150] DL_Rom_SetEnableAttr
[0000385] [19:19:39:628484] [Tid0x0000138c] [debug] -->[C151] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000386] [19:19:39:628484] [Tid0x0000138c] [debug] <--[C151] DL_Rom_SetEnableAttr
[0000387] [19:19:39:631491] [Tid0x0000138c] [debug] -->[C152] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000388] [19:19:39:631491] [Tid0x0000138c] [debug] <--[C152] DL_Rom_SetEnableAttr
[0000389] [19:19:39:633491] [Tid0x0000138c] [debug] -->[C153] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000390] [19:19:39:633491] [Tid0x0000138c] [debug] <--[C153] DL_Rom_SetEnableAttr
[0000391] [19:19:39:636492] [Tid0x0000138c] [debug] -->[C154] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000392] [19:19:39:636492] [Tid0x0000138c] [debug] <--[C154] DL_Rom_SetEnableAttr
[0000393] [19:19:39:639484] [Tid0x0000138c] [debug] -->[C155] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000394] [19:19:39:639484] [Tid0x0000138c] [debug] <--[C155] DL_Rom_SetEnableAttr
[0000395] [19:19:39:641492] [Tid0x0000138c] [debug] -->[C156] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000396] [19:19:39:641492] [Tid0x0000138c] [debug] <--[C156] DL_Rom_SetEnableAttr
[0000397] [19:19:39:644484] [Tid0x0000138c] [debug] -->[C157] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000398] [19:19:39:645484] [Tid0x0000138c] [debug] <--[C157] DL_Rom_SetEnableAttr
[0000399] [19:19:39:647485] [Tid0x0000138c] [debug] -->[C158] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000400] [19:19:39:647485] [Tid0x0000138c] [debug] <--[C158] DL_Rom_SetEnableAttr
[0000401] [19:19:39:650485] [Tid0x0000138c] [debug] -->[C159] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000402] [19:19:39:650485] [Tid0x0000138c] [debug] <--[C159] DL_Rom_SetEnableAttr
[0000403] [19:19:39:653485] [Tid0x0000138c] [debug] -->[C160] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000404] [19:19:39:653485] [Tid0x0000138c] [debug] <--[C160] DL_Rom_SetEnableAttr
[0000405] [19:19:39:656485] [Tid0x0000138c] [debug] -->[C161] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000406] [19:19:39:656485] [Tid0x0000138c] [debug] <--[C161] DL_Rom_SetEnableAttr
[0000407] [19:19:39:659485] [Tid0x0000138c] [debug] -->[C162] DL_Rom_SetEnableAttr #(api.cpp, line:2915)
[0000408] [19:19:39:659485] [Tid0x0000138c] [debug] <--[C162] DL_Rom_SetEnableAttr
[0000409] [19:19:43:206669] [Tid0x0000138c] [debug] -->[C163] FlashTool_Connect_BROM #(api.cpp, line:1827)
[0000410] [19:19:43:206669] [Tid0x0000138c] [debug] -->[C164] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000411] [19:19:43:206669] [Tid0x0000138c] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000412] [19:19:43:206669] [Tid0x0000138c] [debug] have load scatter already #(api.cpp, line:1943)
[0000413] [19:19:43:206669] [Tid0x0000138c] [debug] libversion 2 #(api.cpp, line:1960)
[0000414] [19:19:43:206669] [Tid0x0000138c] [debug] -->[C165] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000415] [19:19:43:215527] [Tid0x0000138c] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2552)
[0000416] [19:19:43:215527] [Tid0x0000138c] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000417] [19:19:43:217022] [Tid0x0000138c] [debug] callback in brom stage #(cflashtool_api.cpp, line:1209)
[0000418] [19:19:43:217022] [Tid0x0000138c] [debug] m_cb_in_brom_stage run success #(cflashtool_api.cpp, line:1217)
[0000419] [19:19:43:217022] [Tid0x0000138c] [debug] connect_brom_ex OK #(cflashtool_api.cpp, line:1223)
[0000420] [19:19:43:217022] [Tid0x0000138c] [debug] <--[C165] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000421] [19:19:43:217022] [Tid0x0000138c] [debug] <--[C164] FlashTool_Connect_BROM_Ex
[0000422] [19:19:43:217022] [Tid0x0000138c] [debug] <--[C163] FlashTool_Connect_BROM
[0000423] [19:19:43:217022] [Tid0x0000138c] [debug] -->[C166] FlashTool_GetBootResult #(api.cpp, line:2051)
[0000424] [19:19:43:217022] [Tid0x0000138c] [debug] <--[C166] FlashTool_GetBootResult
[0000425] [19:19:43:217022] [Tid0x0000138c] [debug] -->[C167] FlashTool_Connect_Download_DA #(api.cpp, line:2071)
[0000426] [19:19:43:217022] [Tid0x0000138c] [debug] bCheckScatter: 1 #(api.cpp, line:2072)
[0000427] [19:19:43:217022] [Tid0x0000138c] [debug] -->[C168] cflashtool_api::FlashTool_Connect_Download_DA #(cflashtool_api.cpp, line:1384)
[0000428] [19:19:43:217022] [Tid0x0000138c] [debug] bCheckScatter: 1 #(cflashtool_api.cpp, line:1385)
[0000429] [19:19:43:218024] [Tid0x0000138c] [debug] da_source type: 0 #(cflashtool_api.cpp, line:1414)
[0000430] [19:19:43:218024] [Tid0x0000138c] [debug] checksum_level: 3,  battery_setting: 0, reset_key_setting: 0 #(cflashtool_api.cpp, line:1527)
[0000431] [19:19:43:218024] [Tid0x0000138c] [debug] connect_da_end_stage: 2,  enable_dram_in_1st_da: 0, da_log_level: 2, da_log_channel: 1 #(cflashtool_api.cpp, line:1529)
[0000432] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_cid[0]: 0x410301d6 #(cflashtool_api.cpp, line:1644)
[0000433] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_cid[1]: 0x34344133 #(cflashtool_api.cpp, line:1644)
[0000434] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_cid[2]: 0xbf391032 #(cflashtool_api.cpp, line:1644)
[0000435] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_cid[3]: 0x25b824d7 #(cflashtool_api.cpp, line:1644)
[0000436] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[0]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000437] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[1]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000438] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[2]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000439] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[3]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000440] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[4]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000441] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[5]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000442] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[6]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000443] [19:19:46:147338] [Tid0x0000138c] [debug] m_emmc_fwver[7]: 0x0 #(cflashtool_api.cpp, line:1650)
[0000444] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: BBCHIP_TYPE: MT6765 #(cflashtool_api.cpp, line:1279)
[0000445] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: BBCHIP: "MT6765", EXT_26M 
[0000446] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: BBCHIP: CODE(0x6765), VER(0xCA00), SW(0x0000)              
[0000447] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_ret="S_DA_INVALID_STORAGE_TYPE"(3178)                                  
[0000448] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_chip_select[0]="CS_0"(0x00)                   
[0000449] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_chip_select[1]="CS_0"(0x00)                   
[0000450] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_id(0x0000)="[AMD] AM29DL323D"                         
[0000451] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_size(0x00000000)                              
[0000452] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_1(0x0000)                        
[0000453] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_2(0x0000)                        
[0000454] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_3(0x0000)                        
[0000455] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_4(0x0000)                        
[0000456] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_otp_status="S_DONE"(0)                     
[0000457] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_otp_size(0x00000000)                          
[0000458] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_ret="S_DA_INVALID_STORAGE_TYPE"(3178)                                
[0000459] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_chip_select="CS_0"(0x00)                    
[0000460] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_id(0x0000)="[SAMSUNG] K9F5608Q0C"                       
[0000461] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_size(0x0000000000000000)                        
[0000462] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_id_count(0x0000)                        
[0000463] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_pagesize(0)                                  
[0000464] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_sparesize(0)                                 
[0000465] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_pages_per_block(0)                           
[0000466] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_io_interface( )                              
[0000467] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_addr_cycle( )                                
[0000468] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_bmt_exist( )                                
[0000469] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_ret="S_DONE"(0)          
[0000470] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_boot1_size(0x00400000)      
[0000471] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_boot2_size(0x00400000)      
[0000472] [19:19:46:147338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_rpmb_size(0x01000000)       
[0000473] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp1_size(0x00000000)        
[0000474] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp2_size(0x00000000)        
[0000475] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp3_size(0x00000000)        
[0000476] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp4_size(0x00000000)        
[0000477] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_ua_size(0x0000001CCF000000)      
[0000478] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_cid(0x410301D6 0x34344133 0xBF391032 0x25B824D7)      
[0000479] [19:19:46:148338] [Tid0x0000138c] [debug] DA_REPORT: eMMC: m_emmc_fwver(0x0  0x0  0x0  0x0  0x0  0x0  0x0  0x0 )      
[0000480] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_ret="S_DA_INVALID_STORAGE_TYPE"(3178)          
[0000481] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_ua_size(0x0000000000000000)      
[0000482] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_cid(0x00000000 0x00000000 0x00000000 0x00000000)      
[0000483] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: INT_RAM: m_int_sram_ret="S_DONE"(0)                         
[0000484] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: INT_RAM: m_int_sram_size(0x0003A000)                           
[0000485] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_ret="S_DONE"(0)                          
[0000486] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_type(0x02)="HW_RAM_DRAM"                     
[0000487] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_chip_select(0x00)="CS_0"              
[0000488] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_size(0x0000000100000000)                            
[0000489] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: RandomID: 0xA36C9DBB 0xC939517C 0x0D29B500 0x59B1A306
[0000490] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_ret="S_DA_INVALID_STORAGE_TYPE"(3178)          
[0000491] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu0_size(0x0000000000000000)      
[0000492] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu1_size(0x0000000000000000)      
[0000493] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu2_size(0x0000000000000000)      
[0000494] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_cid()      
[0000495] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_fwver()      
[0000496] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_sn()      
[0000497] [19:19:46:148338] [Tid0x0000138c] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_vendor_id(0x0)      
[0000498] [19:19:46:148338] [Tid0x0000138c] [debug] <--[C168] cflashtool_api::FlashTool_Connect_Download_DA
[0000499] [19:19:46:148338] [Tid0x0000138c] [debug] <--[C167] FlashTool_Connect_Download_DA
[0000500] [19:19:46:151340] [Tid0x0000138c] [debug] -->[C169] FlashTool_Check_Battery #(api.cpp, line:2147)
[0000501] [19:19:46:151340] [Tid0x0000138c] [debug] -->[C170] cflashtool_api::FlashTool_Check_Battery #(cflashtool_api.cpp, line:1787)
[0000502] [19:19:46:156340] [Tid0x0000138c] [debug] <--[C170] cflashtool_api::FlashTool_Check_Battery
[0000503] [19:19:46:156340] [Tid0x0000138c] [debug] <--[C169] FlashTool_Check_Battery
[0000504] [19:19:46:156340] [Tid0x0000138c] [debug] -->[C171] FlashTool_CheckUSBStatus #(api.cpp, line:1154)
[0000505] [19:19:46:156340] [Tid0x0000138c] [debug] -->[C172] cflashtool_api::FlashTool_CheckUSBStatus #(cflashtool_api.cpp, line:113)
[0000506] [19:19:46:160063] [Tid0x0000138c] [debug] <--[C172] cflashtool_api::FlashTool_CheckUSBStatus
[0000507] [19:19:46:160063] [Tid0x0000138c] [debug] <--[C171] FlashTool_CheckUSBStatus
[0000508] [19:19:46:160063] [Tid0x0000138c] [debug] -->[C173] FlashTool_Device_Control #(api.cpp, line:4064)
[0000509] [19:19:46:160063] [Tid0x0000138c] [debug] -->[C174] cflashtool_api::FlashTool_Device_Control #(cflashtool_api.cpp, line:2690)
[0000510] [19:19:46:167656] [Tid0x0000138c] [debug] <--[C174] cflashtool_api::FlashTool_Device_Control
[0000511] [19:19:46:167656] [Tid0x0000138c] [debug] <--[C173] FlashTool_Device_Control
[0000512] [19:19:46:167656] [Tid0x0000138c] [debug] -->[C175] FlashTool_Format #(api.cpp, line:1346)
[0000513] [19:19:46:167656] [Tid0x0000138c] [debug] -->[C176] cflashtool_api::FlashTool_Format #(cflashtool_api.cpp, line:673)
[0000514] [19:19:46:200199] [Tid0x0000138c] [debug] <--[C176] cflashtool_api::FlashTool_Format
[0000515] [19:19:46:200199] [Tid0x0000138c] [debug] <--[C175] FlashTool_Format
[0000516] [19:19:46:200199] [Tid0x0000138c] [debug] -->[C177] FlashTool_Format #(api.cpp, line:1346)
[0000517] [19:19:46:200199] [Tid0x0000138c] [debug] -->[C178] cflashtool_api::FlashTool_Format #(cflashtool_api.cpp, line:673)
[0000518] [19:19:46:224200] [Tid0x0000138c] [debug] <--[C178] cflashtool_api::FlashTool_Format
[0000519] [19:19:46:224200] [Tid0x0000138c] [debug] <--[C177] FlashTool_Format
[0000520] [19:19:46:224200] [Tid0x0000138c] [debug] -->[C179] FlashTool_Format #(api.cpp, line:1346)
[0000521] [19:19:46:224200] [Tid0x0000138c] [debug] -->[C180] cflashtool_api::FlashTool_Format #(cflashtool_api.cpp, line:673)
[0000522] [19:19:48:843394] [Tid0x0000138c] [debug] <--[C180] cflashtool_api::FlashTool_Format
[0000523] [19:19:48:843394] [Tid0x0000138c] [debug] <--[C179] FlashTool_Format
[0000524] [19:19:48:843394] [Tid0x0000138c] [debug] -->[C181] FlashTool_Download #(api.cpp, line:1300)
[0000525] [19:19:48:843394] [Tid0x0000138c] [debug] -->[C182] cflashtool_api::FlashTool_Download #(cflashtool_api.cpp, line:412)
[0000526] [19:19:48:843394] [Tid0x0000138c] [debug] -->[C183] DL_GetCount #(api.cpp, line:2696)
[0000527] [19:19:48:843394] [Tid0x0000138c] [debug] <--[C183] DL_GetCount
[0000528] [19:19:48:843394] [Tid0x0000138c] [debug] -->[C184] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000529] [19:19:48:843394] [Tid0x0000138c] [debug] <--[C184] DL_Rom_GetInfoAll
[0000530] [19:22:42:101033] [Tid0x0000138c] [debug] <--[C182] cflashtool_api::FlashTool_Download
[0000531] [19:22:42:101033] [Tid0x0000138c] [debug] <--[C181] FlashTool_Download
[0000532] [19:22:42:101033] [Tid0x0000138c] [debug] -->[C185] FlashTool_EnableWatchDogTimeout #(api.cpp, line:1220)
[0000533] [19:22:42:101033] [Tid0x0000138c] [debug] -->[C186] cflashtool_api::FlashTool_EnableWatchDogTimeout #(cflashtool_api.cpp, line:150)
[0000534] [19:22:42:107033] [Tid0x0000138c] [debug] <--[C186] cflashtool_api::FlashTool_EnableWatchDogTimeout
[0000535] [19:22:42:107033] [Tid0x0000138c] [debug] <--[C185] FlashTool_EnableWatchDogTimeout
[0000536] [19:22:42:107033] [Tid0x0000138c] [debug] -->[C187] FlashTool_Disconnect #(api.cpp, line:1033)
[0000537] [19:22:42:107033] [Tid0x0000138c] [debug] -->[C188] cflashtool_api::FlashTool_Disconnect #(cflashtool_api.cpp, line:33)
[0000538] [19:22:42:118968] [Tid0x0000138c] [debug] -->[C189] DL_ClearFTHandle #(api.cpp, line:2618)
[0000539] [19:22:42:118968] [Tid0x0000138c] [debug] <--[C189] DL_ClearFTHandle
[0000540] [19:22:42:118968] [Tid0x0000138c] [debug] <--[C188] cflashtool_api::FlashTool_Disconnect
[0000541] [19:22:42:118968] [Tid0x0000138c] [debug] <--[C187] FlashTool_Disconnect

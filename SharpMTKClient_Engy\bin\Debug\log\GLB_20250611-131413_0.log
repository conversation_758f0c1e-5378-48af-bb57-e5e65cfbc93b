[00000001] [13:14:13:656056] [Tid0x0000c9fc] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [13:14:13:657055] [Tid0x0000c9fc] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [13:14:13:657055] [Tid0x0000c9fc] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [13:14:13:657055] [Tid0x0000c9fc] [info] create new hsession 0xb732648 #(kernel.cpp, line:92)
[00000005] [13:14:13:657055] [Tid0x0000c9fc] [debug] <--[C3] kernel::create_new_session
[00000006] [13:14:13:657055] [Tid0x0000c9fc] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [13:14:13:657055] [Tid0x0000c9fc] [debug] <--[C4] boot_rom::boot_rom
[00000008] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C6] device_log_source::device_log_source
[00000011] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C7] data_mux::data_mux
[00000013] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C5] device_instance::device_instance
[00000014] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C2] connection::create_session
[00000015] [13:14:13:663862] [Tid0x0000c9fc] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [13:14:13:663862] [Tid0x0000c9fc] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [13:14:13:663862] [Tid0x0000c9fc] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C11] is_valid_ip
[00000022] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C12] is_lge_impl
[00000024] [13:14:13:663862] [Tid0x0000c9fc] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [13:14:13:663862] [Tid0x0000c9fc] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C13] lib_config_parser::get_value
[00000027] [13:14:13:663862] [Tid0x0000c9fc] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [13:14:13:663862] [Tid0x0000c9fc] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [13:14:13:663862] [Tid0x0000c9fc] [info] try to open device: COM0 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [13:14:13:663862] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000031] [13:14:13:663862] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000032] [13:14:13:723406] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000033] [13:14:13:723406] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000034] [13:14:13:786814] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000035] [13:14:13:786814] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000036] [13:14:13:848171] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000037] [13:14:13:848171] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000038] [13:14:13:909878] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000039] [13:14:13:909878] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000040] [13:14:13:972625] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000041] [13:14:13:972625] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000042] [13:14:14:035729] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000043] [13:14:14:035729] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000044] [13:14:14:097371] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000045] [13:14:14:097371] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000046] [13:14:14:160547] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000047] [13:14:14:160547] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000048] [13:14:14:222607] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000049] [13:14:14:222607] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000050] [13:14:14:283215] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000051] [13:14:14:283215] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000052] [13:14:14:344879] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000053] [13:14:14:344879] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000054] [13:14:14:407021] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000055] [13:14:14:407021] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000056] [13:14:14:469669] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000057] [13:14:14:469669] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000058] [13:14:14:531739] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000059] [13:14:14:531739] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000060] [13:14:14:592149] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000061] [13:14:14:592149] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000062] [13:14:14:654053] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000063] [13:14:14:654053] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000064] [13:14:14:717914] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000065] [13:14:14:717914] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000066] [13:14:14:779165] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000067] [13:14:14:779165] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000068] [13:14:14:840832] [Tid0x0000c9fc] [debug] CreateFile error code[0x2] #(comm_engine.cpp, line:94)
[00000069] [13:14:14:840832] [Tid0x0000c9fc] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000070] [13:14:14:902299] [Tid0x0000c9fc] [info] <--[C14] comm_engine::open
[00000071] [13:14:14:902299] [Tid0x0000c9fc] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000072] [13:14:14:902299] [Tid0x0000c9fc] [debug] <--[C9] connection::connect_brom
[00000073] [13:14:14:902299] [Tid0x0000c9fc] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000074] [13:14:14:902299] [Tid0x0000c9fc] [info] <--[C8] flashtool_connect_brom
[00000075] [13:14:14:902299] [Tid0x0000c9fc] [info] -->[C15] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000076] [13:14:14:902299] [Tid0x0000c9fc] [debug] -->[C17] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000077] [13:14:14:902299] [Tid0x0000c9fc] [info] -->[C18] device_log_source::stop #(device_log_source.cpp, line:29)
[00000078] [13:14:14:902299] [Tid0x0000c9fc] [info] <--[C18] device_log_source::stop
[00000079] [13:14:14:902299] [Tid0x0000c9fc] [info] -->[C19] data_mux::stop #(data_mux.cpp, line:92)
[00000080] [13:14:14:902299] [Tid0x0000c9fc] [info] <--[C19] data_mux::stop
[00000081] [13:14:14:902299] [Tid0x0000c9fc] [debug] <--[C17] device_instance::~device_instance
[00000082] [13:14:14:902299] [Tid0x0000c9fc] [info] -->[C20] comm_engine::close #(comm_engine.cpp, line:382)
[00000083] [13:14:14:902299] [Tid0x0000c9fc] [debug] -->[C21] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000084] [13:14:14:902299] [Tid0x0000c9fc] [debug] <--[C21] comm_engine::cancel
[00000085] [13:14:14:902299] [Tid0x0000c9fc] [info] <--[C20] comm_engine::close
[00000086] [13:14:14:902299] [Tid0x0000c9fc] [info] delete hsession 0xb732648 #(kernel.cpp, line:102)
[00000087] [13:14:14:902299] [Tid0x0000c9fc] [info] <--[C15] flashtool_destroy_session

[00000001] [16:54:13:323484] [Tid0x0000fd60] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [16:54:13:324488] [Tid0x0000fd60] [info] create new hsession 0x7da1180 #(kernel.cpp, line:92)
[00000005] [16:54:13:324488] [Tid0x0000fd60] [debug] <--[C3] kernel::create_new_session
[00000006] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [16:54:13:324488] [Tid0x0000fd60] [debug] <--[C4] boot_rom::boot_rom
[00000008] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [16:54:13:324488] [Tid0x0000fd60] [debug] <--[C6] device_log_source::device_log_source
[00000011] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [16:54:13:324488] [Tid0x0000fd60] [debug] <--[C7] data_mux::data_mux
[00000013] [16:54:13:324488] [Tid0x0000fd60] [debug] <--[C5] device_instance::device_instance
[00000014] [16:54:13:324488] [Tid0x0000fd60] [debug] <--[C2] connection::create_session
[00000015] [16:54:13:324488] [Tid0x0000fd60] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [16:54:13:324488] [Tid0x0000fd60] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [16:54:13:324488] [Tid0x0000fd60] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [16:54:13:324488] [Tid0x0000fd60] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [16:54:13:325487] [Tid0x0000fd60] [debug] <--[C11] is_valid_ip
[00000022] [16:54:13:325487] [Tid0x0000fd60] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [16:54:13:325487] [Tid0x0000fd60] [debug] <--[C12] is_lge_impl
[00000024] [16:54:13:325487] [Tid0x0000fd60] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [16:54:13:325487] [Tid0x0000fd60] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [16:54:13:325487] [Tid0x0000fd60] [debug] <--[C13] lib_config_parser::get_value
[00000027] [16:54:13:325487] [Tid0x0000fd60] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [16:54:13:325487] [Tid0x0000fd60] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [16:54:13:325487] [Tid0x0000fd60] [info] try to open device: COM5 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [16:54:13:748032] [Tid0x0000fd60] [info] COM5 open complete. #(comm_engine.cpp, line:168)
[00000031] [16:54:13:748032] [Tid0x0000fd60] [info] <--[C14] comm_engine::open
[00000032] [16:54:13:748032] [Tid0x0000fd60] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000033] [16:54:13:748032] [Tid0x0000fd60] [debug] <--[C15] boot_rom::set_transfer_channel
[00000034] [16:54:13:748032] [Tid0x0000fd60] [debug] -->[C16] boot_rom::connect #(boot_rom.cpp, line:47)
[00000035] [16:54:13:748032] [Tid0x0000fd60] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000036] [16:54:13:748032] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000037] [16:54:13:768330] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000038] [16:54:13:768330] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000039] [16:54:13:768330] [Tid0x0000fd60] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000040] [16:54:13:768330] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000041] [16:54:13:768330] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000042] [16:54:13:768330] [Tid0x0000fd60] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000043] [16:54:13:768330] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000044] [16:54:13:768330] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000045] [16:54:13:768330] [Tid0x0000fd60] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000046] [16:54:13:768330] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000047] [16:54:13:769386] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000048] [16:54:13:769386] [Tid0x0000fd60] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000049] [16:54:13:769386] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000050] [16:54:13:769386] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000051] [16:54:13:769386] [Tid0x0000fd60] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000052] [16:54:13:769386] [Tid0x0000fd60] [debug] <--[C16] boot_rom::connect
[00000053] [16:54:13:769386] [Tid0x0000fd60] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000054] [16:54:13:769386] [Tid0x0000fd60] [debug] -->[C28] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000055] [16:54:13:769386] [Tid0x0000fd60] [debug] -->[C29] boot_rom::get_preloader_version #(boot_rom.cpp, line:888)
[00000056] [16:54:13:769386] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000057] [16:54:13:769386] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000058] [16:54:13:769386] [Tid0x0000fd60] [info] preloader version: 0x3 #(boot_rom.cpp, line:905)
[00000059] [16:54:13:769386] [Tid0x0000fd60] [debug] <--[C29] boot_rom::get_preloader_version
[00000060] [16:54:13:769386] [Tid0x0000fd60] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000061] [16:54:13:769386] [Tid0x0000fd60] [debug] <--[C28] boot_rom_logic::security_verify_connection
[00000062] [16:54:13:769386] [Tid0x0000fd60] [debug] <--[C9] connection::connect_brom
[00000063] [16:54:13:769386] [Tid0x0000fd60] [info] <--[C8] flashtool_connect_brom
[00000064] [16:54:13:769386] [Tid0x0000fd60] [info] -->[C32] flashtool_device_control #(flashtoolex_api.cpp, line:303)
[00000065] [16:54:13:769386] [Tid0x0000fd60] [debug] -->[C33] connection::device_control #(connection.cpp, line:647)
[00000066] [16:54:13:769386] [Tid0x0000fd60] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:654)
[00000067] [16:54:13:769386] [Tid0x0000fd60] [debug] -->[C34] boot_rom::device_control #(boot_rom.cpp, line:740)
[00000068] [16:54:13:769386] [Tid0x0000fd60] [debug] -->[C35] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000069] [16:54:13:769386] [Tid0x0000fd60] [info] get chip id  #(boot_rom.cpp, line:114)
[00000070] [16:54:13:769386] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000071] [16:54:13:769386] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000072] [16:54:13:769386] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000073] [16:54:13:769386] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000074] [16:54:13:769386] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000075] [16:54:13:770394] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000076] [16:54:13:770394] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000077] [16:54:13:770394] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000078] [16:54:13:770394] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000079] [16:54:13:770394] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000080] [16:54:13:770394] [Tid0x0000fd60] [debug] -->[C46] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000081] [16:54:13:770394] [Tid0x0000fd60] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000082] [16:54:13:770394] [Tid0x0000fd60] [debug] <--[C46] lib_config_parser::get_value
[00000083] [16:54:13:770394] [Tid0x0000fd60] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:208)
[00000084] [16:54:13:770394] [Tid0x0000fd60] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000085] [16:54:13:770394] [Tid0x0000fd60] [debug] <--[C35] boot_rom::get_chip_id
[00000086] [16:54:13:770394] [Tid0x0000fd60] [debug] <--[C34] boot_rom::device_control
[00000087] [16:54:13:770394] [Tid0x0000fd60] [debug] <--[C33] connection::device_control
[00000088] [16:54:13:770394] [Tid0x0000fd60] [info] <--[C32] flashtool_device_control
[00000089] [16:54:13:770394] [Tid0x0000fd60] [info] -->[C47] flashtool_device_control #(flashtoolex_api.cpp, line:303)
[00000090] [16:54:13:770394] [Tid0x0000fd60] [debug] -->[C48] connection::device_control #(connection.cpp, line:647)
[00000091] [16:54:13:770394] [Tid0x0000fd60] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:654)
[00000092] [16:54:13:770394] [Tid0x0000fd60] [debug] -->[C49] boot_rom::device_control #(boot_rom.cpp, line:740)
[00000093] [16:54:13:770394] [Tid0x0000fd60] [debug] -->[C50] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000094] [16:54:13:770394] [Tid0x0000fd60] [info] get chip id  #(boot_rom.cpp, line:114)
[00000095] [16:54:13:770394] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000096] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000097] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000098] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000099] [16:54:13:771390] [Tid0x0000fd60] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000100] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000101] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000102] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000103] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000104] [16:54:13:771390] [Tid0x0000fd60] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000105] [16:54:13:771390] [Tid0x0000fd60] [debug] -->[C61] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000106] [16:54:13:771390] [Tid0x0000fd60] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000107] [16:54:13:771390] [Tid0x0000fd60] [debug] <--[C61] lib_config_parser::get_value
[00000108] [16:54:13:771390] [Tid0x0000fd60] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:208)
[00000109] [16:54:13:772395] [Tid0x0000fd60] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000110] [16:54:13:772395] [Tid0x0000fd60] [debug] <--[C50] boot_rom::get_chip_id
[00000111] [16:54:13:772395] [Tid0x0000fd60] [debug] <--[C49] boot_rom::device_control
[00000112] [16:54:13:772395] [Tid0x0000fd60] [debug] <--[C48] connection::device_control
[00000113] [16:54:13:772395] [Tid0x0000fd60] [info] <--[C47] flashtool_device_control
[00000114] [16:54:13:772395] [Tid0x0000fd60] [error] Not Support platform: , type:  #(chip_mapping.cpp, line:228)
[00000115] [16:54:13:772395] [Tid0x0000fd60] [info] -->[C62] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000116] [16:54:13:772395] [Tid0x0000fd60] [debug] -->[C64] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000117] [16:54:13:772395] [Tid0x0000fd60] [info] -->[C65] device_log_source::stop #(device_log_source.cpp, line:29)
[00000118] [16:54:13:772395] [Tid0x0000fd60] [info] <--[C65] device_log_source::stop
[00000119] [16:54:13:772395] [Tid0x0000fd60] [info] -->[C66] data_mux::stop #(data_mux.cpp, line:92)
[00000120] [16:54:13:772395] [Tid0x0000fd60] [info] <--[C66] data_mux::stop
[00000121] [16:54:13:772395] [Tid0x0000fd60] [debug] <--[C64] device_instance::~device_instance
[00000122] [16:54:13:772395] [Tid0x0000fd60] [info] -->[C67] comm_engine::close #(comm_engine.cpp, line:382)
[00000123] [16:54:13:772395] [Tid0x0000fd60] [debug] -->[C68] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000124] [16:54:13:772395] [Tid0x0000fd60] [debug] <--[C68] comm_engine::cancel
[00000125] [16:54:13:785908] [Tid0x0000fd60] [info] <--[C67] comm_engine::close
[00000126] [16:54:13:785908] [Tid0x0000fd60] [info] delete hsession 0x7da1180 #(kernel.cpp, line:102)
[00000127] [16:54:13:785908] [Tid0x0000fd60] [info] <--[C62] flashtool_destroy_session

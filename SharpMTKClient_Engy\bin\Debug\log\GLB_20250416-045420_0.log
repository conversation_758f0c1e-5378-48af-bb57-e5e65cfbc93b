[00000001] [04:54:20:181959] [Tid0x00005710] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [04:54:20:182959] [Tid0x00005710] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [04:54:20:182959] [Tid0x00005710] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [04:54:20:182959] [Tid0x00005710] [info] create new hsession 0x11a1b288 #(kernel.cpp, line:92)
[00000005] [04:54:20:182959] [Tid0x00005710] [debug] <--[C3] kernel::create_new_session
[00000006] [04:54:20:182959] [Tid0x00005710] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [04:54:20:182959] [Tid0x00005710] [debug] <--[C4] boot_rom::boot_rom
[00000008] [04:54:20:189646] [Tid0x00005710] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [04:54:20:189646] [Tid0x00005710] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [04:54:20:189646] [Tid0x00005710] [debug] <--[C6] device_log_source::device_log_source
[00000011] [04:54:20:189646] [Tid0x00005710] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [04:54:20:189646] [Tid0x00005710] [debug] <--[C7] data_mux::data_mux
[00000013] [04:54:20:189646] [Tid0x00005710] [debug] <--[C5] device_instance::device_instance
[00000014] [04:54:20:189646] [Tid0x00005710] [debug] <--[C2] connection::create_session
[00000015] [04:54:20:189646] [Tid0x00005710] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [04:54:20:189646] [Tid0x00005710] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [04:54:20:189646] [Tid0x00005710] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [04:54:20:189646] [Tid0x00005710] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [04:54:20:189646] [Tid0x00005710] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [04:54:20:189646] [Tid0x00005710] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [04:54:20:190650] [Tid0x00005710] [debug] <--[C11] is_valid_ip
[00000022] [04:54:20:190650] [Tid0x00005710] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [04:54:20:190650] [Tid0x00005710] [debug] <--[C12] is_lge_impl
[00000024] [04:54:20:190650] [Tid0x00005710] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [04:54:20:190650] [Tid0x00005710] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [04:54:20:190650] [Tid0x00005710] [debug] <--[C13] lib_config_parser::get_value
[00000027] [04:54:20:190650] [Tid0x00005710] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [04:54:20:190650] [Tid0x00005710] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [04:54:20:190650] [Tid0x00005710] [info] try to open device: COM13 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [04:54:20:464821] [Tid0x00005710] [info] COM13 open complete. #(comm_engine.cpp, line:168)
[00000031] [04:54:20:465820] [Tid0x00005710] [info] <--[C14] comm_engine::open
[00000032] [04:54:20:465820] [Tid0x00005710] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000033] [04:54:20:465820] [Tid0x00005710] [debug] <--[C15] boot_rom::set_transfer_channel
[00000034] [04:54:20:465820] [Tid0x00005710] [debug] -->[C16] boot_rom::connect #(boot_rom.cpp, line:47)
[00000035] [04:54:20:465820] [Tid0x00005710] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000036] [04:54:20:465820] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000037] [04:54:20:465820] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000038] [04:54:20:465820] [Tid0x00005710] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000039] [04:54:20:465820] [Tid0x00005710] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000040] [04:54:20:465820] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000041] [04:54:20:485336] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000042] [04:54:20:485336] [Tid0x00005710] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000043] [04:54:20:485336] [Tid0x00005710] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000044] [04:54:20:485336] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000045] [04:54:20:485336] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000046] [04:54:20:485336] [Tid0x00005710] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000047] [04:54:20:485336] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000048] [04:54:20:485336] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000049] [04:54:20:485336] [Tid0x00005710] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000050] [04:54:20:486351] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000051] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000052] [04:54:20:486351] [Tid0x00005710] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000053] [04:54:20:486351] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000054] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000055] [04:54:20:486351] [Tid0x00005710] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000056] [04:54:20:486351] [Tid0x00005710] [debug] <--[C16] boot_rom::connect
[00000057] [04:54:20:486351] [Tid0x00005710] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000058] [04:54:20:486351] [Tid0x00005710] [debug] -->[C31] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000059] [04:54:20:486351] [Tid0x00005710] [debug] -->[C32] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000060] [04:54:20:486351] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000061] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000062] [04:54:20:486351] [Tid0x00005710] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000063] [04:54:20:486351] [Tid0x00005710] [debug] <--[C32] boot_rom::get_preloader_version
[00000064] [04:54:20:486351] [Tid0x00005710] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000065] [04:54:20:486351] [Tid0x00005710] [debug] <--[C31] boot_rom_logic::security_verify_connection
[00000066] [04:54:20:486351] [Tid0x00005710] [debug] <--[C9] connection::connect_brom
[00000067] [04:54:20:486351] [Tid0x00005710] [info] <--[C8] flashtool_connect_brom
[00000068] [04:54:20:486351] [Tid0x00005710] [info] -->[C35] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000069] [04:54:20:486351] [Tid0x00005710] [debug] -->[C36] connection::device_control #(connection.cpp, line:669)
[00000070] [04:54:20:486351] [Tid0x00005710] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000071] [04:54:20:486351] [Tid0x00005710] [debug] -->[C37] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000072] [04:54:20:486351] [Tid0x00005710] [debug] -->[C38] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000073] [04:54:20:486351] [Tid0x00005710] [info] get chip id  #(boot_rom.cpp, line:114)
[00000074] [04:54:20:486351] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000075] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000076] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000077] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000078] [04:54:20:486351] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000079] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000080] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000081] [04:54:20:486351] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000082] [04:54:20:487626] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000083] [04:54:20:487626] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000084] [04:54:20:487626] [Tid0x00005710] [debug] -->[C49] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000085] [04:54:20:487626] [Tid0x00005710] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000086] [04:54:20:487626] [Tid0x00005710] [debug] <--[C49] lib_config_parser::get_value
[00000087] [04:54:20:487626] [Tid0x00005710] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000088] [04:54:20:487626] [Tid0x00005710] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000089] [04:54:20:487626] [Tid0x00005710] [debug] <--[C38] boot_rom::get_chip_id
[00000090] [04:54:20:487626] [Tid0x00005710] [debug] <--[C37] boot_rom::device_control
[00000091] [04:54:20:487626] [Tid0x00005710] [debug] <--[C36] connection::device_control
[00000092] [04:54:20:487626] [Tid0x00005710] [info] <--[C35] flashtool_device_control
[00000093] [04:54:20:487626] [Tid0x00005710] [info] -->[C50] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000094] [04:54:20:487626] [Tid0x00005710] [debug] -->[C51] connection::device_control #(connection.cpp, line:669)
[00000095] [04:54:20:487626] [Tid0x00005710] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000096] [04:54:20:487626] [Tid0x00005710] [debug] -->[C52] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000097] [04:54:20:487626] [Tid0x00005710] [debug] -->[C53] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000098] [04:54:20:487626] [Tid0x00005710] [info] get chip id  #(boot_rom.cpp, line:114)
[00000099] [04:54:20:487626] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000100] [04:54:20:487626] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000101] [04:54:20:487626] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000102] [04:54:20:487626] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000103] [04:54:20:487626] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000104] [04:54:20:487626] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000105] [04:54:20:487626] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000106] [04:54:20:488629] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000107] [04:54:20:488629] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000108] [04:54:20:488629] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000109] [04:54:20:488629] [Tid0x00005710] [debug] -->[C64] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000110] [04:54:20:488629] [Tid0x00005710] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000111] [04:54:20:488629] [Tid0x00005710] [debug] <--[C64] lib_config_parser::get_value
[00000112] [04:54:20:488629] [Tid0x00005710] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000113] [04:54:20:488629] [Tid0x00005710] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000114] [04:54:20:488629] [Tid0x00005710] [debug] <--[C53] boot_rom::get_chip_id
[00000115] [04:54:20:488629] [Tid0x00005710] [debug] <--[C52] boot_rom::device_control
[00000116] [04:54:20:488629] [Tid0x00005710] [debug] <--[C51] connection::device_control
[00000117] [04:54:20:488629] [Tid0x00005710] [info] <--[C50] flashtool_device_control
[00000118] [04:54:20:488629] [Tid0x00005710] [debug] -->[C65] flashtool_get_com_handle #(undocument_api.cpp, line:126)
[00000119] [04:54:20:488629] [Tid0x00005710] [debug] <--[C65] flashtool_get_com_handle
[00000120] [04:54:20:489647] [Tid0x00005710] [info] -->[C66] flashtool_connect_da #(flashtoolex_api.cpp, line:134)
[00000121] [04:54:20:489647] [Tid0x00005710] [debug] -->[C67] connection::connect_da #(connection.cpp, line:258)
[00000122] [04:54:20:489647] [Tid0x00005710] [info] (1/7)connecting DA. #(connection.cpp, line:261)
[00000123] [04:54:20:489647] [Tid0x00005710] [info] (2/7)read DA config. #(connection.cpp, line:262)
[00000124] [04:54:20:489647] [Tid0x00005710] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\device.cfg.xml #(da_config_parser.cpp, line:34)
[00000125] [04:54:20:489647] [Tid0x00005710] [info] (3/7)read DA image file. #(connection.cpp, line:270)
[00000126] [04:54:20:489647] [Tid0x00005710] [debug] -->[C68] da_image::load #(da_image.cpp, line:24)
[00000127] [04:54:20:489647] [Tid0x00005710] [info] local file: C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin #(file_data_factory.cpp, line:22)
[00000128] [04:54:20:489647] [Tid0x00005710] [debug] <--[C68] da_image::load
[00000129] [04:54:20:489647] [Tid0x00005710] [info] (4/7)connecting 1st DA. #(connection.cpp, line:296)
[00000130] [04:54:20:489647] [Tid0x00005710] [debug] -->[C69] connection::connect_1st_da #(connection.cpp, line:358)
[00000131] [04:54:20:489647] [Tid0x00005710] [debug] -->[C70] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000132] [04:54:20:489647] [Tid0x00005710] [info] get chip id  #(boot_rom.cpp, line:114)
[00000133] [04:54:20:489647] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000134] [04:54:20:489647] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000135] [04:54:20:489647] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000136] [04:54:20:489647] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000137] [04:54:20:490651] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000138] [04:54:20:490651] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000139] [04:54:20:490651] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000140] [04:54:20:490651] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000141] [04:54:20:490651] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000142] [04:54:20:490651] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000143] [04:54:20:490651] [Tid0x00005710] [debug] -->[C81] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000144] [04:54:20:490651] [Tid0x00005710] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000145] [04:54:20:490651] [Tid0x00005710] [debug] <--[C81] lib_config_parser::get_value
[00000146] [04:54:20:490651] [Tid0x00005710] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000147] [04:54:20:490651] [Tid0x00005710] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000148] [04:54:20:490651] [Tid0x00005710] [debug] <--[C70] boot_rom::get_chip_id
[00000149] [04:54:20:490651] [Tid0x00005710] [info] select DA use chip id. #(connection.cpp, line:379)
[00000150] [04:54:20:490651] [Tid0x00005710] [debug] -->[C82] da_image::select #(da_image.cpp, line:59)
[00000151] [04:54:20:490651] [Tid0x00005710] [info] search DA: MT6765 #(da_image.cpp, line:65)
[00000152] [04:54:20:490651] [Tid0x00005710] [info] da_description: MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327 #(da_image.cpp, line:67)
[00000153] [04:54:20:490651] [Tid0x00005710] [info] use DA index: 0x0 #(da_parser_v04.cpp, line:29)
[00000154] [04:54:20:490651] [Tid0x00005710] [debug] <--[C82] da_image::select
[00000155] [04:54:20:490651] [Tid0x00005710] [info] get 1st DA data. #(connection.cpp, line:391)
[00000156] [04:54:20:490651] [Tid0x00005710] [debug] -->[C83] da_image::get_section_data #(da_image.cpp, line:94)
[00000157] [04:54:20:490651] [Tid0x00005710] [debug] <--[C83] da_image::get_section_data
[00000158] [04:54:20:490651] [Tid0x00005710] [info] send 1st DA data to loader. #(connection.cpp, line:398)
[00000159] [04:54:20:490651] [Tid0x00005710] [debug] -->[C84] boot_rom::send_da #(boot_rom.cpp, line:248)
[00000160] [04:54:20:490651] [Tid0x00005710] [info] send DA data to boot rom.  #(boot_rom.cpp, line:249)
[00000161] [04:54:20:490651] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[d7 ]
[00000162] [04:54:20:490651] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[d7 ]
[00000163] [04:54:20:490651] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 20 00 00 ]
[00000164] [04:54:20:490651] [Tid0x00005710] [debug] 			<-Rx: 0x00000004 Hex[00 20 00 00 ]
[00000165] [04:54:20:491651] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 03 9b 88 ]
[00000166] [04:54:20:491651] [Tid0x00005710] [debug] 			<-Rx: 0x00000004 Hex[00 03 9b 88 ]
[00000167] [04:54:20:491651] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 00 01 00 ]
[00000168] [04:54:20:491651] [Tid0x00005710] [debug] 			<-Rx: 0x00000004 Hex[00 00 01 00 ]
[00000169] [04:54:20:491651] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000170] [04:54:20:494650] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[ff ff ff ea cc 14 00 fb 00 00 0f e1 c0 10 a0 e3 ]
[00000171] [04:54:20:497650] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[03 f0 45 fa 25 e0 00 21 19 60 d3 e9 02 45 de e9 ]
[00000172] [04:54:20:501697] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[51 ec 30 0b fc f7 88 ef 80 46 89 46 fe f7 c4 f8 ]
[00000173] [04:54:20:504698] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[1b 88 e3 80 fc f7 c8 f8 02 28 03 d8 17 48 21 88 ]
[00000174] [04:54:20:507699] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[09 4a 00 93 b3 23 fd f7 6a fa fe e7 02 b0 10 bd ]
[00000175] [04:54:20:511077] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[4b 43 94 f8 32 11 59 43 fb f7 41 fa f8 f7 c4 f8 ]
[00000176] [04:54:20:515114] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[f7 b5 05 46 90 f8 4d 70 0c 46 08 46 4f f4 3c 72 ]
[00000177] [04:54:20:518122] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[28 46 62 65 23 66 63 4b 63 66 08 eb 01 03 83 f8 ]
[00000178] [04:54:20:521638] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[31 30 64 23 e3 61 06 e0 94 f8 31 30 02 2b 18 bf ]
[00000179] [04:54:20:524629] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[f2 60 1c b1 01 21 0a 46 0b 46 02 e0 01 22 21 46 ]
[00000180] [04:54:20:528641] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[90 45 1b d1 25 e0 3d 4a 00 23 99 45 08 bf 90 45 ]
[00000181] [04:54:20:531806] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[03 62 28 9b 1a 43 ad 4b 42 ea 07 42 07 f0 cf fc ]
[00000182] [04:54:20:534807] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[00 22 4f f0 00 43 09 04 01 f5 20 21 b0 31 05 f0 ]
[00000183] [04:54:20:537806] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[c6 49 4f f0 33 32 4f f0 77 33 03 f0 9f fc 20 46 ]
[00000184] [04:54:20:541693] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[13 46 78 49 01 f0 a2 fc 20 46 7b 49 4f f4 18 42 ]
[00000185] [04:54:20:545686] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[c0 21 34 31 ff f7 a2 fc 61 78 20 46 2a 46 03 23 ]
[00000186] [04:54:20:548685] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[0a f1 a8 03 ac 35 01 93 0a f1 ac 0a d9 f8 04 30 ]
[00000187] [04:54:20:552043] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[20 46 29 46 9d f8 0c 20 ff f7 1a fb e3 78 23 44 ]
[00000188] [04:54:20:555033] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[f9 f7 7a ff 20 46 84 21 19 22 f9 f7 75 ff 20 46 ]
[00000189] [04:54:20:558034] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[d2 b2 1a 43 f7 f7 98 fc 9d f8 94 20 9d f8 9b e0 ]
[00000190] [04:54:20:562554] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[4c 14 06 00 04 00 06 00 58 00 06 00 00 00 38 80 ]
[00000191] [04:54:20:565561] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[dd e9 06 23 cd e9 08 23 15 e0 01 22 30 46 8d e8 ]
[00000192] [04:54:20:568554] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[43 54 29 0a 00 62 61 74 74 65 72 79 5f 65 78 69 ]
[00000193] [04:54:20:572892] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[79 5d 6d 65 6d 63 70 79 20 74 6f 74 61 6c 20 73 ]
[00000194] [04:54:20:575881] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[20 20 20 20 3d 20 30 78 25 78 0a 00 52 65 67 5b ]
[00000195] [04:54:20:579385] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[44 52 20 69 73 20 69 6e 20 73 65 6c 66 2d 72 65 ]
[00000196] [04:54:20:582395] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[4c 50 33 20 66 6f 72 20 52 4b 25 64 0a 00 6d 69 ]
[00000197] [04:54:20:586395] [Tid0x00005710] [debug] 			Tx->: 0x00002000 Hex[49 4f 43 4b 20 6a 69 74 74 65 72 20 6d 65 74 65 ]
[00000198] [04:54:20:588387] [Tid0x00005710] [debug] 			Tx->: 0x00001b88 Hex[54 52 4c 30 20 69 6e 66 6f 72 6d 61 74 69 6f 6e ]
[00000199] [04:54:20:607784] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[74 34 ]
[00000200] [04:54:20:612288] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000201] [04:54:20:612288] [Tid0x00005710] [debug] <--[C84] boot_rom::send_da
[00000202] [04:54:20:612288] [Tid0x00005710] [info] jump to 1st DA. #(connection.cpp, line:405)
[00000203] [04:54:20:612288] [Tid0x00005710] [debug] -->[C125] boot_rom::jump_to_da #(boot_rom.cpp, line:349)
[00000204] [04:54:20:612288] [Tid0x00005710] [debug] -->[C126] boot_rom::jump_to #(boot_rom.cpp, line:396)
[00000205] [04:54:20:612288] [Tid0x00005710] [info] boot rom jump to. #(boot_rom.cpp, line:397)
[00000206] [04:54:20:612288] [Tid0x00005710] [debug] 			Tx->: 0x00000001 Hex[d5 ]
[00000207] [04:54:20:612288] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[d5 ]
[00000208] [04:54:20:612288] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 20 00 00 ]
[00000209] [04:54:20:612288] [Tid0x00005710] [debug] 			<-Rx: 0x00000004 Hex[00 20 00 00 ]
[00000210] [04:54:20:612288] [Tid0x00005710] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000211] [04:54:20:612288] [Tid0x00005710] [debug] <--[C126] boot_rom::jump_to
[00000212] [04:54:20:612288] [Tid0x00005710] [info] Begin receive DA 1st sync char #(boot_rom.cpp, line:357)
[00000213] [04:54:20:699524] [Tid0x00005710] [debug] 			<-Rx: 0x00000001 Hex[c0 ]
[00000214] [04:54:20:699524] [Tid0x00005710] [debug] <--[C125] boot_rom::jump_to_da
[00000215] [04:54:20:699524] [Tid0x00005710] [debug] -->[C133] device_instance::reset #(device_instance.cpp, line:45)
[00000216] [04:54:20:699524] [Tid0x00005710] [info] -->[C134] data_mux::stop #(data_mux.cpp, line:92)
[00000217] [04:54:20:699524] [Tid0x00005710] [info] <--[C134] data_mux::stop
[00000218] [04:54:20:699524] [Tid0x00005710] [debug] worker_thr id: 7d2c
[00000219] [04:54:20:699524] [Tid0x00005710] [debug] monitor_thr id: 2b0
[00000220] [04:54:20:700998] [Tid0x00005710] [debug] -->[C135] device_log_source::reset #(device_log_source.cpp, line:17)
[00000221] [04:54:20:700998] [Tid0x00005710] [info] -->[C136] device_log_source::stop #(device_log_source.cpp, line:29)
[00000222] [04:54:20:700998] [Tid0x00005710] [info] <--[C136] device_log_source::stop
[00000223] [04:54:20:700998] [Tid0x00005710] [debug] <--[C135] device_log_source::reset
[00000224] [04:54:20:700998] [Tid0x00005710] [debug] <--[C133] device_instance::reset
[00000225] [04:54:20:700998] [Tid0x00005710] [info] Start send SYNC signal.  #(device_instance.cpp, line:116) #(device_instance.cpp, line:116)
[00000226] [04:54:20:700998] [Tid0x00007d2c] [info] -->[C137] data_mux::worker #(data_mux.cpp, line:163)
[00000227] [04:54:20:700998] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000228] [04:54:20:700998] [Tid0x000002b0] [info] -->[C140] data_mux::monitor #(data_mux.cpp, line:121)
[00000229] [04:54:20:700998] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[53 59 4e 43 ]
[00000230] [04:54:20:700998] [Tid0x00005710] [info] setup device environment #(connection.cpp, line:420)
[00000231] [04:54:20:700998] [Tid0x00005710] [info] setting da_log_level & da_log_channel & forbidden ufs provision by UI #(connection.cpp, line:423)
[00000232] [04:54:20:700998] [Tid0x00005710] [info] -->[C142] device_instance::setup_device_environment #(device_instance.cpp, line:156)
[00000233] [04:54:20:700998] [Tid0x00005710] [info] /CMD/: SPECIAL_CMD_SETUP_ENVIRONMENT=0x10100 #(device_instance.cpp, line:160)
[00000234] [04:54:20:700998] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000235] [04:54:20:700998] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 01 01 00 ]
[00000236] [04:54:20:700998] [Tid0x00005710] [info] struct: (uint)da_log_level, (uint)log_channel, (uint)system_os, (uint)reserve2.bit[0][1]-ufs_provision, (uint)reserve3. #(device_instance.cpp, line:164)
[00000237] [04:54:20:700998] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 14 00 00 00 ]
[00000238] [04:54:20:701997] [Tid0x00005710] [debug] 			Tx->: 0x00000014 Hex[02 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 ]
[00000239] [04:54:20:701997] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000240] [04:54:20:701997] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000241] [04:54:20:701997] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:192)
[00000242] [04:54:20:701997] [Tid0x00005710] [info] <--[C142] device_instance::setup_device_environment
[00000243] [04:54:20:701997] [Tid0x00005710] [info] get special device init parameters. #(connection.cpp, line:438)
[00000244] [04:54:20:701997] [Tid0x00005710] [info] No device parameter config file. skip. #(dev_param_config_parser.cpp, line:109)
[00000245] [04:54:20:902709] [Tid0x00005710] [info] setup special device init parameters. #(connection.cpp, line:448)
[00000246] [04:54:20:902709] [Tid0x00005710] [info] -->[C149] device_instance::setup_device_init_parameters #(device_instance.cpp, line:198)
[00000247] [04:54:20:902709] [Tid0x00005710] [info] /CMD/: SPECIAL_CMD_SETUP_HW_INIT_PARAMS=0x10101 #(device_instance.cpp, line:202)
[00000248] [04:54:20:902709] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000249] [04:54:20:902709] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[01 01 01 00 ]
[00000250] [04:54:20:902709] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000251] [04:54:20:902709] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000252] [04:54:20:903709] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000253] [04:54:20:903709] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000254] [04:54:20:903709] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:233)
[00000255] [04:54:20:903709] [Tid0x00005710] [info] <--[C149] device_instance::setup_device_init_parameters
[00000256] [04:54:20:903709] [Tid0x00005710] [info] wait for 1st DA stable sync signal. #(connection.cpp, line:456)
[00000257] [04:54:20:903709] [Tid0x00005710] [info] Receive SYNC signal. #(device_instance.cpp, line:126)
[00000258] [04:54:21:041865] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000259] [04:54:21:041865] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[53 59 4e 43 ]
[00000260] [04:54:21:042864] [Tid0x00005710] [debug] <--[C69] connection::connect_1st_da
[00000261] [04:54:21:042864] [Tid0x00005710] [info] (5/7)send DA some parameters. #(connection.cpp, line:304)
[00000262] [04:54:21:042864] [Tid0x00005710] [debug] -->[C158] connection::set_da_ctrls #(connection.cpp, line:463)
[00000263] [04:54:21:042864] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000264] [04:54:21:042864] [Tid0x00005710] [info] /control-code/: CC_GET_EXPIRE_DATE=0x40011 #(device_instance.cpp, line:350)
[00000265] [04:54:21:042864] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000266] [04:54:21:042864] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000267] [04:54:21:042864] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000268] [04:54:21:042864] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000269] [04:54:21:043865] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000270] [04:54:21:043865] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[11 00 04 00 ]
[00000271] [04:54:21:043865] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000272] [04:54:21:043865] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[04 00 01 c0 ]
[00000273] [04:54:21:043865] [Tid0x00005710] [warning] device control code not support. #(device_instance.cpp, line:371)
[00000274] [04:54:21:043865] [Tid0x00005710] [info] DA expired date: 2099.1.1 #(connection.cpp, line:494)
[00000275] [04:54:21:043865] [Tid0x00005710] [info] send reset key setting: 0x0  ##default[0x0]. 1 key[0x50]. 2 key[0x68] #(connection.cpp, line:501)
[00000276] [04:54:21:043865] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000277] [04:54:21:043865] [Tid0x00005710] [info] /control-code/: CC_SET_RESET_KEY=0x20004 #(device_instance.cpp, line:350)
[00000278] [04:54:21:043865] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000279] [04:54:21:044872] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000280] [04:54:21:044872] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000281] [04:54:21:044872] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000282] [04:54:21:044872] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000283] [04:54:21:044872] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[04 00 02 00 ]
[00000284] [04:54:21:044872] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000285] [04:54:21:045873] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000286] [04:54:21:045873] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000287] [04:54:21:045873] [Tid0x00005710] [info] send parameters. #(device_instance.cpp, line:379)
[00000288] [04:54:21:045873] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000289] [04:54:21:045873] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000290] [04:54:21:045873] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000291] [04:54:21:046864] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000292] [04:54:21:046864] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000293] [04:54:21:046864] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000294] [04:54:21:046864] [Tid0x00005710] [info] send battery setting: 0x0 ##battery[0x0]. USB power[0x1]. auto[0x2] #(connection.cpp, line:510)
[00000295] [04:54:21:046864] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000296] [04:54:21:046864] [Tid0x00005710] [info] /control-code/: CC_SET_BATTERY_OPT=0x20002 #(device_instance.cpp, line:350)
[00000297] [04:54:21:046864] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000298] [04:54:21:046864] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000299] [04:54:21:047864] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000300] [04:54:21:047864] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000301] [04:54:21:047864] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000302] [04:54:21:047864] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[02 00 02 00 ]
[00000303] [04:54:21:047864] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000304] [04:54:21:047864] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000305] [04:54:21:047864] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000306] [04:54:21:047864] [Tid0x00005710] [info] send parameters. #(device_instance.cpp, line:379)
[00000307] [04:54:21:047864] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000308] [04:54:21:047864] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000309] [04:54:21:047864] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000310] [04:54:21:136770] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000311] [04:54:21:136770] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000312] [04:54:21:136770] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000313] [04:54:21:136770] [Tid0x00005710] [info] send checksum level setting: 0x3 ##none[0x0]. USB[0x1]. storage[0x2], both[0x3] #(connection.cpp, line:519)
[00000314] [04:54:21:136770] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000315] [04:54:21:136770] [Tid0x00005710] [info] /control-code/: CC_SET_CHECKSUM_LEVEL=0x20003 #(device_instance.cpp, line:350)
[00000316] [04:54:21:136770] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000317] [04:54:21:137771] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000318] [04:54:21:137771] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000319] [04:54:21:137771] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000320] [04:54:21:137771] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000321] [04:54:21:137771] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[03 00 02 00 ]
[00000322] [04:54:21:137771] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000323] [04:54:21:137771] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000324] [04:54:21:138770] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000325] [04:54:21:138770] [Tid0x00005710] [info] send parameters. #(device_instance.cpp, line:379)
[00000326] [04:54:21:138770] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000327] [04:54:21:138770] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[03 00 00 00 ]
[00000328] [04:54:21:138770] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000329] [04:54:21:139770] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000330] [04:54:21:139770] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000331] [04:54:21:139770] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000332] [04:54:21:139770] [Tid0x00005710] [debug] <--[C158] connection::set_da_ctrls
[00000333] [04:54:21:139770] [Tid0x00005710] [info] Check whether need to enter 2nd DA, need_2nd_da: 1 #(connection.cpp, line:323)
[00000334] [04:54:21:139770] [Tid0x00005710] [info] (6/7)eanble DRAM #(connection.cpp, line:329)
[00000335] [04:54:21:139770] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000336] [04:54:21:139770] [Tid0x00005710] [info] /control-code/: CC_GET_CONNECTION_AGENT=0x4000a #(device_instance.cpp, line:350)
[00000337] [04:54:21:139770] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000338] [04:54:21:139770] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000339] [04:54:21:139770] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000340] [04:54:21:139770] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000341] [04:54:21:139770] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000342] [04:54:21:139770] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[0a 00 04 00 ]
[00000343] [04:54:21:141175] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000344] [04:54:21:141175] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000345] [04:54:21:141175] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000346] [04:54:21:141175] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000347] [04:54:21:142178] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 09 00 00 00 ]
[00000348] [04:54:21:142178] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000009 Hex[70 72 65 6c 6f 61 64 65 72 ]
[00000349] [04:54:21:142178] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000350] [04:54:21:142178] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000351] [04:54:21:142178] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000352] [04:54:21:142178] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000353] [04:54:21:142178] [Tid0x00005710] [info] preloader alive. skip initializing external DRAM. #(connection.cpp, line:216)
[00000354] [04:54:21:142178] [Tid0x00005710] [info] (7/7)connecting 2nd DA. #(connection.cpp, line:337)
[00000355] [04:54:21:142178] [Tid0x00005710] [debug] -->[C220] connection::connect_2nd_da #(connection.cpp, line:617)
[00000356] [04:54:21:142178] [Tid0x00005710] [info] get 2nd DA data #(connection.cpp, line:629)
[00000357] [04:54:21:142178] [Tid0x00005710] [debug] -->[C221] da_image::get_section_data #(da_image.cpp, line:94)
[00000358] [04:54:21:142178] [Tid0x00005710] [debug] <--[C221] da_image::get_section_data
[00000359] [04:54:21:143178] [Tid0x00005710] [info] jump to 2nd DA #(connection.cpp, line:637)
[00000360] [04:54:21:143178] [Tid0x00005710] [debug] -->[C222] device_instance::boot_to #(device_instance.cpp, line:456)
[00000361] [04:54:21:143178] [Tid0x00005710] [info] /CMD/: CMD_BOOT_TO=0x10008 #(device_instance.cpp, line:462)
[00000362] [04:54:21:143178] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000363] [04:54:21:143178] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[08 00 01 00 ]
[00000364] [04:54:21:143178] [Tid0x00005710] [info] receive response for command check. #(device_instance.cpp, line:466)
[00000365] [04:54:21:143178] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000366] [04:54:21:143178] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000367] [04:54:21:143178] [Tid0x00005710] [info] send: at_address[0x40000000], length[0x4c0dc] #(device_instance.cpp, line:480)
[00000368] [04:54:21:143178] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 10 00 00 00 ]
[00000369] [04:54:21:143178] [Tid0x00005710] [debug] 			Tx->: 0x00000010 Hex[00 00 00 40 00 00 00 00 dc c0 04 00 00 00 00 00 ]
[00000370] [04:54:21:144178] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 dc c0 04 00 ]
[00000371] [04:54:21:155708] [Tid0x00005710] [debug] 			Tx->: 0x0004c0dc Hex[07 00 00 ea 94 00 00 ea 9a 00 00 ea a0 00 00 ea ]
[00000372] [04:54:21:159699] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000373] [04:54:21:159699] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000374] [04:54:21:159699] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:495)
[00000375] [04:54:21:159699] [Tid0x00005710] [debug] <--[C222] device_instance::boot_to
[00000376] [04:54:21:159699] [Tid0x00005710] [info] wait for 2nd DA stable sync signal. #(connection.cpp, line:645)
[00000377] [04:54:21:159699] [Tid0x00005710] [info] Receive SYNC signal. #(device_instance.cpp, line:126)
[00000378] [04:54:21:295954] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000379] [04:54:21:295954] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[53 59 4e 43 ]
[00000380] [04:54:21:296955] [Tid0x00005710] [info] -->[C235] download::do_optional_server_security_check #(download.cpp, line:135)
[00000381] [04:54:21:296955] [Tid0x00005710] [info] <--[C235] download::do_optional_server_security_check
[00000382] [04:54:21:296955] [Tid0x00005710] [debug] <--[C220] connection::connect_2nd_da
[00000383] [04:54:21:296955] [Tid0x00005710] [debug] <--[C67] connection::connect_da
[00000384] [04:54:21:296955] [Tid0x00005710] [info] <--[C66] flashtool_connect_da
[00000385] [04:54:21:296955] [Tid0x00005710] [info] -->[C236] flashtool_get_device_info #(flashtoolex_api.cpp, line:162)
[00000386] [04:54:21:296955] [Tid0x00005710] [debug] -->[C237] logic::get_device_info #(logic.cpp, line:773)
[00000387] [04:54:21:296955] [Tid0x00005710] [info] -->[C238] device_instance::get_dev_info #(device_instance.cpp, line:1239)
[00000388] [04:54:21:296955] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000389] [04:54:21:296955] [Tid0x00005710] [info] /control-code/: CC_GET_RAM_INFO=0x4000c #(device_instance.cpp, line:350)
[00000390] [04:54:21:296955] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000391] [04:54:21:296955] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000392] [04:54:21:296955] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000393] [04:54:21:296955] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000394] [04:54:21:296955] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000395] [04:54:21:296955] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[0c 00 04 00 ]
[00000396] [04:54:21:297967] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000397] [04:54:21:297967] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000398] [04:54:21:297967] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000399] [04:54:21:297967] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000400] [04:54:21:297967] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 30 00 00 00 ]
[00000401] [04:54:21:297967] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000030 Hex[02 00 00 00 00 00 00 00 00 00 20 00 00 00 00 00 ]
[00000402] [04:54:21:297967] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000403] [04:54:21:299472] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000404] [04:54:21:299472] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000405] [04:54:21:299472] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000406] [04:54:21:299472] [Tid0x00005710] [info] SRAM: type[0x2] base_address[0x200000] size[0x3a000] #(device_instance.cpp, line:1258)
[00000407] [04:54:21:299472] [Tid0x00005710] [info] DRAM: type[0x1] base_address[0x40000000] size[0x100000000] #(device_instance.cpp, line:1262)
[00000408] [04:54:21:299472] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000409] [04:54:21:299472] [Tid0x00005710] [info] /control-code/: CC_GET_EMMC_INFO=0x40001 #(device_instance.cpp, line:350)
[00000410] [04:54:21:300475] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000411] [04:54:21:300475] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000412] [04:54:21:300475] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000413] [04:54:21:300475] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000414] [04:54:21:300475] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000415] [04:54:21:300475] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[01 00 04 00 ]
[00000416] [04:54:21:300475] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000417] [04:54:21:300475] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000418] [04:54:21:300475] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000419] [04:54:21:301475] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000420] [04:54:21:301475] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 68 00 00 00 ]
[00000421] [04:54:21:301475] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000068 Hex[01 00 00 00 00 02 00 00 00 00 40 00 00 00 00 00 ]
[00000422] [04:54:21:301475] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000423] [04:54:21:302478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000424] [04:54:21:302478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000425] [04:54:21:302478] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000426] [04:54:21:302478] [Tid0x00005710] [info] EMMC: type[0x1] #size: boot1[0x400000] boot2[0x400000] user[0x1ccf000000] rpmb[0x1000000] gp1[0x0] gp2[0x0] gp3[0x0] gp4[0x0] #(device_instance.cpp, line:1283)
[00000427] [04:54:21:302478] [Tid0x00005710] [info] EMMC: cid[0x410301d6 0x34344133 0xbf391032 0x25b824d7] fwver[0x0] #(device_instance.cpp, line:1286)
[00000428] [04:54:21:302478] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000429] [04:54:21:302478] [Tid0x00005710] [info] /control-code/: CC_GET_NAND_INFO=0x40002 #(device_instance.cpp, line:350)
[00000430] [04:54:21:302478] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000431] [04:54:21:302478] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000432] [04:54:21:303478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000433] [04:54:21:303478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000434] [04:54:21:303478] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000435] [04:54:21:303478] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[02 00 04 00 ]
[00000436] [04:54:21:303478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000437] [04:54:21:303478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000438] [04:54:21:303478] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000439] [04:54:21:303478] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000440] [04:54:21:304478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 30 00 00 00 ]
[00000441] [04:54:21:304478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000030 Hex[00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ]
[00000442] [04:54:21:304478] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000443] [04:54:21:304478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000444] [04:54:21:304478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000445] [04:54:21:304478] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000446] [04:54:21:304478] [Tid0x00005710] [info] NAND: type[0x0] #size: page[0x0] block[0x0] spare[0x0] total[0x0] available[0x0] #(device_instance.cpp, line:1309)
[00000447] [04:54:21:304478] [Tid0x00005710] [info] NAND: id[0x0 0x0 0x0]  #(device_instance.cpp, line:1313)
[00000448] [04:54:21:304478] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000449] [04:54:21:304478] [Tid0x00005710] [info] /control-code/: CC_GET_NOR_INFO=0x40003 #(device_instance.cpp, line:350)
[00000450] [04:54:21:304478] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000451] [04:54:21:305478] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000452] [04:54:21:305478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000453] [04:54:21:305478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000454] [04:54:21:305478] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000455] [04:54:21:305478] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[03 00 04 00 ]
[00000456] [04:54:21:305478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000457] [04:54:21:305478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000458] [04:54:21:305478] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000459] [04:54:21:305478] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000460] [04:54:21:306479] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 10 00 00 00 ]
[00000461] [04:54:21:306479] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000010 Hex[00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ]
[00000462] [04:54:21:306479] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000463] [04:54:21:306479] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000464] [04:54:21:306479] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000465] [04:54:21:306479] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000466] [04:54:21:306479] [Tid0x00005710] [info] NOR: type[0x0] #size: page[0x0] available[0x0] #(device_instance.cpp, line:1334)
[00000467] [04:54:21:306479] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000468] [04:54:21:306479] [Tid0x00005710] [info] /control-code/: CC_GET_UFS_INFO=0x40004 #(device_instance.cpp, line:350)
[00000469] [04:54:21:306479] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000470] [04:54:21:307478] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000471] [04:54:21:307478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000472] [04:54:21:307478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000473] [04:54:21:307478] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000474] [04:54:21:307478] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[04 00 04 00 ]
[00000475] [04:54:21:307478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000476] [04:54:21:307478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000477] [04:54:21:307478] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000478] [04:54:21:307478] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000479] [04:54:21:308478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 d0 00 00 00 ]
[00000480] [04:54:21:308478] [Tid0x00007d2c] [debug] 			<-Rx: 0x000000d0 Hex[00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ]
[00000482] [04:54:21:308478] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000481] [04:54:21:308478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000483] [04:54:21:308478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000484] [04:54:21:308478] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000485] [04:54:21:308478] [Tid0x00005710] [info] UFS: type[0x0] #size: lu0[0x0] lu1[0x0] lu2[0x0] #(device_instance.cpp, line:1354)
[00000486] [04:54:21:308478] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000487] [04:54:21:308478] [Tid0x00005710] [info] /control-code/: CC_GET_CHIP_ID=0x4000d #(device_instance.cpp, line:350)
[00000488] [04:54:21:308478] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000489] [04:54:21:309478] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000490] [04:54:21:309478] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000491] [04:54:21:309478] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000492] [04:54:21:309478] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000493] [04:54:21:309981] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[0d 00 04 00 ]
[00000494] [04:54:21:309981] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000495] [04:54:21:309981] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000496] [04:54:21:309981] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000497] [04:54:21:309981] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000498] [04:54:21:310983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 0c 00 00 00 ]
[00000499] [04:54:21:310983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[66 07 00 8a 00 ca 00 00 00 00 00 00 ]
[00000500] [04:54:21:310983] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000501] [04:54:21:311983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000502] [04:54:21:311983] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000503] [04:54:21:311983] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000504] [04:54:21:311983] [Tid0x00005710] [debug] -->[C317] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000505] [04:54:21:311983] [Tid0x00005710] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000506] [04:54:21:311983] [Tid0x00005710] [debug] <--[C317] lib_config_parser::get_value
[00000507] [04:54:21:311983] [Tid0x00005710] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000508] [04:54:21:311983] [Tid0x00005710] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] chip_evolution[0x0] #(device_instance.cpp, line:1384)
[00000509] [04:54:21:311983] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000510] [04:54:21:311983] [Tid0x00005710] [info] /control-code/: CC_GET_RAMDOM_ID=0x40008 #(device_instance.cpp, line:350)
[00000511] [04:54:21:311983] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000512] [04:54:21:311983] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000513] [04:54:21:311983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000514] [04:54:21:311983] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000515] [04:54:21:311983] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000516] [04:54:21:312983] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[08 00 04 00 ]
[00000517] [04:54:21:312983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000518] [04:54:21:312983] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000519] [04:54:21:312983] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000520] [04:54:21:312983] [Tid0x00005710] [info] receive results. #(device_instance.cpp, line:385)
[00000521] [04:54:21:312983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 10 00 00 00 ]
[00000522] [04:54:21:313982] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000010 Hex[bb 9d 6c a3 7c 51 39 c9 00 b5 29 0d 06 a3 b1 59 ]
[00000523] [04:54:21:313982] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000524] [04:54:21:313982] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000525] [04:54:21:313982] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000526] [04:54:21:313982] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000527] [04:54:21:313982] [Tid0x00005710] [info] RANDOM ID: [0xa36c9dbb 0xc939517c 0xd29b500 0x59b1a306] #(device_instance.cpp, line:1401)
[00000528] [04:54:21:313982] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:1413)
[00000529] [04:54:21:313982] [Tid0x00005710] [info] <--[C238] device_instance::get_dev_info
[00000530] [04:54:21:313982] [Tid0x00005710] [debug] <--[C237] logic::get_device_info
[00000531] [04:54:21:313982] [Tid0x00005710] [info] <--[C236] flashtool_get_device_info
[00000532] [04:54:21:316984] [Tid0x00005710] [info] -->[C331] flashtool_format #(flashtoolex_api.cpp, line:285)
[00000533] [04:54:21:316984] [Tid0x00005710] [info] -->[C332] logic::format #(logic.cpp, line:850)
[00000534] [04:54:21:316984] [Tid0x00005710] [info] format storage. #(logic.cpp, line:859)
[00000535] [04:54:21:316984] [Tid0x00005710] [info] Format:send host info. 20250416&045421. [Machine]:EngyPC [User]: [OS]:Windows 8 #(logic.cpp, line:878)
[00000536] [04:54:21:316984] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000537] [04:54:21:316984] [Tid0x00005710] [info] /control-code/: CC_SET_HOST_INFO=0x20005 #(device_instance.cpp, line:350)
[00000538] [04:54:21:316984] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000539] [04:54:21:316984] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000540] [04:54:21:316984] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000541] [04:54:21:317983] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000542] [04:54:21:317983] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000543] [04:54:21:317983] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[05 00 02 00 ]
[00000544] [04:54:21:317983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000545] [04:54:21:317983] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[04 00 01 c0 ]
[00000546] [04:54:21:317983] [Tid0x00005710] [warning] device control code not support. #(device_instance.cpp, line:371)
[00000547] [04:54:21:317983] [Tid0x00005710] [info] type: manual erase. #(logic.cpp, line:888)
[00000548] [04:54:21:317983] [Tid0x00005710] [info] -->[C342] device_instance::get_dev_info #(device_instance.cpp, line:1239)
[00000549] [04:54:21:317983] [Tid0x00005710] [info] <--[C342] device_instance::get_dev_info
[00000550] [04:54:21:317983] [Tid0x00005710] [debug] storage: 1, section: 1, address: 0x0, length: 0x400000, level:0, addr_type: 0 #(format_strategy.cpp, line:37)
[00000551] [04:54:21:317983] [Tid0x00005710] [info] -->[C343] device_instance::get_dev_info #(device_instance.cpp, line:1239)
[00000552] [04:54:21:317983] [Tid0x00005710] [info] <--[C343] device_instance::get_dev_info
[00000553] [04:54:21:317983] [Tid0x00005710] [info] send format status.  #(format_strategy.cpp, line:94)
[00000554] [04:54:21:317983] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000555] [04:54:21:317983] [Tid0x00005710] [info] /control-code/: CC_START_DL_INFO=0x80001 #(device_instance.cpp, line:350)
[00000556] [04:54:21:317983] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000557] [04:54:21:317983] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000558] [04:54:21:317983] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000559] [04:54:21:317983] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000560] [04:54:21:317983] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000561] [04:54:21:317983] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[01 00 08 00 ]
[00000562] [04:54:21:319486] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000563] [04:54:21:319486] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000564] [04:54:21:319486] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000565] [04:54:21:319486] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000566] [04:54:21:323490] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000567] [04:54:21:323490] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000568] [04:54:21:323490] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000569] [04:54:21:323490] [Tid0x00005710] [debug] -->[C355] device_instance::format #(device_instance.cpp, line:1119)
[00000570] [04:54:21:323490] [Tid0x00005710] [info] /CMD/: CMD_FORMAT=0x10003 #(device_instance.cpp, line:1128)
[00000571] [04:54:21:324491] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000572] [04:54:21:324491] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[03 00 01 00 ]
[00000573] [04:54:21:324491] [Tid0x00005710] [info] receive response for command check. #(device_instance.cpp, line:1132)
[00000574] [04:54:21:324491] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000575] [04:54:21:324491] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000576] [04:54:21:324491] [Tid0x00005710] [info] send: parameters. #(device_instance.cpp, line:1142)
[00000577] [04:54:21:324491] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 38 00 00 00 ]
[00000578] [04:54:21:324491] [Tid0x00005710] [debug] 			Tx->: 0x00000038 Hex[01 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 ]
[00000579] [04:54:21:324491] [Tid0x00005710] [info] parameter check status. #(device_instance.cpp, line:1145)
[00000580] [04:54:21:327500] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000581] [04:54:21:327500] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000582] [04:54:21:337993] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000583] [04:54:21:337993] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[04 00 04 40 ]
[00000584] [04:54:21:337993] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000585] [04:54:21:337993] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[64 00 00 00 ]
[00000586] [04:54:21:339495] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000587] [04:54:21:339495] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000588] [04:54:21:341499] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000589] [04:54:21:342581] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[05 00 04 40 ]
[00000590] [04:54:21:342581] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:1189)
[00000591] [04:54:21:342581] [Tid0x00005710] [debug] <--[C355] device_instance::format
[00000592] [04:54:21:342581] [Tid0x00005710] [info] <--[C332] logic::format
[00000593] [04:54:21:342581] [Tid0x00005710] [info] <--[C331] flashtool_format
[00000594] [04:54:21:342581] [Tid0x00005710] [info] -->[C372] flashtool_format #(flashtoolex_api.cpp, line:285)
[00000595] [04:54:21:342581] [Tid0x00005710] [info] -->[C373] logic::format #(logic.cpp, line:850)
[00000596] [04:54:21:342581] [Tid0x00005710] [info] format storage. #(logic.cpp, line:859)
[00000597] [04:54:21:342581] [Tid0x00005710] [info] Format:send host info. 20250416&045421. [Machine]:EngyPC [User]: [OS]:Windows 8 #(logic.cpp, line:878)
[00000598] [04:54:21:342581] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000599] [04:54:21:342581] [Tid0x00005710] [info] /control-code/: CC_SET_HOST_INFO=0x20005 #(device_instance.cpp, line:350)
[00000600] [04:54:21:342581] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000601] [04:54:21:342581] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000602] [04:54:21:343581] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000603] [04:54:21:343581] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000604] [04:54:21:343581] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000605] [04:54:21:343581] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[05 00 02 00 ]
[00000606] [04:54:21:343581] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000607] [04:54:21:343581] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[04 00 01 c0 ]
[00000608] [04:54:21:343581] [Tid0x00005710] [warning] device control code not support. #(device_instance.cpp, line:371)
[00000609] [04:54:21:343581] [Tid0x00005710] [info] type: manual erase. #(logic.cpp, line:888)
[00000610] [04:54:21:343581] [Tid0x00005710] [info] -->[C383] device_instance::get_dev_info #(device_instance.cpp, line:1239)
[00000611] [04:54:21:343581] [Tid0x00005710] [info] <--[C383] device_instance::get_dev_info
[00000612] [04:54:21:343581] [Tid0x00005710] [debug] storage: 1, section: 2, address: 0x0, length: 0x400000, level:0, addr_type: 0 #(format_strategy.cpp, line:37)
[00000613] [04:54:21:343581] [Tid0x00005710] [info] -->[C384] device_instance::get_dev_info #(device_instance.cpp, line:1239)
[00000614] [04:54:21:343581] [Tid0x00005710] [info] <--[C384] device_instance::get_dev_info
[00000615] [04:54:21:343581] [Tid0x00005710] [info] send format status.  #(format_strategy.cpp, line:94)
[00000616] [04:54:21:343581] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000617] [04:54:21:343581] [Tid0x00005710] [info] /control-code/: CC_START_DL_INFO=0x80001 #(device_instance.cpp, line:350)
[00000618] [04:54:21:343581] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000619] [04:54:21:344579] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000620] [04:54:21:344579] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000621] [04:54:21:344579] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000622] [04:54:21:344579] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000623] [04:54:21:344579] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[01 00 08 00 ]
[00000624] [04:54:21:344579] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000625] [04:54:21:344579] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000626] [04:54:21:344579] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000627] [04:54:21:344579] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000628] [04:54:21:345579] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000629] [04:54:21:345579] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000630] [04:54:21:345579] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000631] [04:54:21:345579] [Tid0x00005710] [debug] -->[C396] device_instance::format #(device_instance.cpp, line:1119)
[00000632] [04:54:21:345579] [Tid0x00005710] [info] /CMD/: CMD_FORMAT=0x10003 #(device_instance.cpp, line:1128)
[00000633] [04:54:21:345579] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000634] [04:54:21:345579] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[03 00 01 00 ]
[00000635] [04:54:21:345579] [Tid0x00005710] [info] receive response for command check. #(device_instance.cpp, line:1132)
[00000636] [04:54:21:346579] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000637] [04:54:21:346579] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000638] [04:54:21:346579] [Tid0x00005710] [info] send: parameters. #(device_instance.cpp, line:1142)
[00000639] [04:54:21:346579] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 38 00 00 00 ]
[00000640] [04:54:21:346579] [Tid0x00005710] [debug] 			Tx->: 0x00000038 Hex[01 00 00 00 02 00 00 00 00 00 00 00 00 00 00 00 ]
[00000641] [04:54:21:346579] [Tid0x00005710] [info] parameter check status. #(device_instance.cpp, line:1145)
[00000642] [04:54:21:350080] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000643] [04:54:21:350080] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000644] [04:54:21:360588] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000645] [04:54:21:360588] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[04 00 04 40 ]
[00000646] [04:54:21:360588] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000647] [04:54:21:360588] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[64 00 00 00 ]
[00000648] [04:54:21:360588] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000649] [04:54:21:360588] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000650] [04:54:21:363596] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000651] [04:54:21:363596] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[05 00 04 40 ]
[00000652] [04:54:21:363596] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:1189)
[00000653] [04:54:21:363596] [Tid0x00005710] [debug] <--[C396] device_instance::format
[00000654] [04:54:21:363596] [Tid0x00005710] [info] <--[C373] logic::format
[00000655] [04:54:21:363596] [Tid0x00005710] [info] <--[C372] flashtool_format
[00000656] [04:54:21:364595] [Tid0x00005710] [info] -->[C413] flashtool_format #(flashtoolex_api.cpp, line:285)
[00000657] [04:54:21:364595] [Tid0x00005710] [info] -->[C414] logic::format #(logic.cpp, line:850)
[00000658] [04:54:21:364595] [Tid0x00005710] [info] format storage. #(logic.cpp, line:859)
[00000659] [04:54:21:364595] [Tid0x00005710] [info] Format:send host info. 20250416&045421. [Machine]:EngyPC [User]: [OS]:Windows 8 #(logic.cpp, line:878)
[00000660] [04:54:21:364595] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000661] [04:54:21:364595] [Tid0x00005710] [info] /control-code/: CC_SET_HOST_INFO=0x20005 #(device_instance.cpp, line:350)
[00000662] [04:54:21:364595] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000663] [04:54:21:364595] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000664] [04:54:21:364595] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000665] [04:54:21:364595] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000666] [04:54:21:364595] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000667] [04:54:21:365589] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[05 00 02 00 ]
[00000668] [04:54:21:365589] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000669] [04:54:21:365589] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[04 00 01 c0 ]
[00000670] [04:54:21:365589] [Tid0x00005710] [warning] device control code not support. #(device_instance.cpp, line:371)
[00000671] [04:54:21:365589] [Tid0x00005710] [info] type: manual erase. #(logic.cpp, line:888)
[00000672] [04:54:21:365589] [Tid0x00005710] [info] -->[C424] device_instance::get_dev_info #(device_instance.cpp, line:1239)
[00000673] [04:54:21:365589] [Tid0x00005710] [info] <--[C424] device_instance::get_dev_info
[00000674] [04:54:21:365589] [Tid0x00005710] [debug] storage: 1, section: 8, address: 0x0, length: 0x1ccf000000, level:0, addr_type: 0 #(format_strategy.cpp, line:37)
[00000675] [04:54:21:365589] [Tid0x00005710] [info] -->[C425] device_instance::get_dev_info #(device_instance.cpp, line:1239)
[00000676] [04:54:21:365589] [Tid0x00005710] [info] <--[C425] device_instance::get_dev_info
[00000677] [04:54:21:365589] [Tid0x00005710] [info] send format status.  #(format_strategy.cpp, line:94)
[00000678] [04:54:21:365589] [Tid0x00005710] [info] /CMD/: CMD_DEVICE_CTRL=0x10009 #(device_instance.cpp, line:349)
[00000679] [04:54:21:365589] [Tid0x00005710] [info] /control-code/: CC_START_DL_INFO=0x80001 #(device_instance.cpp, line:350)
[00000680] [04:54:21:365589] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000681] [04:54:21:365589] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[09 00 01 00 ]
[00000682] [04:54:21:365589] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000683] [04:54:21:366588] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000684] [04:54:21:366588] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000685] [04:54:21:366588] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[01 00 08 00 ]
[00000686] [04:54:21:366588] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000687] [04:54:21:366588] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000688] [04:54:21:366588] [Tid0x00005710] [info] device support this control code. #(device_instance.cpp, line:375)
[00000689] [04:54:21:366588] [Tid0x00005710] [info] receive response. #(device_instance.cpp, line:389)
[00000690] [04:54:21:367588] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000691] [04:54:21:367588] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000692] [04:54:21:367588] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:400)
[00000693] [04:54:21:367588] [Tid0x00005710] [debug] -->[C437] device_instance::format #(device_instance.cpp, line:1119)
[00000694] [04:54:21:367588] [Tid0x00005710] [info] /CMD/: CMD_FORMAT=0x10003 #(device_instance.cpp, line:1128)
[00000695] [04:54:21:367588] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000696] [04:54:21:367588] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[03 00 01 00 ]
[00000697] [04:54:21:367588] [Tid0x00005710] [info] receive response for command check. #(device_instance.cpp, line:1132)
[00000698] [04:54:21:367588] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000699] [04:54:21:367588] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000700] [04:54:21:367588] [Tid0x00005710] [info] send: parameters. #(device_instance.cpp, line:1142)
[00000701] [04:54:21:367588] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 38 00 00 00 ]
[00000702] [04:54:21:368588] [Tid0x00005710] [debug] 			Tx->: 0x00000038 Hex[01 00 00 00 08 00 00 00 00 00 00 00 00 00 00 00 ]
[00000703] [04:54:21:368588] [Tid0x00005710] [info] parameter check status. #(device_instance.cpp, line:1145)
[00000704] [04:54:21:371093] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000705] [04:54:21:372092] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000706] [04:54:24:171772] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000707] [04:54:24:171772] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[04 00 04 40 ]
[00000708] [04:54:24:171772] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000709] [04:54:24:171772] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[64 00 00 00 ]
[00000710] [04:54:24:171772] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000711] [04:54:24:172779] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[00 00 00 00 ]
[00000712] [04:54:24:175213] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000713] [04:54:24:175213] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[05 00 04 40 ]
[00000714] [04:54:24:175213] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:1189)
[00000715] [04:54:24:175213] [Tid0x00005710] [debug] <--[C437] device_instance::format
[00000716] [04:54:24:175213] [Tid0x00005710] [info] <--[C414] logic::format
[00000717] [04:54:24:175213] [Tid0x00005710] [info] <--[C413] flashtool_format
[00000718] [04:54:24:176247] [Tid0x00005710] [info] -->[C454] flashtool_shutdown_device #(flashtoolex_api.cpp, line:149)
[00000719] [04:54:24:176247] [Tid0x00005710] [debug] -->[C455] connection::shutdown_device #(connection.cpp, line:989)
[00000720] [04:54:24:176247] [Tid0x00005710] [info] reboot option: is_dev_reboot[0] 
                           timeout_ms[0] 
                           async[0] 
                           bootup[0] 
                           dlbit[0] 
                           bNotResetRTCTime[0] #(connection.cpp, line:1021)
[00000721] [04:54:24:176247] [Tid0x00005710] [debug] -->[C456] device_instance::shutdown_device #(device_instance.cpp, line:509)
[00000722] [04:54:24:176754] [Tid0x00005710] [info] /CMD/: CMD_SHUTDOWN=0x10007 #(device_instance.cpp, line:514)
[00000723] [04:54:24:176754] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000724] [04:54:24:176754] [Tid0x00005710] [debug] 			Tx->: 0x00000004 Hex[07 00 01 00 ]
[00000725] [04:54:24:176754] [Tid0x00005710] [info] receive response for command check. #(device_instance.cpp, line:518)
[00000726] [04:54:24:177263] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000727] [04:54:24:177263] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000728] [04:54:24:177263] [Tid0x00005710] [info] reboot option: is_dev_reboot[0] 
                              timeout_ms[0] 
                              async[0] 
                              bootup[0] 
                              dlbit[0] 
                              bNotResetRTCTime[0] #(device_instance.cpp, line:539)
[00000729] [04:54:24:177263] [Tid0x00005710] [debug] 			Tx->: 0x0000000c Hex[ef ee ee fe 01 00 00 00 18 00 00 00 ]
[00000730] [04:54:24:177263] [Tid0x00005710] [debug] 			Tx->: 0x00000018 Hex[00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ]
[00000731] [04:54:24:180519] [Tid0x00007d2c] [debug] 			<-Rx: 0x0000000c Hex[ef ee ee fe 01 00 00 00 04 00 00 00 ]
[00000732] [04:54:24:180519] [Tid0x00007d2c] [debug] 			<-Rx: 0x00000004 Hex[00 00 00 00 ]
[00000733] [04:54:24:180519] [Tid0x00005710] [info] [result]: STATUS_OK #(device_instance.cpp, line:552)
[00000734] [04:54:24:180519] [Tid0x00005710] [debug] <--[C456] device_instance::shutdown_device
[00000735] [04:54:24:180519] [Tid0x00005710] [debug] <--[C455] connection::shutdown_device
[00000736] [04:54:24:180519] [Tid0x00005710] [info] <--[C454] flashtool_shutdown_device
[00000737] [04:54:24:180519] [Tid0x00005710] [info] -->[C465] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000738] [04:54:24:180519] [Tid0x00005710] [debug] -->[C467] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000739] [04:54:24:180519] [Tid0x00005710] [info] -->[C468] device_log_source::stop #(device_log_source.cpp, line:29)
[00000740] [04:54:24:181519] [Tid0x0000e18c] [info] device_log_source worker thread interrupted. exit. #(device_log_source.cpp, line:58)
[00000741] [04:54:24:181519] [Tid0x00005710] [info] <--[C468] device_log_source::stop
[00000742] [04:54:24:181519] [Tid0x00005710] [info] -->[C469] data_mux::stop #(data_mux.cpp, line:92)
[00000743] [04:54:24:181519] [Tid0x00005710] [debug] -->[C470] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000744] [04:54:24:181519] [Tid0x00005710] [debug] <--[C470] comm_engine::cancel
[00000745] [04:54:24:181519] [Tid0x00007d2c] [warning] Rx abort. #(comm_engine.cpp, line:204)
[00000746] [04:54:24:182519] [Tid0x00007d2c] [info] mux worker thread interrupted. exit. #(data_mux.cpp, line:195)
[00000747] [04:54:24:182519] [Tid0x00007d2c] [info] <--[C137] data_mux::worker
[00000748] [04:54:24:182519] [Tid0x000002b0] [info] mux monitor thread interrupted. exit. #(data_mux.cpp, line:140)
[00000749] [04:54:24:182519] [Tid0x000002b0] [info] <--[C140] data_mux::monitor
[00000750] [04:54:24:182519] [Tid0x00005710] [info] <--[C469] data_mux::stop
[00000751] [04:54:24:182519] [Tid0x00005710] [debug] <--[C467] device_instance::~device_instance
[00000752] [04:54:24:182519] [Tid0x00005710] [info] -->[C471] comm_engine::close #(comm_engine.cpp, line:382)
[00000753] [04:54:24:182519] [Tid0x00005710] [debug] -->[C472] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000754] [04:54:24:182519] [Tid0x00005710] [debug] <--[C472] comm_engine::cancel
[00000755] [04:54:24:194389] [Tid0x00005710] [info] <--[C471] comm_engine::close
[00000756] [04:54:24:194389] [Tid0x00005710] [info] delete hsession 0x11a1b288 #(kernel.cpp, line:102)
[00000757] [04:54:24:194389] [Tid0x00005710] [info] <--[C465] flashtool_destroy_session
[00000758] [04:56:43:799512] [Tid0x0000f394] [info] -->[C473] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000759] [04:56:43:799512] [Tid0x0000f394] [debug] -->[C474] connection::create_session #(connection.cpp, line:43)
[00000760] [04:56:43:799512] [Tid0x0000f394] [debug] -->[C475] kernel::create_new_session #(kernel.cpp, line:76)
[00000761] [04:56:43:799512] [Tid0x0000f394] [info] create new hsession 0xb789750 #(kernel.cpp, line:92)
[00000762] [04:56:43:799512] [Tid0x0000f394] [debug] <--[C475] kernel::create_new_session
[00000763] [04:56:43:799512] [Tid0x0000f394] [debug] -->[C476] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000764] [04:56:43:799512] [Tid0x0000f394] [debug] <--[C476] boot_rom::boot_rom
[00000765] [04:56:43:799512] [Tid0x0000f394] [debug] -->[C477] device_instance::device_instance #(device_instance.cpp, line:22)
[00000766] [04:56:43:799512] [Tid0x0000f394] [debug] -->[C478] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000767] [04:56:43:799512] [Tid0x0000f394] [debug] <--[C478] device_log_source::device_log_source
[00000768] [04:56:43:799512] [Tid0x0000f394] [debug] -->[C479] data_mux::data_mux #(data_mux.cpp, line:10)
[00000769] [04:56:43:799512] [Tid0x0000f394] [debug] <--[C479] data_mux::data_mux
[00000770] [04:56:43:799512] [Tid0x0000f394] [debug] <--[C477] device_instance::device_instance
[00000771] [04:56:43:799512] [Tid0x0000f394] [debug] <--[C474] connection::create_session
[00000772] [04:56:43:799512] [Tid0x0000f394] [info] <--[C473] flashtool_create_session_with_handle
[00000773] [04:56:43:799512] [Tid0x0000f394] [info] -->[C480] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000774] [04:56:43:799512] [Tid0x0000f394] [debug] -->[C481] connection::connect_brom #(connection.cpp, line:94)
[00000775] [04:56:43:800511] [Tid0x0000f394] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000776] [04:56:43:800511] [Tid0x0000f394] [debug] -->[C482] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000777] [04:56:43:800511] [Tid0x0000f394] [debug] -->[C483] is_valid_ip #(engine_factory.cpp, line:13)
[00000778] [04:56:43:800511] [Tid0x0000f394] [debug] <--[C483] is_valid_ip
[00000779] [04:56:43:800511] [Tid0x0000f394] [debug] -->[C484] is_lge_impl #(engine_factory.cpp, line:32)
[00000780] [04:56:43:800511] [Tid0x0000f394] [debug] <--[C484] is_lge_impl
[00000781] [04:56:43:800511] [Tid0x0000f394] [debug] -->[C485] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000782] [04:56:43:800511] [Tid0x0000f394] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000783] [04:56:43:800511] [Tid0x0000f394] [debug] <--[C485] lib_config_parser::get_value
[00000784] [04:56:43:800511] [Tid0x0000f394] [debug] <--[C482] engine_factory::create_transmission_engine
[00000785] [04:56:43:800511] [Tid0x0000f394] [info] -->[C486] comm_engine::open #(comm_engine.cpp, line:63)
[00000786] [04:56:43:800511] [Tid0x0000f394] [info] try to open device: COM20 baud rate 115200 #(comm_engine.cpp, line:71)
[00000787] [04:56:43:800511] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000788] [04:56:43:800511] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000789] [04:56:43:850899] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000790] [04:56:43:850899] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000791] [04:56:43:911864] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000792] [04:56:43:911864] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000793] [04:56:43:974325] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000794] [04:56:43:974325] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000795] [04:56:44:036863] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000796] [04:56:44:036863] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000797] [04:56:44:099656] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000798] [04:56:44:099656] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000799] [04:56:44:161501] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000800] [04:56:44:161501] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000801] [04:56:44:223384] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000802] [04:56:44:223384] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000803] [04:56:44:285179] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000804] [04:56:44:285179] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000805] [04:56:44:346866] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000806] [04:56:44:346866] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000807] [04:56:44:407783] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000808] [04:56:44:407783] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000809] [04:56:44:469658] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000810] [04:56:44:469658] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000811] [04:56:44:529850] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000812] [04:56:44:529850] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000813] [04:56:44:591834] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000814] [04:56:44:591834] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000815] [04:56:44:654523] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000816] [04:56:44:654523] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000817] [04:56:44:716573] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000818] [04:56:44:716573] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000819] [04:56:44:778700] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000820] [04:56:44:778700] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000821] [04:56:44:841284] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000822] [04:56:44:841284] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000823] [04:56:44:903293] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000824] [04:56:44:903293] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000825] [04:56:44:966365] [Tid0x0000f394] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000826] [04:56:44:966365] [Tid0x0000f394] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000827] [04:56:45:029791] [Tid0x0000f394] [info] <--[C486] comm_engine::open
[00000828] [04:56:45:029791] [Tid0x0000f394] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000829] [04:56:45:029791] [Tid0x0000f394] [debug] <--[C481] connection::connect_brom
[00000830] [04:56:45:029791] [Tid0x0000f394] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000831] [04:56:45:029791] [Tid0x0000f394] [info] <--[C480] flashtool_connect_brom
[00000832] [04:56:45:029791] [Tid0x0000f394] [info] -->[C487] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000833] [04:56:45:029791] [Tid0x0000f394] [debug] -->[C489] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000834] [04:56:45:029791] [Tid0x0000f394] [info] -->[C490] device_log_source::stop #(device_log_source.cpp, line:29)
[00000835] [04:56:45:029791] [Tid0x0000f394] [info] <--[C490] device_log_source::stop
[00000836] [04:56:45:029791] [Tid0x0000f394] [info] -->[C491] data_mux::stop #(data_mux.cpp, line:92)
[00000837] [04:56:45:029791] [Tid0x0000f394] [info] <--[C491] data_mux::stop
[00000838] [04:56:45:029791] [Tid0x0000f394] [debug] <--[C489] device_instance::~device_instance
[00000839] [04:56:45:029791] [Tid0x0000f394] [info] -->[C492] comm_engine::close #(comm_engine.cpp, line:382)
[00000840] [04:56:45:029791] [Tid0x0000f394] [debug] -->[C493] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000841] [04:56:45:029791] [Tid0x0000f394] [debug] <--[C493] comm_engine::cancel
[00000842] [04:56:45:029791] [Tid0x0000f394] [info] <--[C492] comm_engine::close
[00000843] [04:56:45:029791] [Tid0x0000f394] [info] delete hsession 0xb789750 #(kernel.cpp, line:102)
[00000844] [04:56:45:029791] [Tid0x0000f394] [info] <--[C487] flashtool_destroy_session
[00000845] [04:56:45:030791] [Tid0x0000f394] [info] -->[C494] flashtool_shutdown_device #(flashtoolex_api.cpp, line:149)
[00000846] [04:56:45:030791] [Tid0x0000f394] [debug] -->[C495] connection::shutdown_device #(connection.cpp, line:989)
[00000847] [04:56:45:030791] [Tid0x0000f394] [error] invalid session. #(connection.cpp, line:994)
[00000848] [04:56:45:030791] [Tid0x0000f394] [debug] <--[C495] connection::shutdown_device
[00000849] [04:56:45:030791] [Tid0x0000f394] [error] <ERR_CHECKPOINT>[811][error][0xc001000a]</ERR_CHECKPOINT>flashtool_shutdown_device fail #(flashtoolex_api.cpp, line:154)
[00000850] [04:56:45:030791] [Tid0x0000f394] [info] <--[C494] flashtool_shutdown_device
[00000851] [04:56:45:030791] [Tid0x0000f394] [info] -->[C496] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000852] [04:56:45:030791] [Tid0x0000f394] [info] <--[C496] flashtool_destroy_session

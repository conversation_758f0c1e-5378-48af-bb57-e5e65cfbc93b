[00000001] [04:52:28:937844] [Tid0x0000b674] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [04:52:28:937844] [Tid0x0000b674] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [04:52:28:937844] [Tid0x0000b674] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [04:52:28:937844] [Tid0x0000b674] [info] create new hsession 0x1177b288 #(kernel.cpp, line:92)
[00000005] [04:52:28:937844] [Tid0x0000b674] [debug] <--[C3] kernel::create_new_session
[00000006] [04:52:28:937844] [Tid0x0000b674] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [04:52:28:937844] [Tid0x0000b674] [debug] <--[C4] boot_rom::boot_rom
[00000008] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C6] device_log_source::device_log_source
[00000011] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C7] data_mux::data_mux
[00000013] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C5] device_instance::device_instance
[00000014] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C2] connection::create_session
[00000015] [04:52:28:945030] [Tid0x0000b674] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [04:52:28:945030] [Tid0x0000b674] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [04:52:28:945030] [Tid0x0000b674] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C11] is_valid_ip
[00000022] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C12] is_lge_impl
[00000024] [04:52:28:945030] [Tid0x0000b674] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [04:52:28:945030] [Tid0x0000b674] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C13] lib_config_parser::get_value
[00000027] [04:52:28:945030] [Tid0x0000b674] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [04:52:28:945030] [Tid0x0000b674] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [04:52:28:945030] [Tid0x0000b674] [info] try to open device: COM20 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [04:52:28:946034] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000031] [04:52:28:946034] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000032] [04:52:29:006295] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000033] [04:52:29:006295] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000034] [04:52:29:067372] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000035] [04:52:29:067372] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000036] [04:52:29:128630] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000037] [04:52:29:128630] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000038] [04:52:29:191707] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000039] [04:52:29:191707] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000040] [04:52:29:253182] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000041] [04:52:29:253182] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000042] [04:52:29:314287] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000043] [04:52:29:314287] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000044] [04:52:29:376107] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000045] [04:52:29:376107] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000046] [04:52:29:437391] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000047] [04:52:29:437391] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000048] [04:52:29:500189] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000049] [04:52:29:500189] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000050] [04:52:29:562225] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000051] [04:52:29:562732] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000052] [04:52:29:623814] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000053] [04:52:29:623814] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000054] [04:52:29:685869] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000055] [04:52:29:685869] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000056] [04:52:29:748232] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000057] [04:52:29:748232] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000058] [04:52:29:809399] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000059] [04:52:29:809399] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000060] [04:52:29:871984] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000061] [04:52:29:871984] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000062] [04:52:29:934260] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000063] [04:52:29:934260] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000064] [04:52:29:996109] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000065] [04:52:29:996109] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000066] [04:52:30:058461] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000067] [04:52:30:058461] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000068] [04:52:30:120075] [Tid0x0000b674] [debug] CreateFile error code[0xaa] #(comm_engine.cpp, line:94)
[00000069] [04:52:30:120075] [Tid0x0000b674] [info] retry CreateFile. #(comm_engine.cpp, line:95)
[00000070] [04:52:30:182427] [Tid0x0000b674] [info] <--[C14] comm_engine::open
[00000071] [04:52:30:182427] [Tid0x0000b674] [error] ./arch/win/comm_engine.cpp(103): Throw in function void __thiscall comm_engine::open(const class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > &)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: Create COM File failed.
 #(connection.cpp, line:137)
[00000072] [04:52:30:182427] [Tid0x0000b674] [debug] <--[C9] connection::connect_brom
[00000073] [04:52:30:182427] [Tid0x0000b674] [error] <ERR_CHECKPOINT>[809][error][0xc0010001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000074] [04:52:30:182427] [Tid0x0000b674] [info] <--[C8] flashtool_connect_brom
[00000075] [04:52:30:182427] [Tid0x0000b674] [info] -->[C15] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000076] [04:52:30:182427] [Tid0x0000b674] [debug] -->[C17] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000077] [04:52:30:182427] [Tid0x0000b674] [info] -->[C18] device_log_source::stop #(device_log_source.cpp, line:29)
[00000078] [04:52:30:182427] [Tid0x0000b674] [info] <--[C18] device_log_source::stop
[00000079] [04:52:30:182427] [Tid0x0000b674] [info] -->[C19] data_mux::stop #(data_mux.cpp, line:92)
[00000080] [04:52:30:182427] [Tid0x0000b674] [info] <--[C19] data_mux::stop
[00000081] [04:52:30:182427] [Tid0x0000b674] [debug] <--[C17] device_instance::~device_instance
[00000082] [04:52:30:182427] [Tid0x0000b674] [info] -->[C20] comm_engine::close #(comm_engine.cpp, line:382)
[00000083] [04:52:30:183419] [Tid0x0000b674] [debug] -->[C21] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000084] [04:52:30:183419] [Tid0x0000b674] [debug] <--[C21] comm_engine::cancel
[00000085] [04:52:30:183419] [Tid0x0000b674] [info] <--[C20] comm_engine::close
[00000086] [04:52:30:183419] [Tid0x0000b674] [info] delete hsession 0x1177b288 #(kernel.cpp, line:102)
[00000087] [04:52:30:183419] [Tid0x0000b674] [info] <--[C15] flashtool_destroy_session
[00000088] [04:52:30:183419] [Tid0x0000b674] [info] -->[C22] flashtool_shutdown_device #(flashtoolex_api.cpp, line:149)
[00000089] [04:52:30:183419] [Tid0x0000b674] [debug] -->[C23] connection::shutdown_device #(connection.cpp, line:989)
[00000090] [04:52:30:183419] [Tid0x0000b674] [error] invalid session. #(connection.cpp, line:994)
[00000091] [04:52:30:183419] [Tid0x0000b674] [debug] <--[C23] connection::shutdown_device
[00000092] [04:52:30:183419] [Tid0x0000b674] [error] <ERR_CHECKPOINT>[811][error][0xc001000a]</ERR_CHECKPOINT>flashtool_shutdown_device fail #(flashtoolex_api.cpp, line:154)
[00000093] [04:52:30:183419] [Tid0x0000b674] [info] <--[C22] flashtool_shutdown_device
[00000094] [04:52:30:183419] [Tid0x0000b674] [info] -->[C24] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000095] [04:52:30:183419] [Tid0x0000b674] [info] <--[C24] flashtool_destroy_session

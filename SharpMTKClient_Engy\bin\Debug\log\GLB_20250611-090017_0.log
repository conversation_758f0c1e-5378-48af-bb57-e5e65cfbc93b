[00000001] [09:00:17:632760] [Tid0x0000af14] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [09:00:17:633759] [Tid0x0000af14] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [09:00:17:633759] [Tid0x0000af14] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [09:00:17:633759] [Tid0x0000af14] [info] create new hsession 0xbbcfa70 #(kernel.cpp, line:92)
[00000005] [09:00:17:633759] [Tid0x0000af14] [debug] <--[C3] kernel::create_new_session
[00000006] [09:00:17:633759] [Tid0x0000af14] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [09:00:17:633759] [Tid0x0000af14] [debug] <--[C4] boot_rom::boot_rom
[00000008] [09:00:17:640505] [Tid0x0000af14] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [09:00:17:640505] [Tid0x0000af14] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [09:00:17:640505] [Tid0x0000af14] [debug] <--[C6] device_log_source::device_log_source
[00000011] [09:00:17:640505] [Tid0x0000af14] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [09:00:17:640505] [Tid0x0000af14] [debug] <--[C7] data_mux::data_mux
[00000013] [09:00:17:640505] [Tid0x0000af14] [debug] <--[C5] device_instance::device_instance
[00000014] [09:00:17:640505] [Tid0x0000af14] [debug] <--[C2] connection::create_session
[00000015] [09:00:17:640505] [Tid0x0000af14] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [09:00:17:640505] [Tid0x0000af14] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [09:00:17:640505] [Tid0x0000af14] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [09:00:17:640505] [Tid0x0000af14] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [09:00:17:640505] [Tid0x0000af14] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [09:00:17:641008] [Tid0x0000af14] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [09:00:17:641008] [Tid0x0000af14] [debug] <--[C11] is_valid_ip
[00000022] [09:00:17:641008] [Tid0x0000af14] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [09:00:17:641008] [Tid0x0000af14] [debug] <--[C12] is_lge_impl
[00000024] [09:00:17:641008] [Tid0x0000af14] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [09:00:17:641008] [Tid0x0000af14] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [09:00:17:641008] [Tid0x0000af14] [debug] <--[C13] lib_config_parser::get_value
[00000027] [09:00:17:641008] [Tid0x0000af14] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [09:00:17:641008] [Tid0x0000af14] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [09:00:17:641008] [Tid0x0000af14] [info] try to open device: COM5 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [09:00:17:970783] [Tid0x0000af14] [info] COM5 open complete. #(comm_engine.cpp, line:168)
[00000031] [09:00:17:970783] [Tid0x0000af14] [info] <--[C14] comm_engine::open
[00000032] [09:00:17:970783] [Tid0x0000af14] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000033] [09:00:17:970783] [Tid0x0000af14] [debug] <--[C15] boot_rom::set_transfer_channel
[00000034] [09:00:17:970783] [Tid0x0000af14] [debug] -->[C16] boot_rom::connect #(boot_rom.cpp, line:47)
[00000035] [09:00:17:970783] [Tid0x0000af14] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000036] [09:00:17:977163] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000037] [09:00:17:990684] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[52 ]
[00000038] [09:00:17:990684] [Tid0x0000af14] [debug] 			<-Rx: 0x00000004 Hex[45 41 44 59 ]
[00000039] [09:00:17:990684] [Tid0x0000af14] [info] preloader exist. connect. #(boot_rom.cpp, line:88)
[00000040] [09:00:17:990684] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000041] [09:00:17:990684] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[5f ]
[00000042] [09:00:17:990684] [Tid0x0000af14] [debug] send 0xA0. receive 0x5F #(boot_rom.cpp, line:98)
[00000043] [09:00:17:990684] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[0a ]
[00000044] [09:00:17:990684] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[f5 ]
[00000045] [09:00:17:990684] [Tid0x0000af14] [debug] send 0x0A. receive 0xF5 #(boot_rom.cpp, line:98)
[00000046] [09:00:17:990684] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[50 ]
[00000047] [09:00:17:990684] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[af ]
[00000048] [09:00:17:991684] [Tid0x0000af14] [debug] send 0x50. receive 0xAF #(boot_rom.cpp, line:98)
[00000049] [09:00:17:991684] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[05 ]
[00000050] [09:00:17:991684] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[fa ]
[00000051] [09:00:17:991684] [Tid0x0000af14] [debug] send 0x05. receive 0xFA #(boot_rom.cpp, line:98)
[00000052] [09:00:17:991684] [Tid0x0000af14] [debug] <--[C16] boot_rom::connect
[00000053] [09:00:17:991684] [Tid0x0000af14] [info] (2/2)security verify tool and DA. #(connection.cpp, line:132)
[00000054] [09:00:17:991684] [Tid0x0000af14] [debug] -->[C28] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000055] [09:00:17:991684] [Tid0x0000af14] [debug] -->[C29] boot_rom::get_preloader_version #(boot_rom.cpp, line:899)
[00000056] [09:00:17:991684] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000057] [09:00:17:991684] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000058] [09:00:17:991684] [Tid0x0000af14] [info] preloader version: 0x3 #(boot_rom.cpp, line:916)
[00000059] [09:00:17:991684] [Tid0x0000af14] [debug] <--[C29] boot_rom::get_preloader_version
[00000060] [09:00:17:991684] [Tid0x0000af14] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000061] [09:00:17:991684] [Tid0x0000af14] [debug] <--[C28] boot_rom_logic::security_verify_connection
[00000062] [09:00:17:991684] [Tid0x0000af14] [debug] <--[C9] connection::connect_brom
[00000063] [09:00:17:991684] [Tid0x0000af14] [info] <--[C8] flashtool_connect_brom
[00000064] [09:00:17:992682] [Tid0x0000af14] [info] -->[C32] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000065] [09:00:17:992682] [Tid0x0000af14] [debug] -->[C33] connection::device_control #(connection.cpp, line:669)
[00000066] [09:00:17:992682] [Tid0x0000af14] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000067] [09:00:17:992682] [Tid0x0000af14] [debug] -->[C34] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000068] [09:00:17:992682] [Tid0x0000af14] [debug] -->[C35] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000069] [09:00:17:992682] [Tid0x0000af14] [info] get chip id  #(boot_rom.cpp, line:114)
[00000070] [09:00:17:992682] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000071] [09:00:17:992682] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000072] [09:00:17:992682] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000073] [09:00:17:993681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000074] [09:00:17:993681] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000075] [09:00:17:993681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000076] [09:00:17:993681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000077] [09:00:17:993681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000078] [09:00:17:993681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000079] [09:00:17:993681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000080] [09:00:17:993681] [Tid0x0000af14] [debug] -->[C46] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000081] [09:00:17:993681] [Tid0x0000af14] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000082] [09:00:17:993681] [Tid0x0000af14] [debug] <--[C46] lib_config_parser::get_value
[00000083] [09:00:17:993681] [Tid0x0000af14] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000084] [09:00:17:993681] [Tid0x0000af14] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000085] [09:00:17:994681] [Tid0x0000af14] [debug] <--[C35] boot_rom::get_chip_id
[00000086] [09:00:17:994681] [Tid0x0000af14] [debug] <--[C34] boot_rom::device_control
[00000087] [09:00:17:994681] [Tid0x0000af14] [debug] <--[C33] connection::device_control
[00000088] [09:00:17:994681] [Tid0x0000af14] [info] <--[C32] flashtool_device_control
[00000089] [09:00:17:994681] [Tid0x0000af14] [info] -->[C47] flashtool_device_control #(flashtoolex_api.cpp, line:304)
[00000090] [09:00:17:994681] [Tid0x0000af14] [debug] -->[C48] connection::device_control #(connection.cpp, line:669)
[00000091] [09:00:17:994681] [Tid0x0000af14] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:676)
[00000092] [09:00:17:994681] [Tid0x0000af14] [debug] -->[C49] boot_rom::device_control #(boot_rom.cpp, line:751)
[00000093] [09:00:17:994681] [Tid0x0000af14] [debug] -->[C50] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000094] [09:00:17:994681] [Tid0x0000af14] [info] get chip id  #(boot_rom.cpp, line:114)
[00000095] [09:00:17:994681] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000096] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000097] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000098] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000099] [09:00:17:994681] [Tid0x0000af14] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000100] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000101] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000102] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000103] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000104] [09:00:17:994681] [Tid0x0000af14] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000105] [09:00:17:994681] [Tid0x0000af14] [debug] -->[C61] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000106] [09:00:17:994681] [Tid0x0000af14] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000107] [09:00:17:994681] [Tid0x0000af14] [debug] <--[C61] lib_config_parser::get_value
[00000108] [09:00:17:994681] [Tid0x0000af14] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:209)
[00000109] [09:00:17:994681] [Tid0x0000af14] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000110] [09:00:17:995683] [Tid0x0000af14] [debug] <--[C50] boot_rom::get_chip_id
[00000111] [09:00:17:995683] [Tid0x0000af14] [debug] <--[C49] boot_rom::device_control
[00000112] [09:00:17:995683] [Tid0x0000af14] [debug] <--[C48] connection::device_control
[00000113] [09:00:17:995683] [Tid0x0000af14] [info] <--[C47] flashtool_device_control
[00000114] [09:00:17:995683] [Tid0x0000af14] [error] Not Support platform: , type:  #(chip_mapping.cpp, line:229)
[00000115] [09:00:17:996681] [Tid0x0000af14] [info] -->[C62] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000116] [09:00:17:996681] [Tid0x0000af14] [debug] -->[C64] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000117] [09:00:17:996681] [Tid0x0000af14] [info] -->[C65] device_log_source::stop #(device_log_source.cpp, line:29)
[00000118] [09:00:17:996681] [Tid0x0000af14] [info] <--[C65] device_log_source::stop
[00000119] [09:00:17:996681] [Tid0x0000af14] [info] -->[C66] data_mux::stop #(data_mux.cpp, line:92)
[00000120] [09:00:17:996681] [Tid0x0000af14] [info] <--[C66] data_mux::stop
[00000121] [09:00:17:996681] [Tid0x0000af14] [debug] <--[C64] device_instance::~device_instance
[00000122] [09:00:17:996681] [Tid0x0000af14] [info] -->[C67] comm_engine::close #(comm_engine.cpp, line:382)
[00000123] [09:00:17:996681] [Tid0x0000af14] [debug] -->[C68] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000124] [09:00:17:996681] [Tid0x0000af14] [debug] <--[C68] comm_engine::cancel
[00000125] [09:00:18:005190] [Tid0x0000af14] [info] <--[C67] comm_engine::close
[00000126] [09:00:18:005190] [Tid0x0000af14] [info] delete hsession 0xbbcfa70 #(kernel.cpp, line:102)
[00000127] [09:00:18:005190] [Tid0x0000af14] [info] <--[C62] flashtool_destroy_session
[00000128] [09:00:18:006191] [Tid0x0000af14] [info] -->[C69] flashtool_shutdown_device #(flashtoolex_api.cpp, line:149)
[00000129] [09:00:18:007190] [Tid0x0000af14] [debug] -->[C70] connection::shutdown_device #(connection.cpp, line:989)
[00000130] [09:00:18:007190] [Tid0x0000af14] [error] invalid session. #(connection.cpp, line:994)
[00000131] [09:00:18:007190] [Tid0x0000af14] [debug] <--[C70] connection::shutdown_device
[00000132] [09:00:18:007190] [Tid0x0000af14] [error] <ERR_CHECKPOINT>[811][error][0xc001000a]</ERR_CHECKPOINT>flashtool_shutdown_device fail #(flashtoolex_api.cpp, line:154)
[00000133] [09:00:18:007190] [Tid0x0000af14] [info] <--[C69] flashtool_shutdown_device
[00000134] [09:00:18:007190] [Tid0x0000af14] [info] -->[C71] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000135] [09:00:18:007190] [Tid0x0000af14] [info] <--[C71] flashtool_destroy_session

[0000001] [04:34:42:433995] [Tid0x00002070] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [04:34:42:433995] [Tid0x00002070] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [04:34:45:906708] [Tid0x0000a220] [debug] -->[C2] DL_LoadScatter #(api.cpp, line:2554)
[0000004] [04:34:45:923873] [Tid0x0000a220] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000005] [04:34:45:923873] [Tid0x0000a220] [debug] <--[C2] DL_LoadScatter
[0000006] [04:34:45:923873] [Tid0x0000a220] [debug] -->[C3] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000007] [04:34:45:933244] [Tid0x0000a220] [debug] <--[C3] DL_AutoLoadRomImages
[0000008] [04:34:45:934236] [Tid0x0000a220] [debug] -->[C4] DL_GetCount #(api.cpp, line:2696)
[0000009] [04:34:45:940871] [Tid0x0000a220] [debug] <--[C4] DL_GetCount
[0000010] [04:34:45:940871] [Tid0x0000a220] [debug] -->[C5] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000011] [04:34:45:940871] [Tid0x0000a220] [debug] <--[C5] DL_Rom_GetInfoAll
[0000012] [04:35:16:083534] [Tid0x000088c4] [debug] -->[C6] FlashTool_Connect #(api.cpp, line:883)
[0000013] [04:35:16:083534] [Tid0x000088c4] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000014] [04:35:16:083534] [Tid0x000088c4] [debug] -->[C7] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000015] [04:35:16:083534] [Tid0x000088c4] [debug] bCheckScatter: 1
[0000016] [04:35:16:083534] [Tid0x000088c4] [debug] -->[C8] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000017] [04:35:16:083534] [Tid0x000088c4] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000018] [04:35:16:083534] [Tid0x000088c4] [debug] have load scatter already #(api.cpp, line:1943)
[0000019] [04:35:16:083534] [Tid0x000088c4] [debug] libversion 2 #(api.cpp, line:1960)
[0000020] [04:35:16:083534] [Tid0x000088c4] [debug] -->[C9] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000021] [04:35:16:682670] [Tid0x000088c4] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2554)
[0000022] [04:35:16:682670] [Tid0x000088c4] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000023] [04:35:16:683670] [Tid0x000088c4] [debug] callback in brom stage #(cflashtool_api.cpp, line:1209)
[0000024] [04:35:16:683670] [Tid0x000088c4] [debug] m_cb_in_brom_stage run success #(cflashtool_api.cpp, line:1217)
[0000025] [04:35:16:683670] [Tid0x000088c4] [debug] connect_brom_ex OK #(cflashtool_api.cpp, line:1223)
[0000026] [04:35:16:683670] [Tid0x000088c4] [debug] <--[C9] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000027] [04:35:16:683670] [Tid0x000088c4] [debug] <--[C8] FlashTool_Connect_BROM_Ex
[0000028] [04:35:16:683670] [Tid0x000088c4] [debug] bRealCheckScatter: 1 #(api.cpp, line:968)
[0000029] [04:35:16:683670] [Tid0x000088c4] [debug] -->[C10] FlashTool_Connect_Download_DA #(api.cpp, line:2071)
[0000030] [04:35:16:683670] [Tid0x000088c4] [debug] bCheckScatter: 1 #(api.cpp, line:2072)
[0000031] [04:35:16:683670] [Tid0x000088c4] [debug] -->[C11] cflashtool_api::FlashTool_Connect_Download_DA #(cflashtool_api.cpp, line:1384)
[0000032] [04:35:16:683670] [Tid0x000088c4] [debug] bCheckScatter: 1 #(cflashtool_api.cpp, line:1385)
[0000033] [04:35:16:683670] [Tid0x000088c4] [debug] da_source type: 0 #(cflashtool_api.cpp, line:1414)
[0000034] [04:35:16:684670] [Tid0x000088c4] [debug] checksum_level: 3,  battery_setting: 0, reset_key_setting: 0 #(cflashtool_api.cpp, line:1528)
[0000035] [04:35:16:684670] [Tid0x000088c4] [debug] connect_da_end_stage: 2,  enable_dram_in_1st_da: 0, da_log_level: 2, da_log_channel: 1, ufs_provision: 56966788 #(cflashtool_api.cpp, line:1531)
[0000036] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_cid[0]: 0x410301d6 #(cflashtool_api.cpp, line:1646)
[0000037] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_cid[1]: 0x34344133 #(cflashtool_api.cpp, line:1646)
[0000038] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_cid[2]: 0xbf391032 #(cflashtool_api.cpp, line:1646)
[0000039] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_cid[3]: 0x25b824d7 #(cflashtool_api.cpp, line:1646)
[0000040] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[0]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000041] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[1]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000042] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[2]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000043] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[3]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000044] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[4]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000045] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[5]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000046] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[6]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000047] [04:35:17:507437] [Tid0x000088c4] [debug] m_emmc_fwver[7]: 0x0 #(cflashtool_api.cpp, line:1652)
[0000048] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: BBCHIP_TYPE: MT6765 #(cflashtool_api.cpp, line:1279)
[0000049] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: BBCHIP: "MT6765", EXT_26M 
[0000050] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: BBCHIP: CODE(0x6765), VER(0xCA00), SW(0x0000)              
[0000051] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_ret="S_DA_INVALID_STORAGE_TYPE"(3178)                                  
[0000052] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_chip_select[0]="CS_0"(0x00)                   
[0000053] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_chip_select[1]="CS_0"(0x00)                   
[0000054] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_id(0x0000)="[AMD] AM29DL323D"                         
[0000055] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_size(0x00000000)                              
[0000056] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_1(0x0000)                        
[0000057] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_2(0x0000)                        
[0000058] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_3(0x0000)                        
[0000059] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_dev_code_4(0x0000)                        
[0000060] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_otp_status="S_DONE"(0)                     
[0000061] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NOR: m_nor_flash_otp_size(0x00000000)                          
[0000062] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_ret="S_DA_INVALID_STORAGE_TYPE"(3178)                                
[0000063] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_chip_select="CS_0"(0x00)                    
[0000064] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_id(0x0000)="[SAMSUNG] K9F5608Q0C"                       
[0000065] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_size(0x0000000000000000)                        
[0000066] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_flash_id_count(0x0000)                        
[0000067] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_pagesize(0)                                  
[0000068] [04:35:17:507437] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_sparesize(0)                                 
[0000069] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_pages_per_block(0)                           
[0000070] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_io_interface( )                              
[0000071] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_addr_cycle( )                                
[0000072] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: NAND: m_nand_bmt_exist( )                                
[0000073] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_ret="S_DONE"(0)          
[0000074] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_boot1_size(0x00400000)      
[0000075] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_boot2_size(0x00400000)      
[0000076] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_rpmb_size(0x01000000)       
[0000077] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp1_size(0x00000000)        
[0000078] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp2_size(0x00000000)        
[0000079] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp3_size(0x00000000)        
[0000080] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_gp4_size(0x00000000)        
[0000081] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_ua_size(0x0000001CCF000000)      
[0000082] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: eMMC: m_emmc_cid(0x410301D6 0x34344133 0xBF391032 0x25B824D7)      
[0000083] [04:35:17:508443] [Tid0x000088c4] [debug] DA_REPORT: eMMC: m_emmc_fwver(0x0  0x0  0x0  0x0  0x0  0x0  0x0  0x0 )      
[0000084] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_ret="S_DA_INVALID_STORAGE_TYPE"(3178)          
[0000085] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_ua_size(0x0000000000000000)      
[0000086] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: SDMMC: m_sdmmc_cid(0x00000000 0x00000000 0x00000000 0x00000000)      
[0000087] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: INT_RAM: m_int_sram_ret="S_DONE"(0)                         
[0000088] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: INT_RAM: m_int_sram_size(0x0003A000)                           
[0000089] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_ret="S_DONE"(0)                          
[0000090] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_type(0x02)="HW_RAM_DRAM"                     
[0000091] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_chip_select(0x00)="CS_0"              
[0000092] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: EXT_RAM: m_ext_ram_size(0x0000000100000000)                            
[0000093] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: RandomID: 0xA36C9DBB 0xC939517C 0x0D29B500 0x59B1A306
[0000094] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_ret="S_DA_INVALID_STORAGE_TYPE"(3178)          
[0000095] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu0_size(0x0000000000000000)      
[0000096] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu1_size(0x0000000000000000)      
[0000097] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_lu2_size(0x0000000000000000)      
[0000098] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_cid()      
[0000099] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_fwver()      
[0000100] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_sn()      
[0000101] [04:35:17:508443] [Tid0x000088c4] [debug] SyncWithDA(): DA_REPORT: UFS: m_ufs_vendor_id(0x0)      
[0000102] [04:35:17:508443] [Tid0x000088c4] [debug] <--[C11] cflashtool_api::FlashTool_Connect_Download_DA
[0000103] [04:35:17:508443] [Tid0x000088c4] [debug] <--[C10] FlashTool_Connect_Download_DA
[0000104] [04:35:17:508443] [Tid0x000088c4] [debug] <--[C7] FlashTool_Connect_Ex
[0000105] [04:35:17:508443] [Tid0x000088c4] [debug] <--[C6] FlashTool_Connect
[0000106] [04:35:17:509442] [Tid0x000088c4] [debug] -->[C12] FlashTool_Download #(api.cpp, line:1300)
[0000107] [04:35:17:509442] [Tid0x000088c4] [debug] -->[C13] cflashtool_api::FlashTool_Download #(cflashtool_api.cpp, line:412)
[0000108] [04:35:17:509442] [Tid0x000088c4] [debug] -->[C14] DL_GetCount #(api.cpp, line:2696)
[0000109] [04:35:17:509442] [Tid0x000088c4] [debug] <--[C14] DL_GetCount
[0000110] [04:35:17:509442] [Tid0x000088c4] [debug] -->[C15] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000111] [04:35:17:510445] [Tid0x000088c4] [debug] <--[C15] DL_Rom_GetInfoAll
[0000112] [04:37:58:224721] [Tid0x000088c4] [debug] <--[C13] cflashtool_api::FlashTool_Download
[0000113] [04:37:58:224721] [Tid0x000088c4] [debug] <--[C12] FlashTool_Download
[0000114] [04:37:58:224721] [Tid0x000088c4] [debug] -->[C16] FlashTool_EnableWatchDogTimeout #(api.cpp, line:1220)
[0000115] [04:37:58:224721] [Tid0x000088c4] [debug] -->[C17] cflashtool_api::FlashTool_EnableWatchDogTimeout #(cflashtool_api.cpp, line:150)
[0000116] [04:37:58:231235] [Tid0x000088c4] [debug] <--[C17] cflashtool_api::FlashTool_EnableWatchDogTimeout
[0000117] [04:37:58:231235] [Tid0x000088c4] [debug] <--[C16] FlashTool_EnableWatchDogTimeout
[0000118] [04:37:58:231235] [Tid0x000088c4] [debug] -->[C18] FlashTool_Disconnect #(api.cpp, line:1033)
[0000119] [04:37:58:231235] [Tid0x000088c4] [debug] -->[C19] cflashtool_api::FlashTool_Disconnect #(cflashtool_api.cpp, line:33)
[0000120] [04:37:58:248794] [Tid0x000088c4] [debug] -->[C20] DL_ClearFTHandle #(api.cpp, line:2618)
[0000121] [04:37:58:248794] [Tid0x000088c4] [debug] <--[C20] DL_ClearFTHandle
[0000122] [04:37:58:248794] [Tid0x000088c4] [debug] <--[C19] cflashtool_api::FlashTool_Disconnect
[0000123] [04:37:58:248794] [Tid0x000088c4] [debug] <--[C18] FlashTool_Disconnect

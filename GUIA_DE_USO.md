# 📱 GUÍA DE USO - SHARPMTKCLIENT_ENGY

## 🎯 INTRODUCCIÓN

SharpMTKClient_Engy es una herramienta profesional para flashear dispositivos con chipsets MediaTek (MTK). Esta guía te ayudará a usar la aplicación de manera segura y efectiva.

---

## ⚠️ ADVERTENCIAS IMPORTANTES

### 🚨 **ANTES DE COMENZAR:**
- ⚠️ **RIESGO DE BRICK**: El flasheo incorrecto puede dañar permanentemente tu dispositivo
- 🔋 **Batería**: Asegúrate de que el dispositivo tenga al menos 50% de batería
- 🔌 **Cable USB**: Usa un cable USB de buena calidad
- 💾 **Backup**: Siempre haz un backup completo antes de flashear
- 🚫 **No desconectes**: Nunca desconectes el dispositivo durante el flasheo

---

## 🛠️ PREPARACIÓN

### 📋 **Requisitos Previos:**

1. **Drivers MTK Instalados**
   - Instala los drivers MediaTek USB VCOM
   - Verifica que el dispositivo sea reconocido en modo download

2. **Archivos Necesarios**
   - 📄 **Archivo Scatter** (.txt) - Configuración de particiones
   - 🔧 **Download Agent** (.bin) - Agente de descarga MTK
   - 🔐 **Archivo Auth** (.auth) - Autenticación (opcional)
   - 📦 **Archivos ROM** - Imágenes de firmware

3. **Dispositivo en Modo Download**
   - Apaga el dispositivo completamente
   - Mantén presionado Vol- mientras conectas USB
   - O usa combinación específica de tu dispositivo

---

## 🚀 PROCESO DE FLASHEO

### **Paso 1: Iniciar la Aplicación**

1. Ejecuta `SharpMTKClient_Engy.exe` como administrador
2. Verifica que aparezcan los mensajes de inicialización en la consola:
   ```
   [FORM INFO] 2024-XX-XX XX:XX:XX - Initializing MTK handlers...
   [FORM INFO] 2024-XX-XX XX:XX:XX - MTK Download handler initialized
   [FORM INFO] 2024-XX-XX XX:XX:XX - Application initialized successfully
   ```

### **Paso 2: Cargar Archivo Scatter**

1. Haz clic en el botón **"..."** junto a "Scatter File"
2. Selecciona tu archivo scatter (.txt)
3. **Indicadores de éxito:**
   - ✅ Campo se vuelve verde
   - ✅ Lista de particiones se llena automáticamente
   - ✅ Mensaje: "Scatter file loaded: X partitions found"

4. **Verificación:**
   - Revisa que todas las particiones necesarias estén listadas
   - Verifica que los archivos ROM existan (filas blancas = OK, rosa = faltante)

### **Paso 3: Cargar Download Agent**

1. Haz clic en el botón **"..."** junto a "Download Agent"
2. Selecciona tu archivo DA (.bin)
3. **Indicadores de éxito:**
   - ✅ Campo se vuelve verde
   - ✅ Mensaje: "Download Agent loaded successfully"

### **Paso 4: Cargar Autenticación (Opcional)**

1. Haz clic en el botón **"..."** junto a "Auth File"
2. Selecciona tu archivo de autenticación (.auth)
3. **Indicadores de éxito:**
   - ✅ Mensaje: "Authentication file loaded successfully"

### **Paso 5: Configurar Modo de Flasheo**

Selecciona el modo apropiado en el dropdown:

- **📥 Download Only**: Solo flashea las particiones seleccionadas
- **🔄 Firmware Upgrade**: Actualización de firmware con formateo FRP
- **🗑️ Format All + Download**: Formatea completamente y flashea

### **Paso 6: Conectar Dispositivo**

1. Asegúrate de que el dispositivo esté en modo download
2. Conecta el dispositivo via USB
3. Haz clic en **"Download"**

### **Paso 7: Proceso de Flasheo**

1. **Búsqueda de dispositivo:**
   ```
   [FORM INFO] Searching for MTK device (timeout: 60s)...
   [FORM INFO] Device found on COM port: X
   ```

2. **Inicio del flasheo:**
   ```
   [FORM INFO] Starting flash operation...
   [FORM INFO] Flash thread started: comX
   ```

3. **Monitoreo:**
   - Observa los logs en la consola
   - La barra de progreso se actualizará
   - NO desconectes el dispositivo

4. **Finalización:**
   ```
   Download Done
   ```

---

## 🔍 INTERPRETACIÓN DE LOGS

### **Logs Normales (INFO):**
```
[MTK_DA INFO] 2024-XX-XX XX:XX:XX - Loading DA file: path/to/da.bin (Size: XXXkB)
[MTK_DA INFO] 2024-XX-XX XX:XX:XX - DA loaded successfully: da.bin
[FORM INFO] 2024-XX-XX XX:XX:XX - Scatter file loaded successfully. X partitions enabled.
```

### **Advertencias (WARNING):**
```
[MTK_DA WARNING] 2024-XX-XX XX:XX:XX - DA file has unexpected extension: .xxx. Expected .bin
[FORM WARNING] 2024-XX-XX XX:XX:XX - ROM file not found: path/to/rom.img
```

### **Errores (ERROR):**
```
[MTK_DA ERROR] 2024-XX-XX XX:XX:XX - DA file not found: path/to/da.bin
[FORM ERROR] 2024-XX-XX XX:XX:XX - Device detection timeout
```

---

## ❌ SOLUCIÓN DE PROBLEMAS

### **🔴 "Device detection timeout"**
**Causas:**
- Dispositivo no en modo download
- Drivers no instalados correctamente
- Cable USB defectuoso

**Soluciones:**
1. Reinstala drivers MTK
2. Prueba otro cable USB
3. Verifica modo download del dispositivo
4. Reinicia la aplicación como administrador

### **🔴 "DA file not found" o "DA_Load failed"**
**Causas:**
- Archivo DA incorrecto o corrupto
- Permisos de archivo

**Soluciones:**
1. Verifica que el archivo DA sea correcto para tu chipset
2. Ejecuta como administrador
3. Descarga nuevo archivo DA

### **🔴 "Scatter file is not valid"**
**Causas:**
- Archivo scatter corrupto o incorrecto
- Archivos ROM faltantes

**Soluciones:**
1. Verifica que el scatter sea para tu dispositivo exacto
2. Asegúrate de que todos los archivos ROM estén presentes
3. Verifica rutas en el archivo scatter

### **🔴 "Flash operation failed"**
**Causas:**
- Dispositivo desconectado durante flasheo
- Archivos ROM corruptos
- Problema de autenticación

**Soluciones:**
1. NO desconectes y reinicia el proceso
2. Verifica integridad de archivos ROM
3. Usa archivo de autenticación correcto

---

## 📁 ARCHIVOS DE LOG

Los logs se guardan automáticamente en:
```
/log/MTK_DA_YYYYMMDD.log      - Logs del Download Agent
/log/SharpMTK_YYYYMMDD.log    - Logs generales
/log/BROM_DLL_V5.log          - Logs de la librería MTK
```

**Para soporte técnico, incluye siempre estos archivos.**

---

## ✅ VERIFICACIÓN POST-FLASHEO

### **Después del flasheo exitoso:**

1. **Desconecta el dispositivo**
2. **Enciende normalmente** (puede tardar más la primera vez)
3. **Verifica funcionalidades básicas:**
   - WiFi, Bluetooth, llamadas
   - Cámara, sensores
   - Carga de batería

### **Si el dispositivo no enciende:**
1. Intenta entrar en modo recovery
2. Verifica si responde en modo download
3. Considera reflashear con firmware completo

---

## 🆘 SOPORTE Y RECURSOS

### **Antes de pedir ayuda:**
1. ✅ Lee esta guía completamente
2. ✅ Verifica que tienes los archivos correctos
3. ✅ Revisa los logs de error
4. ✅ Intenta los pasos de solución de problemas

### **Al pedir ayuda, incluye:**
- Modelo exacto del dispositivo
- Archivos de log completos
- Descripción detallada del problema
- Pasos que ya intentaste

---

## 📞 CONTACTO

Para soporte técnico o reportar bugs, contacta al desarrollador con:
- Logs completos
- Información del dispositivo
- Descripción detallada del problema

---

*Recuerda: El flasheo siempre conlleva riesgos. Procede bajo tu propia responsabilidad.*

﻿using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Security;
using System;

namespace SharpMTKClient_Engy.MediaTeK
{
    public class FlashtoollibEx
    {
        [DllImport("FlashtoollibEx.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern IntPtr flashtool_create_session();

        [DllImport("FlashtoollibEx.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int flashtool_read_partition_table(IntPtr F28E3432, ref IntPtr intptr_1);

        [DllImport("FlashtoollibEx.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int flashtool_set_ufs_config(IntPtr intptr_1, ref E82EE19B C616FC8E);

    }
}

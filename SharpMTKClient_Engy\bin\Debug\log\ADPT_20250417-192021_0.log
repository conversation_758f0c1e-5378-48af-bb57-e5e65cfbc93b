[0000001] [19:20:21:787578] [Tid0x00002b88] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [19:20:21:788570] [Tid0x00002b88] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [19:20:24:904288] [Tid0x0000a34c] [debug] -->[C2] DL_LoadScatter #(api.cpp, line:2554)
[0000004] [19:20:24:920287] [Tid0x0000a34c] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000005] [19:20:24:920287] [Tid0x0000a34c] [debug] <--[C2] DL_LoadScatter
[0000006] [19:20:24:921286] [Tid0x0000a34c] [debug] -->[C3] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000007] [19:20:24:930294] [Tid0x0000a34c] [debug] <--[C3] DL_AutoLoadRomImages
[0000008] [19:20:24:931286] [Tid0x0000a34c] [debug] -->[C4] DL_GetCount #(api.cpp, line:2696)
[0000009] [19:20:24:938216] [Tid0x0000a34c] [debug] <--[C4] DL_GetCount
[0000010] [19:20:24:938216] [Tid0x0000a34c] [debug] -->[C5] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000011] [19:20:24:938216] [Tid0x0000a34c] [debug] <--[C5] DL_Rom_GetInfoAll
[0000012] [19:25:15:101978] [Tid0x0000c7ec] [debug] -->[C6] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000013] [19:25:15:102978] [Tid0x0000c7ec] [debug] <--[C6] DL_SetChecksumLevel
[0000014] [19:25:15:105979] [Tid0x0000c7ec] [debug] -->[C7] FlashTool_Connect #(api.cpp, line:883)
[0000015] [19:25:15:105979] [Tid0x0000c7ec] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000016] [19:25:15:105979] [Tid0x0000c7ec] [debug] -->[C8] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000017] [19:25:15:105979] [Tid0x0000c7ec] [debug] bCheckScatter: 1
[0000018] [19:25:15:105979] [Tid0x0000c7ec] [debug] -->[C9] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000019] [19:25:15:105979] [Tid0x0000c7ec] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000020] [19:25:15:105979] [Tid0x0000c7ec] [debug] have load scatter already #(api.cpp, line:1943)
[0000021] [19:25:15:105979] [Tid0x0000c7ec] [debug] libversion 2 #(api.cpp, line:1960)
[0000022] [19:25:15:105979] [Tid0x0000c7ec] [debug] -->[C10] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000023] [19:25:15:415646] [Tid0x0000c7ec] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2554)
[0000024] [19:25:15:415646] [Tid0x0000c7ec] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000025] [19:25:15:420646] [Tid0x0000c7ec] [debug] <--[C10] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000026] [19:25:15:420646] [Tid0x0000c7ec] [debug] <--[C9] FlashTool_Connect_BROM_Ex
[0000027] [19:25:15:420646] [Tid0x0000c7ec] [debug] <--[C8] FlashTool_Connect_Ex
[0000028] [19:25:15:420646] [Tid0x0000c7ec] [debug] <--[C7] FlashTool_Connect
[0000029] [19:25:15:421646] [Tid0x0000c7ec] [debug] -->[C11] FlashTool_Disconnect #(api.cpp, line:1033)
[0000030] [19:25:15:421646] [Tid0x0000c7ec] [debug] -->[C12] cflashtool_api::FlashTool_Disconnect #(cflashtool_api.cpp, line:33)
[0000031] [19:25:15:421646] [Tid0x0000c7ec] [debug] -->[C13] DL_ClearFTHandle #(api.cpp, line:2618)
[0000032] [19:25:15:421646] [Tid0x0000c7ec] [debug] <--[C13] DL_ClearFTHandle
[0000033] [19:25:15:421646] [Tid0x0000c7ec] [debug] <--[C12] cflashtool_api::FlashTool_Disconnect
[0000034] [19:25:15:421646] [Tid0x0000c7ec] [debug] <--[C11] FlashTool_Disconnect

[0000001] [19:43:19:568337] [Tid0x0000e9ec] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [19:43:19:569336] [Tid0x0000e9ec] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [19:43:22:691030] [Tid0x0000bb10] [debug] -->[C2] DL_LoadScatter #(api.cpp, line:2554)
[0000004] [19:43:22:707037] [Tid0x0000bb10] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000005] [19:43:22:707037] [Tid0x0000bb10] [debug] <--[C2] DL_LoadScatter
[0000006] [19:43:22:708030] [Tid0x0000bb10] [debug] -->[C3] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000007] [19:43:22:717030] [Tid0x0000bb10] [debug] <--[C3] DL_AutoLoadRomImages
[0000008] [19:43:22:718031] [Tid0x0000bb10] [debug] -->[C4] DL_GetCount #(api.cpp, line:2696)
[0000009] [19:43:22:724508] [Tid0x0000bb10] [debug] <--[C4] DL_GetCount
[0000010] [19:43:22:724508] [Tid0x0000bb10] [debug] -->[C5] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000011] [19:43:22:724508] [Tid0x0000bb10] [debug] <--[C5] DL_Rom_GetInfoAll
[0000012] [19:43:51:426031] [Tid0x0000dccc] [debug] -->[C6] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000013] [19:43:51:426031] [Tid0x0000dccc] [debug] <--[C6] DL_SetChecksumLevel
[0000014] [19:43:51:429030] [Tid0x0000dccc] [debug] -->[C7] FlashTool_Connect #(api.cpp, line:883)
[0000015] [19:43:51:429030] [Tid0x0000dccc] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000016] [19:43:51:429030] [Tid0x0000dccc] [debug] -->[C8] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000017] [19:43:51:429030] [Tid0x0000dccc] [debug] bCheckScatter: 1
[0000018] [19:43:51:429030] [Tid0x0000dccc] [debug] -->[C9] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000019] [19:43:51:429030] [Tid0x0000dccc] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000020] [19:43:51:429030] [Tid0x0000dccc] [debug] have load scatter already #(api.cpp, line:1943)
[0000021] [19:43:51:429030] [Tid0x0000dccc] [debug] libversion 2 #(api.cpp, line:1960)
[0000022] [19:43:51:429030] [Tid0x0000dccc] [debug] -->[C10] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000023] [19:43:51:936264] [Tid0x0000dccc] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2554)
[0000024] [19:43:51:936264] [Tid0x0000dccc] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000025] [19:43:51:945771] [Tid0x0000dccc] [debug] <--[C10] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000026] [19:43:51:945771] [Tid0x0000dccc] [debug] <--[C9] FlashTool_Connect_BROM_Ex
[0000027] [19:43:51:945771] [Tid0x0000dccc] [debug] <--[C8] FlashTool_Connect_Ex
[0000028] [19:43:51:945771] [Tid0x0000dccc] [debug] <--[C7] FlashTool_Connect
[0000029] [19:43:51:946771] [Tid0x0000dccc] [debug] -->[C11] FlashTool_Disconnect #(api.cpp, line:1033)
[0000030] [19:43:51:946771] [Tid0x0000dccc] [debug] -->[C12] cflashtool_api::FlashTool_Disconnect #(cflashtool_api.cpp, line:33)
[0000031] [19:43:51:946771] [Tid0x0000dccc] [debug] -->[C13] DL_ClearFTHandle #(api.cpp, line:2618)
[0000032] [19:43:51:946771] [Tid0x0000dccc] [debug] <--[C13] DL_ClearFTHandle
[0000033] [19:43:51:946771] [Tid0x0000dccc] [debug] <--[C12] cflashtool_api::FlashTool_Disconnect
[0000034] [19:43:51:946771] [Tid0x0000dccc] [debug] <--[C11] FlashTool_Disconnect

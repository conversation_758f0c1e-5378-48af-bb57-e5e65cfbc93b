﻿using SharpMTKClient_Engy.CSharpMTK_Parsed;
using SharpMTKClient_Engy.CSharpMTK_Parsed.BL;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Data;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Module;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Utility;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace SharpMTKClient_Engy
{
	public partial class Form1 : Form
	{
		private string dlFile = null;
		private string daFile = null;
		private string authFile = null;
		private List<MTK_DL.ROM_INFO> rom_info_list = new List<MTK_DL.ROM_INFO>();
		private List<Thread> threads = new List<Thread>();


		public Form1()
		{
			InitializeComponent();
			CheckForIllegalCrossThreadCalls = false; // Disable cross-thread call check

			//Init Handlers
			MTK_DL.DLHandle();
			MTK_DA.DAHandle();
			MTK_AUTH.AUTHHandle();

			//Init MTK Log
			MTK_FlashTool.DebugLogsOn();

			FlashModeSelector.SelectedIndex = 0;
		}


		private void button4_Click(object sender, System.EventArgs e)
		{
			Thread thread = new Thread(DownloadFiles);
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start();
		}

		public void SetProgressBar(int value)
		{
			if (InvokeRequired)
			{
				Invoke(new Action<int>(SetProgressBar), value);
			}
			else
			{
				progressBar1.Value = value;
			}
		}

		private void DownloadFiles()
		{
			try
			{
				//Check Log Folder
				if (!Directory.Exists("log"))
				{
					Directory.CreateDirectory("log");
				}

				if (string.IsNullOrEmpty(txtScatter.Text))
				{
					MessageBox.Show("Please select Scatter");
					return;
				}
				//if (dlFile != txtScatter.Text)
				//{
				//	dlFile = txtScatter.Text;
				//	MTK_DL.DLLoadScatter(0, dlFile);
				//	MTK_DL.DLAutoLoadRomImages(0, dlFile);
				//	MTK_DL.DLVerifyROMMemBuf(0, dlFile, rom_info_list);

				//	rom_info_list = MTK_DL.GetAllRomInfo(0);
				//}

				string fullName = new DirectoryInfo(txtScatter.Text.Trim()).Parent.FullName;

				if (string.IsNullOrEmpty(txtDa.Text))
				{
					MessageBox.Show("Please select DA");
					return;
				}

				if (daFile != txtDa.Text)
				{
					daFile = txtDa.Text;
					MTK_DA.DALOAD(daFile);
				}

                MTK_DA.DALOAD(daFile);

                //if (authFile != txtAuth.Text)
                //{
                //	authFile = txtAuth.Text;
                //	MTK_AUTH.AUTHLOAD(authFile);
                //}

                //IEnumerable<string> dr = GetFlashDoneDevice();
                //DrInit(ref dr);

                SaveFfu.GetFfu(fullName);
				if (FlashGlobal.IsFirmwarewrite)
				{
					SaveFfu.GetWb(fullName);
				}

				MTKDevice mTKDevice = new MTKDevice();
				mTKDevice.swPath = fullName;
				mTKDevice.scatter = txtScatter.Text.Trim();
				mTKDevice.da = txtDa.Text;
				mTKDevice.dl_type = txtAuth.Text;
				mTKDevice.chkSum = int.Parse(MiProperty.ChkSumTable["None"].ToString());
				mTKDevice.sort = 0;
				mTKDevice.rom_info_list = rom_info_list;
				mTKDevice.idx = 0;
				mTKDevice.getDeviceTimeout = 60;
				mTKDevice.comPortIndex = 0;
				mTKDevice.DownloadMode = FlashModeSelector.SelectedItem.ToString();

				SetProgressBar(0);

				TimeOut timeOut = new TimeOut();
				timeOut.Do = mTKDevice.getMtkdeviceAuto;
				if (timeOut.DoWithTimeout(new TimeSpan(0, 0, 0, mTKDevice.getDeviceTimeout)) || !timeOut.mResult)
				{
					MessageBox.Show("find device timeout");
					return;
				}
				if (mTKDevice.comPortIndex != 0)
				{
					try
					{
						mTKDevice.deviceName = $"com={mTKDevice.comPortIndex.ToString()}";
						if (!timer_updateStatus.Enabled)
						{
							timer_updateStatus.Enabled = true;
						}
						//FlashingDevice.flashDeviceList[0].StartTime = DateTime.Now;
						//FlashingDevice.flashDeviceList[0].Status = "flashing";
						//FlashingDevice.flashDeviceList[0].Progress = 0f;
						//FlashingDevice.flashDeviceList[0].IsDone = false;
						//FlashingDevice.flashDeviceList[0].IsUpdate = true;
						//DrawCln(mTKDevice.idx, mTKDevice.deviceName, 0.0);
						//reDrawProgress();
						//FlashingDevice.UpdateDeviceStatus(mTKDevice.deviceName, null, "start flash", "flashing", isDone: false);
						Thread thread = new Thread(mTKDevice.flash);
						thread.Name = $"com={mTKDevice.comPortIndex.ToString()}";
						thread.IsBackground = true;
						thread.Start();
						//FlashingDevice.flashDeviceList[0].DThread = thread;
						threads.Add(thread);
					}
					catch (Exception ex)
					{
						MessageBox.Show(ex.Message);
						Console.WriteLine(ex.Message + "  " + ex.StackTrace);
					}
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine("MAIN ERROR: " + ex.Message);
				Console.WriteLine("MAIN ERROR: " + ex.StackTrace);
			}

		}

		private void button1_Click(object sender, EventArgs e)
		{
			Thread thread = new Thread(SelectScatter);
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start();
		}

		private void SelectScatter()
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = "Scatter Files (*.txt)|*.txt";
			openFileDialog.Title = "Select Scatter File";
			RomList_FilesFromScatter.Rows.Clear();
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				txtScatter.Text = openFileDialog.FileName;
				//txtScatter.Text = openFileDialog.FileName;
				//txtScatter.Text = openFileDialog.FileName;

				dlFile = txtScatter.Text;
				MTK_DL.DLLoadScatter(0, dlFile);
				MTK_DL.DLAutoLoadRomImages(0, dlFile);
				MTK_DL.DLVerifyROMMemBuf(0, dlFile, rom_info_list);


				rom_info_list = MTK_DL.GetAllRomInfo(0);
				Console.WriteLine(rom_info_list.Count);
				//RomList_FilesFromScatter.DataSource = rom_info_list;

				foreach (var rom in rom_info_list)
				{
					//Console.WriteLine("");
					//Console.WriteLine("");
					//Console.WriteLine("ROM Info:");
					//Console.WriteLine($"rom.name {rom.name}");
					//Console.WriteLine($"rom.addr_type {rom.addr_type}");
					//Console.WriteLine($"rom.combo_partsize_check {rom.combo_partsize_check}");
					//Console.WriteLine($"rom.enable {rom.enable}");
					//Console.WriteLine($"rom.filepath {rom.filepath}");
					//Console.WriteLine($"rom.filesize {rom.filesize}");
					//Console.WriteLine($"rom.index {rom.index}");
					//Console.WriteLine($"rom.is_reserved {rom.is_reserved}");
					//Console.WriteLine($"rom.item_is_visable {rom.item_is_visable}");
					//Console.WriteLine($"rom.operation_type {rom.operation_type}");
					//Console.WriteLine($"rom.partition_size {rom.partition_size} 0x{rom.partition_size:X16}");
					//Console.WriteLine($"rom.part_id {rom.part_id}");
					//Console.WriteLine($"rom.region_addr {rom.region_addr} 0x{rom.region_addr:X16}");
					//Console.WriteLine($"rom.rom_type {rom.rom_type}");
					//Console.WriteLine($"rom.begin_addr: {rom.begin_addr} 0x{rom.begin_addr:X16}");
					//Console.WriteLine($"rom.end_addr: {rom.end_addr} 0x{rom.end_addr:X16}");
					//RomList_FilesFromScatter.Rows.Add(rom.enable, rom.name, rom.partition_size.ToString("X16"), rom.begin_addr.ToString("X16"), rom.end_addr.ToString("X16"), rom.filepath);
					if (rom.enable)
					{
						RomList_FilesFromScatter.Rows.Add(true, rom.name, rom.addr_type, rom.begin_addr.ToString("X16"), rom.end_addr.ToString("X16"), rom.filepath);
					}
				}

				RomList_FilesFromScatter.ReadOnly = false;
				RomList_FilesFromScatter.Columns[0].ReadOnly = false;
			}
		}

		private void RomList_FilesFromScatter_CellContentClick(object sender, DataGridViewCellEventArgs e)
		{
			if (e.ColumnIndex == 0) // índice de la columna del checkbox
			{
				RomList_FilesFromScatter.CommitEdit(DataGridViewDataErrorContexts.Commit);
			}
		}

		private void button2_Click(object sender, EventArgs e)
		{
			Thread thread = new Thread(SelectDA);
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start();
		}

		private void SelectDA()
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = "Download Agent Files (*.bin)|*.bin";
			openFileDialog.Title = "Select Download Agent File";
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				txtDa.Text = openFileDialog.FileName;
				daFile = txtDa.Text;
				MTK_DA.DALOAD(daFile);
			}
		}
	}
}

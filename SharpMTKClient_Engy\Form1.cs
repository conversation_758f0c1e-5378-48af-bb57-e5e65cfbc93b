﻿using SharpMTKClient_Engy.CSharpMTK_Parsed;
using SharpMTKClient_Engy.CSharpMTK_Parsed.BL;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Data;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Module;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Utility;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace SharpMTKClient_Engy
{
	public partial class Form1 : Form
	{
		private string dlFile = null;
		private string daFile = null;
		private string authFile = null;
		private List<MTK_DL.ROM_INFO> rom_info_list = new List<MTK_DL.ROM_INFO>();
		private List<Thread> threads = new List<Thread>();


		public Form1()
		{
			InitializeComponent();
			CheckForIllegalCrossThreadCalls = false; // Disable cross-thread call check

			InitializeApplication();
		}

		/// <summary>
		/// Initialize the application with proper error handling
		/// </summary>
		private void InitializeApplication()
		{
			try
			{
				// Initialize MTK Handlers with error checking
				LogInfo("Initializing MTK handlers...");

				try
				{
					MTK_DL.DLHandle();
					LogInfo("MTK Download handler initialized");
				}
				catch (Exception ex)
				{
					ShowError($"Failed to initialize MTK Download handler: {ex.Message}");
					return;
				}

				if (!MTK_DA.DAHandle())
				{
					ShowError("Failed to initialize MTK Download Agent handler");
					return;
				}

				MTK_AUTH.AUTHHandle();

				// Initialize MTK Logging
				MTK_FlashTool.DebugLogsOn();

				// Set default flash mode
				FlashModeSelector.SelectedIndex = 0;

				// Update UI state
				UpdateUIState();

				LogInfo("Application initialized successfully");
			}
			catch (Exception ex)
			{
				ShowError($"Failed to initialize application: {ex.Message}");
			}
		}


		private void button4_Click(object sender, System.EventArgs e)
		{
			Thread thread = new Thread(DownloadFiles);
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start();
		}

		public void SetProgressBar(int value)
		{
			if (InvokeRequired)
			{
				Invoke(new Action<int>(SetProgressBar), value);
			}
			else
			{
				progressBar1.Value = value;
			}
		}

		private void DownloadFiles()
		{
			try
			{
				LogInfo("Starting download/flash operation...");

				// Validate all required files first
				if (!ValidateAllFiles())
				{
					return;
				}

				// Ensure log directory exists
				string logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "log");
				if (!Directory.Exists(logDir))
				{
					Directory.CreateDirectory(logDir);
				}

				string scatterPath = txtScatter.Text.Trim();
				string fullName = new DirectoryInfo(scatterPath).Parent.FullName;

				LogInfo($"Working directory: {fullName}");
				LogInfo($"Scatter file: {scatterPath}");
				LogInfo($"DA file: {txtDa.Text}");

				// Reload DA if file has changed
				if (daFile != txtDa.Text)
				{
					LogInfo("DA file changed, reloading...");
					if (!MTK_DA.DALOAD(txtDa.Text, enableValidation: true))
					{
						ShowError("Failed to reload Download Agent");
						return;
					}
					daFile = txtDa.Text;
				}

				// Verify DA is ready
				if (!MTK_DA.IsDAReady())
				{
					ShowError("Download Agent is not ready for operations");
					return;
				}

				// Load authentication file if specified
				if (!string.IsNullOrEmpty(txtAuth.Text))
				{
					if (authFile != txtAuth.Text)
					{
						LogInfo($"Loading authentication file: {txtAuth.Text}");
						authFile = txtAuth.Text;
						MTK_AUTH.AUTHLOAD(authFile);
					}
				}

				// Process FFU files
				LogInfo("Processing FFU files...");
				SaveFfu.GetFfu(fullName);
				if (FlashGlobal.IsFirmwarewrite)
				{
					LogInfo("Processing WB files...");
					SaveFfu.GetWb(fullName);
				}

				// Configure MTK device
				LogInfo("Configuring MTK device...");
				MTKDevice mTKDevice = new MTKDevice();
				mTKDevice.swPath = fullName;
				mTKDevice.scatter = scatterPath;
				mTKDevice.da = txtDa.Text;
				mTKDevice.dl_type = txtAuth.Text ?? string.Empty;
				mTKDevice.chkSum = int.Parse(MiProperty.ChkSumTable["None"].ToString());
				mTKDevice.sort = 0;
				mTKDevice.rom_info_list = rom_info_list;
				mTKDevice.idx = 0;
				mTKDevice.getDeviceTimeout = 60;
				mTKDevice.comPortIndex = 0;
				mTKDevice.DownloadMode = FlashModeSelector.SelectedItem?.ToString() ?? "Download Only";

				LogInfo($"Flash mode: {mTKDevice.DownloadMode}");

				// Reset progress bar
				SetProgressBar(0);

				// Search for MTK device with timeout
				LogInfo($"Searching for MTK device (timeout: {mTKDevice.getDeviceTimeout}s)...");
				TimeOut timeOut = new TimeOut();
				timeOut.Do = mTKDevice.getMtkdeviceAuto;

				if (timeOut.DoWithTimeout(new TimeSpan(0, 0, 0, mTKDevice.getDeviceTimeout)) || !timeOut.mResult)
				{
					ShowError("Device detection timeout. Please ensure device is connected and in download mode.");
					LogError("Device detection timeout");
					return;
				}

				if (mTKDevice.comPortIndex != 0)
				{
					try
					{
						mTKDevice.deviceName = $"com={mTKDevice.comPortIndex}";
						LogInfo($"Device found on COM port: {mTKDevice.comPortIndex}");

						// Enable status timer
						if (!timer_updateStatus.Enabled)
						{
							timer_updateStatus.Enabled = true;
						}

						// Start flashing in background thread
						LogInfo("Starting flash operation...");
						Thread thread = new Thread(mTKDevice.flash)
						{
							Name = $"com={mTKDevice.comPortIndex}",
							IsBackground = true
						};

						thread.Start();
						threads.Add(thread);

						LogInfo($"Flash thread started: {thread.Name}");
					}
					catch (Exception ex)
					{
						ShowError($"Error starting flash operation: {ex.Message}");
						LogError($"Flash operation error: {ex.Message}\nStack trace: {ex.StackTrace}");
					}
				}
				else
				{
					ShowError("No MTK device found. Please check device connection and drivers.");
					LogError("No MTK device detected");
				}
			}
			catch (Exception ex)
			{
				ShowError($"Critical error during download operation: {ex.Message}");
				LogError($"CRITICAL ERROR: {ex.Message}\nStack trace: {ex.StackTrace}");
			}
		}

		private void button1_Click(object sender, EventArgs e)
		{
			Thread thread = new Thread(SelectScatter);
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start();
		}

		private void SelectScatter()
		{
			try
			{
				OpenFileDialog openFileDialog = new OpenFileDialog();
				openFileDialog.Filter = "Scatter Files (*.txt)|*.txt|All Files (*.*)|*.*";
				openFileDialog.Title = "Select Scatter File";
				openFileDialog.CheckFileExists = true;
				openFileDialog.CheckPathExists = true;

				// Clear previous ROM list
				RomList_FilesFromScatter.Rows.Clear();

				if (openFileDialog.ShowDialog() == DialogResult.OK)
				{
					string selectedFile = openFileDialog.FileName;

					// Validate scatter file
					if (!ValidateScatterFile(selectedFile))
					{
						ShowError("Selected file does not appear to be a valid Scatter file.");
						return;
					}

					LogInfo($"Loading scatter file: {selectedFile}");
					txtScatter.Text = selectedFile;

					// Load scatter file
					dlFile = selectedFile;
					MTK_DL.DLLoadScatter(0, dlFile);
					MTK_DL.DLAutoLoadRomImages(0, dlFile);
					MTK_DL.DLVerifyROMMemBuf(0, dlFile, rom_info_list);

					// Get ROM information
					rom_info_list = MTK_DL.GetAllRomInfo(0);
					LogInfo($"Found {rom_info_list.Count} ROM partitions");

					// Populate ROM list in UI
					int enabledCount = 0;
					foreach (var rom in rom_info_list)
					{
						if (rom.enable)
						{
							// Validate ROM file exists
							bool fileExists = !string.IsNullOrEmpty(rom.filepath) && File.Exists(rom.filepath);
							Color rowColor = fileExists ? Color.White : Color.LightPink;

							int rowIndex = RomList_FilesFromScatter.Rows.Add(
								true,
								rom.name,
								rom.addr_type,
								rom.begin_addr.ToString("X16"),
								rom.end_addr.ToString("X16"),
								rom.filepath ?? "N/A"
							);

							// Color code rows based on file existence
							if (!fileExists)
							{
								RomList_FilesFromScatter.Rows[rowIndex].DefaultCellStyle.BackColor = rowColor;
								LogWarning($"ROM file not found: {rom.filepath}");
							}

							enabledCount++;
						}
					}

					// Configure DataGridView
					RomList_FilesFromScatter.ReadOnly = false;
					RomList_FilesFromScatter.Columns[0].ReadOnly = false;

					// Update UI state
					UpdateUIState();

					LogInfo($"Scatter file loaded successfully. {enabledCount} partitions enabled.");
					ShowSuccess($"Scatter file loaded: {enabledCount} partitions found");
				}
			}
			catch (Exception ex)
			{
				ShowError($"Error loading scatter file: {ex.Message}");
				LogError($"Scatter file loading error: {ex.Message}");

				// Clear UI on error
				txtScatter.Text = string.Empty;
				RomList_FilesFromScatter.Rows.Clear();
				dlFile = null;
			}
		}

		private void RomList_FilesFromScatter_CellContentClick(object sender, DataGridViewCellEventArgs e)
		{
			if (e.ColumnIndex == 0) // índice de la columna del checkbox
			{
				RomList_FilesFromScatter.CommitEdit(DataGridViewDataErrorContexts.Commit);
			}
		}

		private void button2_Click(object sender, EventArgs e)
		{
			Thread thread = new Thread(SelectDA);
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start();
		}

		private void button3_Click(object sender, EventArgs e)
		{
			Thread thread = new Thread(SelectAuth);
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start();
		}

		private void SelectDA()
		{
			try
			{
				OpenFileDialog openFileDialog = new OpenFileDialog();
				openFileDialog.Filter = "Download Agent Files (*.bin)|*.bin|All Files (*.*)|*.*";
				openFileDialog.Title = "Select Download Agent File";
				openFileDialog.CheckFileExists = true;
				openFileDialog.CheckPathExists = true;

				if (openFileDialog.ShowDialog() == DialogResult.OK)
				{
					string selectedFile = openFileDialog.FileName;

					// Validate DA file before loading
					if (!MTK_DA.ValidateDAFile(selectedFile))
					{
						ShowError("Selected file does not appear to be a valid Download Agent file.");
						return;
					}

					// Update UI
					txtDa.Text = selectedFile;

					// Load DA with validation
					if (MTK_DA.DALOAD(selectedFile, enableValidation: true))
					{
						daFile = selectedFile;
						ShowSuccess($"Download Agent loaded successfully: {Path.GetFileName(selectedFile)}");
						UpdateUIState();
					}
					else
					{
						ShowError("Failed to load Download Agent file. Please check the file and try again.");
						txtDa.Text = string.Empty;
						daFile = null;
					}
				}
			}
			catch (Exception ex)
			{
				ShowError($"Error selecting Download Agent: {ex.Message}");
			}
		}

		private void SelectAuth()
		{
			try
			{
				OpenFileDialog openFileDialog = new OpenFileDialog();
				openFileDialog.Filter = "Authentication Files (*.auth)|*.auth|All Files (*.*)|*.*";
				openFileDialog.Title = "Select Authentication File";
				openFileDialog.CheckFileExists = true;
				openFileDialog.CheckPathExists = true;

				if (openFileDialog.ShowDialog() == DialogResult.OK)
				{
					string selectedFile = openFileDialog.FileName;

					// Basic validation for auth file
					if (!File.Exists(selectedFile))
					{
						ShowError("Selected authentication file does not exist.");
						return;
					}

					// Update UI
					txtAuth.Text = selectedFile;
					authFile = selectedFile;

					// Load authentication file
					try
					{
						MTK_AUTH.AUTHLOAD(authFile);
						ShowSuccess($"Authentication file loaded: {Path.GetFileName(selectedFile)}");
						LogInfo($"Authentication file loaded: {selectedFile}");
						UpdateUIState();
					}
					catch (Exception ex)
					{
						ShowError($"Failed to load authentication file: {ex.Message}");
						txtAuth.Text = string.Empty;
						authFile = null;
					}
				}
			}
			catch (Exception ex)
			{
				ShowError($"Error selecting authentication file: {ex.Message}");
			}
		}

		#region UI Helper Methods

		/// <summary>
		/// Update UI state based on current application state
		/// </summary>
		private void UpdateUIState()
		{
			try
			{
				// Update button states
				button4.Enabled = !string.IsNullOrEmpty(txtScatter.Text) && !string.IsNullOrEmpty(txtDa.Text);

				// Update status indicators (you could add visual indicators here)
				UpdateStatusIndicators();
			}
			catch (Exception ex)
			{
				LogError($"Error updating UI state: {ex.Message}");
			}
		}

		/// <summary>
		/// Update visual status indicators
		/// </summary>
		private void UpdateStatusIndicators()
		{
			// Change text color based on file status
			txtScatter.BackColor = !string.IsNullOrEmpty(txtScatter.Text) && File.Exists(txtScatter.Text)
				? Color.LightGreen : Color.White;

			txtDa.BackColor = MTK_DA.IsDALoaded
				? Color.LightGreen : Color.White;
		}

		/// <summary>
		/// Show error message to user
		/// </summary>
		/// <param name="message">Error message</param>
		private void ShowError(string message)
		{
			MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
			LogError(message);
		}

		/// <summary>
		/// Show success message to user
		/// </summary>
		/// <param name="message">Success message</param>
		private void ShowSuccess(string message)
		{
			MessageBox.Show(message, "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
			LogInfo(message);
		}

		/// <summary>
		/// Show warning message to user
		/// </summary>
		/// <param name="message">Warning message</param>
		private void ShowWarning(string message)
		{
			MessageBox.Show(message, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
			LogWarning(message);
		}

		#endregion

		#region Logging Methods

		/// <summary>
		/// Log information message
		/// </summary>
		/// <param name="message">Message to log</param>
		private void LogInfo(string message)
		{
			string logMessage = $"[FORM INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
			Console.WriteLine(logMessage);
			WriteToLogFile(logMessage);
		}

		/// <summary>
		/// Log warning message
		/// </summary>
		/// <param name="message">Message to log</param>
		private void LogWarning(string message)
		{
			string logMessage = $"[FORM WARNING] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
			Console.WriteLine(logMessage);
			WriteToLogFile(logMessage);
		}

		/// <summary>
		/// Log error message
		/// </summary>
		/// <param name="message">Message to log</param>
		private void LogError(string message)
		{
			string logMessage = $"[FORM ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
			Console.WriteLine(logMessage);
			WriteToLogFile(logMessage);
		}

		/// <summary>
		/// Write message to log file
		/// </summary>
		/// <param name="message">Message to write</param>
		private void WriteToLogFile(string message)
		{
			try
			{
				string logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "log");
				if (!Directory.Exists(logDir))
				{
					Directory.CreateDirectory(logDir);
				}

				string logFile = Path.Combine(logDir, $"SharpMTK_{DateTime.Now:yyyyMMdd}.log");
				File.AppendAllText(logFile, message + Environment.NewLine);
			}
			catch
			{
				// Ignore logging errors to prevent cascading failures
			}
		}

		#endregion

		#region Validation Methods

		/// <summary>
		/// Validate scatter file
		/// </summary>
		/// <param name="filePath">Path to scatter file</param>
		/// <returns>True if valid</returns>
		private bool ValidateScatterFile(string filePath)
		{
			try
			{
				if (!File.Exists(filePath))
				{
					return false;
				}

				string extension = Path.GetExtension(filePath).ToLowerInvariant();
				if (extension != ".txt")
				{
					return false;
				}

				// Check if file contains scatter-like content
				string content = File.ReadAllText(filePath);
				return content.Contains("partition_index") || content.Contains("begin_addr") || content.Contains("end_addr");
			}
			catch
			{
				return false;
			}
		}

		/// <summary>
		/// Validate all required files before starting flash operation
		/// </summary>
		/// <returns>True if all files are valid</returns>
		private bool ValidateAllFiles()
		{
			// Check scatter file
			if (string.IsNullOrEmpty(txtScatter.Text))
			{
				ShowError("Please select a Scatter file");
				return false;
			}

			if (!ValidateScatterFile(txtScatter.Text))
			{
				ShowError("Selected Scatter file is not valid or does not exist");
				return false;
			}

			// Check DA file
			if (string.IsNullOrEmpty(txtDa.Text))
			{
				ShowError("Please select a Download Agent file");
				return false;
			}

			if (!MTK_DA.IsDALoaded)
			{
				ShowError("Download Agent is not properly loaded");
				return false;
			}

			return true;
		}

		#endregion

		/// <summary>
		/// Handle form closing to cleanup resources
		/// </summary>
		/// <param name="e">Form closing event args</param>
		protected override void OnFormClosing(FormClosingEventArgs e)
		{
			try
			{
				// Cleanup MTK resources
				MTK_DA.DAHandleDestroy();
				MTK_DL.DLHandleDestroy();

				// Wait for threads to complete
				foreach (Thread thread in threads)
				{
					if (thread.IsAlive)
					{
						thread.Join(5000); // Wait up to 5 seconds
					}
				}

				LogInfo("Application closing - resources cleaned up");
			}
			catch (Exception ex)
			{
				LogError($"Error during cleanup: {ex.Message}");
			}

			base.OnFormClosing(e);
		}
	}
}

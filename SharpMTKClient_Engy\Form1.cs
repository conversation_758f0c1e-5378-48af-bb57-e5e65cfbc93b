﻿using SharpMTKClient_Engy.CSharpMTK_Parsed;
using SharpMTKClient_Engy.CSharpMTK_Parsed.BL;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Data;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Module;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Utility;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace SharpMTKClient_Engy
{
    public partial class Form1 : Form
    {
        private string dlFile = null;
        private string daFile = null;
        private string authFile = null;
        private List<MTK_DL.ROM_INFO> rom_info_list = new List<MTK_DL.ROM_INFO>();
        private List<Thread> threads = new List<Thread>();


        public Form1()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false; // Disable cross-thread call check

            InitializeApplication();
        }

        /// <summary>
        /// Initialize the application with proper error handling
        /// </summary>
        private void InitializeApplication()
        {
            try
            {
                // Add global exception handlers
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                LogInfo("Global exception handlers installed");

                // Initialize MTK Handlers with error checking
                LogInfo("Initializing MTK handlers...");

                try
                {
                    MTK_DL.DLHandle();
                    LogInfo("MTK Download handler initialized");
                }
                catch (Exception ex)
                {
                    ShowError($"Failed to initialize MTK Download handler: {ex.Message}");
                    return;
                }

                if (!MTK_DA.DAHandle())
                {
                    ShowError("Failed to initialize MTK Download Agent handler");
                    return;
                }

                MTK_AUTH.AUTHHandle();

                // Initialize MTK Logging
                MTK_FlashTool.DebugLogsOn();

                // Set default flash mode
                FlashModeSelector.SelectedIndex = 0;

                // Update UI state
                UpdateUIState();

                LogInfo("Application initialized successfully");
            }
            catch (Exception ex)
            {
                ShowError($"Failed to initialize application: {ex.Message}");
                LogError($"InitializeApplication exception: {ex.Message}\nStack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handle unhandled thread exceptions
        /// </summary>
        private void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            try
            {
                LogError($"UNHANDLED THREAD EXCEPTION: {e.Exception.Message}");
                LogError($"Stack trace: {e.Exception.StackTrace}");

                MessageBox.Show(
                    $"An unexpected error occurred:\n{e.Exception.Message}\n\nThe application will continue running, but some features may not work correctly.\n\nPlease check the log files for more details.",
                    "Unexpected Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
            catch
            {
                // Last resort - don't let exception handling crash the app
            }
        }

        /// <summary>
        /// Handle unhandled domain exceptions
        /// </summary>
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                Exception ex = e.ExceptionObject as Exception;
                LogError($"UNHANDLED DOMAIN EXCEPTION: {ex?.Message ?? "Unknown error"}");
                LogError($"Stack trace: {ex?.StackTrace ?? "No stack trace available"}");
                LogError($"Is terminating: {e.IsTerminating}");

                if (!e.IsTerminating)
                {
                    MessageBox.Show(
                        $"A critical error occurred:\n{ex?.Message ?? "Unknown error"}\n\nPlease check the log files for more details.",
                        "Critical Error",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error
                    );
                }
            }
            catch
            {
                // Last resort - don't let exception handling crash the app
            }
        }


        private void button4_Click(object sender, System.EventArgs e)
        {
            Thread thread = new Thread(DownloadFiles);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        public void SetProgressBar(int value)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int>(SetProgressBar), value);
            }
            else
            {
                progressBar1.Value = value;
            }
        }

        private void DownloadFiles()
        {
            try
            {
                LogInfo("Starting download/flash operation...");

                // Validate all required files first
                if (!ValidateAllFiles())
                {
                    return;
                }

                // Ensure log directory exists
                string logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "log");
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                string scatterPath = txtScatter.Text.Trim();
                string fullName = new DirectoryInfo(scatterPath).Parent.FullName;

                LogInfo($"Working directory: {fullName}");
                LogInfo($"Scatter file: {scatterPath}");
                LogInfo($"DA file: {txtDa.Text}");

                // Reload DA if file has changed
                if (daFile != txtDa.Text)
                {
                    LogInfo("DA file changed, reloading...");
                    if (!MTK_DA.DALOAD(txtDa.Text, enableValidation: true))
                    {
                        ShowError("Failed to reload Download Agent");
                        return;
                    }
                    daFile = txtDa.Text;
                }

                // Verify DA is ready
                if (!MTK_DA.IsDAReady())
                {
                    ShowError("Download Agent is not ready for operations");
                    return;
                }

                // Load authentication file if specified
                if (!string.IsNullOrEmpty(txtAuth.Text))
                {
                    if (authFile != txtAuth.Text)
                    {
                        LogInfo($"Loading authentication file: {txtAuth.Text}");
                        authFile = txtAuth.Text;
                        MTK_AUTH.AUTHLOAD(authFile);
                    }
                }

                // Process FFU files
                LogInfo("Processing FFU files...");
                SaveFfu.GetFfu(fullName);
                if (FlashGlobal.IsFirmwarewrite)
                {
                    LogInfo("Processing WB files...");
                    SaveFfu.GetWb(fullName);
                }

                // Configure MTK device
                LogInfo("Configuring MTK device...");
                MTKDevice mTKDevice = new MTKDevice();
                mTKDevice.swPath = fullName;
                mTKDevice.scatter = scatterPath;
                mTKDevice.da = txtDa.Text;
                mTKDevice.dl_type = txtAuth.Text ?? string.Empty;
                mTKDevice.chkSum = int.Parse(MiProperty.ChkSumTable["None"].ToString());
                mTKDevice.sort = 0;
                mTKDevice.rom_info_list = rom_info_list;
                mTKDevice.idx = 0;
                mTKDevice.getDeviceTimeout = 60;
                mTKDevice.comPortIndex = 0;
                mTKDevice.DownloadMode = FlashModeSelector.SelectedItem?.ToString() ?? "Download Only";

                LogInfo($"Flash mode: {mTKDevice.DownloadMode}");

                // Reset progress bar
                SetProgressBar(0);

                // Search for MTK device with timeout
                LogInfo($"Searching for MTK device (timeout: {mTKDevice.getDeviceTimeout}s)...");
                TimeOut timeOut = new TimeOut();
                timeOut.Do = mTKDevice.getMtkdeviceAuto;

                if (timeOut.DoWithTimeout(new TimeSpan(0, 0, 0, mTKDevice.getDeviceTimeout)) || !timeOut.mResult)
                {
                    ShowError("Device detection timeout. Please ensure device is connected and in download mode.");
                    LogError("Device detection timeout");
                    return;
                }

                if (mTKDevice.comPortIndex != 0)
                {
                    try
                    {
                        mTKDevice.deviceName = $"com={mTKDevice.comPortIndex}";
                        LogInfo($"Device found on COM port: {mTKDevice.comPortIndex}");

                        // Enable status timer
                        if (!timer_updateStatus.Enabled)
                        {
                            timer_updateStatus.Enabled = true;
                        }

                        // Start flashing in background thread
                        LogInfo("Starting flash operation...");
                        Thread thread = new Thread(mTKDevice.flash)
                        {
                            Name = $"com={mTKDevice.comPortIndex}",
                            IsBackground = true
                        };

                        thread.Start();
                        threads.Add(thread);

                        LogInfo($"Flash thread started: {thread.Name}");
                    }
                    catch (Exception ex)
                    {
                        ShowError($"Error starting flash operation: {ex.Message}");
                        LogError($"Flash operation error: {ex.Message}\nStack trace: {ex.StackTrace}");
                    }
                }
                else
                {
                    ShowError("No MTK device found. Please check device connection and drivers.");
                    LogError("No MTK device detected");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Critical error during download operation: {ex.Message}");
                LogError($"CRITICAL ERROR: {ex.Message}\nStack trace: {ex.StackTrace}");
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(SelectScatter);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void SelectScatter()
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "Scatter Files (*.txt)|*.txt|All Files (*.*)|*.*";
                openFileDialog.Title = "Select Scatter File";
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;

                // Clear previous ROM list
                RomList_FilesFromScatter.Rows.Clear();

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string selectedFile = openFileDialog.FileName;

                    // Validate scatter file
                    if (!ValidateScatterFile(selectedFile))
                    {
                        ShowError("Selected file does not appear to be a valid Scatter file.");
                        return;
                    }

                    LogInfo($"Loading scatter file: {selectedFile}");
                    txtScatter.Text = selectedFile;

                    // Load scatter file
                    dlFile = selectedFile;
                    MTK_DL.DLLoadScatter(0, dlFile);
                    MTK_DL.DLAutoLoadRomImages(0, dlFile);
                    MTK_DL.DLVerifyROMMemBuf(0, dlFile, rom_info_list);

                    // Get ROM information
                    rom_info_list = MTK_DL.GetAllRomInfo(0);
                    LogInfo($"Found {rom_info_list.Count} ROM partitions");

                    // Populate ROM list in UI
                    int enabledCount = 0;
                    foreach (var rom in rom_info_list)
                    {
                        if (rom.enable)
                        {
                            // Validate ROM file exists
                            bool fileExists = !string.IsNullOrEmpty(rom.filepath) && File.Exists(rom.filepath);
                            Color rowColor = fileExists ? Color.White : Color.LightPink;

                            int rowIndex = RomList_FilesFromScatter.Rows.Add(
                                true,
                                rom.name,
                                rom.addr_type,
                                rom.begin_addr.ToString("X16"),
                                rom.end_addr.ToString("X16"),
                                rom.filepath ?? "N/A"
                            );

                            // Color code rows based on file existence
                            if (!fileExists)
                            {
                                RomList_FilesFromScatter.Rows[rowIndex].DefaultCellStyle.BackColor = rowColor;
                                LogWarning($"ROM file not found: {rom.filepath}");
                            }

                            enabledCount++;
                        }
                    }

                    // Configure DataGridView
                    RomList_FilesFromScatter.ReadOnly = false;
                    RomList_FilesFromScatter.Columns[0].ReadOnly = false;

                    // Update UI state
                    UpdateUIState();

                    LogInfo($"Scatter file loaded successfully. {enabledCount} partitions enabled.");
                    ShowSuccess($"Scatter file loaded: {enabledCount} partitions found");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error loading scatter file: {ex.Message}");
                LogError($"Scatter file loading error: {ex.Message}");

                // Clear UI on error
                txtScatter.Text = string.Empty;
                RomList_FilesFromScatter.Rows.Clear();
                dlFile = null;
            }
        }

        private void RomList_FilesFromScatter_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 0) // índice de la columna del checkbox
            {
                RomList_FilesFromScatter.CommitEdit(DataGridViewDataErrorContexts.Commit);
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(SelectDA);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(SelectAuth);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void btnReadPartitions_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(ReadPartitions);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void btnRebootMeta_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(RebootToMetaMode);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void btnAdvancedOps_Click(object sender, EventArgs e)
        {
            ShowAdvancedOperationsMenu();
        }

        private void btnErasePartition_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(ErasePartitionDialog);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void btnUnlockBootloader_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(UnlockBootloaderOperation);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void btnRelockBootloader_Click(object sender, EventArgs e)
        {
            Thread thread = new Thread(RelockBootloaderOperation);
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        private void btnNVOperations_Click(object sender, EventArgs e)
        {
            ShowNVOperationsDialog();
        }

        private void btnRPMBOperations_Click(object sender, EventArgs e)
        {
            ShowRPMBOperationsDialog();
        }

        private void btnGPTOperations_Click(object sender, EventArgs e)
        {
            ShowGPTOperationsDialog();
        }

        private void btnRawFirmware_Click(object sender, EventArgs e)
        {
            ShowRawFirmwareDialog();
        }

        private void btnPreloaderOps_Click(object sender, EventArgs e)
        {
            ShowPreloaderOperationsDialog();
        }

        private void btnFlashModes_Click(object sender, EventArgs e)
        {
            ShowFlashModesDialog();
        }

        private void SelectDA()
        {
            try
            {
                LogInfo("Starting DA selection process...");

                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "Download Agent Files (*.bin)|*.bin|All Files (*.*)|*.*";
                openFileDialog.Title = "Select Download Agent File";
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;

                LogInfo("Showing file dialog...");
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string selectedFile = openFileDialog.FileName;
                    LogInfo($"User selected DA file: {selectedFile}");

                    // Validate DA file before loading
                    LogInfo("Validating DA file...");
                    if (!MTK_DA.ValidateDAFile(selectedFile))
                    {
                        LogError("DA file validation failed");
                        ShowError("Selected file does not appear to be a valid Download Agent file.");
                        return;
                    }

                    LogInfo("DA file validation passed");

                    // Update UI first
                    this.Invoke((MethodInvoker)delegate
                    {
                        txtDa.Text = selectedFile;
                    });

                    LogInfo("Starting DA load operation...");

                    // Load DA with validation in try-catch with detailed logging
                    bool loadResult = false;
                    try
                    {
                        LogInfo("Attempting standard DA load...");
                        loadResult = MTK_DA.DALOAD(selectedFile, enableValidation: true);
                        LogInfo($"Standard DA load result: {loadResult}");
                    }
                    catch (Exception daEx)
                    {
                        LogError($"Exception during standard DA load: {daEx.Message}");
                        LogError($"Attempting safe DA load as fallback...");

                        try
                        {
                            loadResult = MTK_DA.DALOAD_Safe(selectedFile, enableValidation: true);
                            LogInfo($"Safe DA load result: {loadResult}");
                        }
                        catch (Exception safeEx)
                        {
                            LogError($"Exception during safe DA load: {safeEx.Message}");
                            loadResult = false;
                        }
                    }

                    // Handle result
                    if (loadResult)
                    {
                        daFile = selectedFile;
                        LogInfo("DA loaded successfully, updating UI...");

                        this.Invoke((MethodInvoker)delegate
                        {
                            ShowSuccess($"Download Agent loaded successfully: {Path.GetFileName(selectedFile)}");
                            UpdateUIState();
                        });
                    }
                    else
                    {
                        LogError("All DA load methods failed");
                        this.Invoke((MethodInvoker)delegate
                        {
                            ShowError("Failed to load Download Agent file. Please check the file and try again.");
                            txtDa.Text = string.Empty;
                        });
                        daFile = null;
                    }
                }
                else
                {
                    LogInfo("User cancelled DA selection");
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in SelectDA: {ex.Message}");
                LogError($"SelectDA stack trace: {ex.StackTrace}");

                try
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        ShowError($"Error selecting Download Agent: {ex.Message}");
                    });
                }
                catch (Exception invokeEx)
                {
                    LogError($"Failed to show error message: {invokeEx.Message}");
                }
            }
        }

        private void SelectAuth()
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "Authentication Files (*.auth)|*.auth|All Files (*.*)|*.*";
                openFileDialog.Title = "Select Authentication File";
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string selectedFile = openFileDialog.FileName;

                    // Basic validation for auth file
                    if (!File.Exists(selectedFile))
                    {
                        ShowError("Selected authentication file does not exist.");
                        return;
                    }

                    // Update UI
                    txtAuth.Text = selectedFile;
                    authFile = selectedFile;

                    // Load authentication file
                    try
                    {
                        MTK_AUTH.AUTHLOAD(authFile);
                        ShowSuccess($"Authentication file loaded: {Path.GetFileName(selectedFile)}");
                        LogInfo($"Authentication file loaded: {selectedFile}");
                        UpdateUIState();
                    }
                    catch (Exception ex)
                    {
                        ShowError($"Failed to load authentication file: {ex.Message}");
                        txtAuth.Text = string.Empty;
                        authFile = null;
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error selecting authentication file: {ex.Message}");
            }
        }

        private void ReadPartitions()
        {
            try
            {
                LogInfo("Starting read partitions operation...");

                // Validate that we have a device connected
                if (!ValidateDeviceConnection())
                {
                    return;
                }

                // Create MTK FlashTool instance
                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("ReadPartitions");

                // Connect to device
                LogInfo("Connecting to device for partition reading...");
                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());

                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device for partition reading. Error code: {connectResult}");
                    return;
                }

                // Read partitions
                LogInfo("Reading partition information...");
                List<MTK_FlashTool.PartitionInfo> partitions = flashTool.ReadPartitions();

                if (partitions == null)
                {
                    ShowError("Failed to read partition information from device.");
                    flashTool.DADisConnect(0);
                    return;
                }

                if (partitions.Count == 0)
                {
                    ShowWarning("No partitions found on device.");
                    flashTool.DADisConnect(0);
                    return;
                }

                // Display partition information
                DisplayPartitionInfo(partitions);

                // Disconnect
                flashTool.DADisConnect(0);

                ShowSuccess($"Successfully read {partitions.Count} partitions from device");
                LogInfo($"Read partitions operation completed successfully. Found {partitions.Count} partitions.");
            }
            catch (Exception ex)
            {
                ShowError($"Error reading partitions: {ex.Message}");
                LogError($"Exception in ReadPartitions: {ex.Message}");
            }
        }

        private void RebootToMetaMode()
        {
            try
            {
                LogInfo("Starting reboot to META mode operation...");

                // Validate that we have a device connected
                if (!ValidateDeviceConnection())
                {
                    return;
                }

                // Confirm with user
                DialogResult result = MessageBox.Show(
                    "This will reboot the device to META mode. Continue?",
                    "Confirm Reboot to META Mode",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                {
                    LogInfo("Reboot to META mode cancelled by user");
                    return;
                }

                // Create MTK FlashTool instance
                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("RebootMeta");

                // Connect to device
                LogInfo("Connecting to device for META mode reboot...");
                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());

                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device for META mode reboot. Error code: {connectResult}");
                    return;
                }

                // Reboot to META mode
                LogInfo("Rebooting device to META mode...");
                bool rebootResult = flashTool.RebootToMetaMode(15000, false); // 15 second timeout

                // Disconnect
                flashTool.DADisConnect(0);

                if (rebootResult)
                {
                    ShowSuccess("Device successfully rebooted to META mode");
                    LogInfo("Reboot to META mode completed successfully");
                }
                else
                {
                    ShowError("Failed to reboot device to META mode");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error rebooting to META mode: {ex.Message}");
                LogError($"Exception in RebootToMetaMode: {ex.Message}");
            }
        }

        private bool ValidateDeviceConnection()
        {
            // Basic validation - check if we have necessary files loaded
            if (string.IsNullOrEmpty(txtDa.Text) || !MTK_DA.IsDALoaded)
            {
                ShowError("Please load a Download Agent file first");
                return false;
            }

            return true;
        }

        private int GetAvailableComPort()
        {
            int comPortIndex = 0;
            while (!ComPortCtrl.getDevicesMtk(out comPortIndex))
            {
                Console.WriteLine($"Waiting for MTK device on COM port {comPortIndex}...");
            }
            return comPortIndex;
            // This is a simplified version - in a real implementation,
            // you would scan for available COM ports and detect MTK devices
            // For now, return a default port number
            //return 3; // COM3 as default
        }

        private void DisplayPartitionInfo(List<MTK_FlashTool.PartitionInfo> partitions)
        {
            try
            {
                // Create a new form to display partition information
                Form partitionForm = new Form();
                partitionForm.Text = "Device Partition Information";
                partitionForm.Size = new Size(800, 600);
                partitionForm.StartPosition = FormStartPosition.CenterParent;

                // Create DataGridView to display partitions
                DataGridView partitionGrid = new DataGridView();
                partitionGrid.Dock = DockStyle.Fill;
                partitionGrid.AutoGenerateColumns = false;
                partitionGrid.ReadOnly = true;
                partitionGrid.AllowUserToAddRows = false;
                partitionGrid.AllowUserToDeleteRows = false;

                // Add columns
                partitionGrid.Columns.Add("Name", "Partition Name");
                partitionGrid.Columns.Add("BeginAddr", "Begin Address");
                partitionGrid.Columns.Add("EndAddr", "End Address");
                partitionGrid.Columns.Add("Size", "Size (MB)");
                partitionGrid.Columns.Add("PartID", "Partition ID");
                partitionGrid.Columns.Add("Type", "Type");

                // Set column widths
                partitionGrid.Columns[0].Width = 150;
                partitionGrid.Columns[1].Width = 120;
                partitionGrid.Columns[2].Width = 120;
                partitionGrid.Columns[3].Width = 100;
                partitionGrid.Columns[4].Width = 80;
                partitionGrid.Columns[5].Width = 100;

                // Populate data
                foreach (var partition in partitions)
                {
                    double sizeMB = (double)partition.size / (1024 * 1024);
                    partitionGrid.Rows.Add(
                        partition.name,
                        $"0x{partition.begin_addr:X16}",
                        $"0x{partition.end_addr:X16}",
                        $"{sizeMB:F2}",
                        partition.part_id,
                        partition.type
                    );
                }

                // Add close button
                Button closeButton = new Button();
                closeButton.Text = "Close";
                closeButton.Size = new Size(100, 30);
                closeButton.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
                closeButton.Location = new Point(partitionForm.Width - 120, partitionForm.Height - 60);
                closeButton.Click += (s, e) => partitionForm.Close();

                // Add controls to form
                partitionForm.Controls.Add(partitionGrid);
                partitionForm.Controls.Add(closeButton);

                // Show form
                partitionForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                LogError($"Error displaying partition information: {ex.Message}");
                ShowError($"Error displaying partition information: {ex.Message}");
            }
        }

        #region Advanced Operations

        private void ShowAdvancedOperationsMenu()
        {
            try
            {
                Form advancedForm = new Form();
                advancedForm.Text = "Advanced MTK Operations";
                advancedForm.Size = new Size(400, 300);
                advancedForm.StartPosition = FormStartPosition.CenterParent;

                // Create buttons for advanced operations
                Button btnReadPartitionAdv = new Button { Text = "Read Partition", Size = new Size(150, 30), Location = new Point(20, 20) };
                Button btnErasePartitionAdv = new Button { Text = "Erase Partition", Size = new Size(150, 30), Location = new Point(200, 20) };
                Button btnBootloaderStatus = new Button { Text = "Bootloader Status", Size = new Size(150, 30), Location = new Point(20, 60) };
                Button btnFlashSelected = new Button { Text = "Flash Selected", Size = new Size(150, 30), Location = new Point(200, 60) };

                btnReadPartitionAdv.Click += (s, e) => { advancedForm.Close(); ReadPartitionDialog(); };
                btnErasePartitionAdv.Click += (s, e) => { advancedForm.Close(); ErasePartitionDialog(); };
                btnBootloaderStatus.Click += (s, e) => { advancedForm.Close(); CheckBootloaderStatus(); };
                btnFlashSelected.Click += (s, e) => { advancedForm.Close(); FlashSelectedPartitionsDialog(); };

                advancedForm.Controls.AddRange(new Control[] { btnReadPartitionAdv, btnErasePartitionAdv, btnBootloaderStatus, btnFlashSelected });
                advancedForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowError($"Error showing advanced operations: {ex.Message}");
            }
        }

        private void ErasePartitionDialog()
        {
            try
            {
                if (!ValidateDeviceConnection())
                    return;

                string partitionName = ShowInputDialog("Enter partition name to erase:", "Erase Partition", "userdata");

                if (string.IsNullOrEmpty(partitionName))
                    return;

                DialogResult result = MessageBox.Show(
                    $"Are you sure you want to erase partition '{partitionName}'?\nThis operation cannot be undone!",
                    "Confirm Erase",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning
                );

                if (result != DialogResult.Yes)
                    return;

                LogInfo($"Starting erase operation for partition: {partitionName}");

                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("ErasePartition");

                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());
                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device. Error code: {connectResult}");
                    return;
                }

                bool eraseResult = flashTool.ErasePartition(partitionName);
                flashTool.DADisConnect(0);

                if (eraseResult)
                {
                    ShowSuccess($"Partition '{partitionName}' erased successfully");
                }
                else
                {
                    ShowError($"Failed to erase partition '{partitionName}'");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error erasing partition: {ex.Message}");
            }
        }

        private void UnlockBootloaderOperation()
        {
            try
            {
                if (!ValidateDeviceConnection())
                    return;

                DialogResult result = MessageBox.Show(
                    "Unlocking the bootloader will void your warranty and may brick your device.\nContinue?",
                    "Unlock Bootloader Warning",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning
                );

                if (result != DialogResult.Yes)
                    return;

                LogInfo("Starting bootloader unlock operation");

                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("UnlockBootloader");

                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());
                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device. Error code: {connectResult}");
                    return;
                }

                bool unlockResult = flashTool.UnlockBootloader();
                flashTool.DADisConnect(0);

                if (unlockResult)
                {
                    ShowSuccess("Bootloader unlocked successfully");
                }
                else
                {
                    ShowError("Failed to unlock bootloader");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error unlocking bootloader: {ex.Message}");
            }
        }

        private void RelockBootloaderOperation()
        {
            try
            {
                if (!ValidateDeviceConnection())
                    return;

                DialogResult result = MessageBox.Show(
                    "Are you sure you want to relock the bootloader?",
                    "Relock Bootloader",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                    return;

                LogInfo("Starting bootloader relock operation");

                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("RelockBootloader");

                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());
                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device. Error code: {connectResult}");
                    return;
                }

                bool relockResult = flashTool.RelockBootloader();
                flashTool.DADisConnect(0);

                if (relockResult)
                {
                    ShowSuccess("Bootloader relocked successfully");
                }
                else
                {
                    ShowError("Failed to relock bootloader");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error relocking bootloader: {ex.Message}");
            }
        }

        private void CheckBootloaderStatus()
        {
            try
            {
                if (!ValidateDeviceConnection())
                    return;

                LogInfo("Checking bootloader status");

                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("BootloaderStatus");

                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());
                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device. Error code: {connectResult}");
                    return;
                }

                MTK_FlashTool.BootloaderLockState state = flashTool.GetBootloaderLockState();
                flashTool.DADisConnect(0);

                string statusMessage = $"Bootloader Status: {state}";
                MessageBox.Show(statusMessage, "Bootloader Status", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LogInfo(statusMessage);
            }
            catch (Exception ex)
            {
                ShowError($"Error checking bootloader status: {ex.Message}");
            }
        }

        private string ShowInputDialog(string prompt, string title, string defaultValue = "")
        {
            Form inputForm = new Form();
            inputForm.Text = title;
            inputForm.Size = new Size(400, 150);
            inputForm.StartPosition = FormStartPosition.CenterParent;
            inputForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            inputForm.MaximizeBox = false;
            inputForm.MinimizeBox = false;

            Label lblPrompt = new Label();
            lblPrompt.Text = prompt;
            lblPrompt.Location = new Point(10, 20);
            lblPrompt.Size = new Size(360, 20);

            TextBox txtInput = new TextBox();
            txtInput.Text = defaultValue;
            txtInput.Location = new Point(10, 45);
            txtInput.Size = new Size(360, 20);

            Button btnOK = new Button();
            btnOK.Text = "OK";
            btnOK.DialogResult = DialogResult.OK;
            btnOK.Location = new Point(215, 80);
            btnOK.Size = new Size(75, 23);

            Button btnCancel = new Button();
            btnCancel.Text = "Cancel";
            btnCancel.DialogResult = DialogResult.Cancel;
            btnCancel.Location = new Point(295, 80);
            btnCancel.Size = new Size(75, 23);

            inputForm.Controls.AddRange(new Control[] { lblPrompt, txtInput, btnOK, btnCancel });
            inputForm.AcceptButton = btnOK;
            inputForm.CancelButton = btnCancel;

            return inputForm.ShowDialog(this) == DialogResult.OK ? txtInput.Text : string.Empty;
        }

        private void ReadPartitionDialog()
        {
            try
            {
                if (!ValidateDeviceConnection())
                    return;

                string partitionName = ShowInputDialog("Enter partition name to read:", "Read Partition", "boot");
                if (string.IsNullOrEmpty(partitionName))
                    return;

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Image Files (*.img)|*.img|All Files (*.*)|*.*";
                saveDialog.FileName = $"{partitionName}.img";
                saveDialog.Title = "Save Partition Image";

                if (saveDialog.ShowDialog() != DialogResult.OK)
                    return;

                LogInfo($"Starting read operation for partition: {partitionName}");

                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("ReadPartition");

                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());
                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device. Error code: {connectResult}");
                    return;
                }

                bool readResult = flashTool.ReadPartition(partitionName, saveDialog.FileName);
                flashTool.DADisConnect(0);

                if (readResult)
                {
                    ShowSuccess($"Partition '{partitionName}' read successfully to: {saveDialog.FileName}");
                }
                else
                {
                    ShowError($"Failed to read partition '{partitionName}'");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error reading partition: {ex.Message}");
            }
        }

        private void FlashSelectedPartitionsDialog()
        {
            try
            {
                if (!ValidateDeviceConnection())
                    return;

                // Get selected partitions from the ROM list
                List<string> selectedPartitions = new List<string>();
                foreach (DataGridViewRow row in RomList_FilesFromScatter.Rows)
                {
                    if (row.Cells[0].Value != null && (bool)row.Cells[0].Value)
                    {
                        selectedPartitions.Add(row.Cells[1].Value?.ToString() ?? "");
                    }
                }

                if (selectedPartitions.Count == 0)
                {
                    ShowWarning("No partitions selected. Please select partitions to flash in the ROM list.");
                    return;
                }

                DialogResult result = MessageBox.Show(
                    $"Flash {selectedPartitions.Count} selected partitions?\nPartitions: {string.Join(", ", selectedPartitions)}",
                    "Flash Selected Partitions",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                    return;

                LogInfo($"Starting flash operation for selected partitions: [{string.Join(", ", selectedPartitions)}]");

                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName("FlashSelected");

                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());
                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device. Error code: {connectResult}");
                    return;
                }

                bool flashResult = flashTool.FlashSelectedPartitions(selectedPartitions, MTK_FlashTool.FlashMode.DOWNLOAD_ONLY);
                flashTool.DADisConnect(0);

                if (flashResult)
                {
                    ShowSuccess($"Selected partitions flashed successfully");
                }
                else
                {
                    ShowError("Failed to flash selected partitions");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error flashing selected partitions: {ex.Message}");
            }
        }

        private void ShowNVOperationsDialog()
        {
            try
            {
                Form nvForm = new Form();
                nvForm.Text = "NV Items Operations";
                nvForm.Size = new Size(400, 200);
                nvForm.StartPosition = FormStartPosition.CenterParent;

                Button btnReadNV = new Button { Text = "Read NV Item", Size = new Size(120, 30), Location = new Point(20, 20) };
                Button btnWriteNV = new Button { Text = "Write NV Item", Size = new Size(120, 30), Location = new Point(150, 20) };
                Button btnEraseNV = new Button { Text = "Erase NV Item", Size = new Size(120, 30), Location = new Point(280, 20) };

                btnReadNV.Click += (s, e) => { nvForm.Close(); ReadNVItemDialog(); };
                btnWriteNV.Click += (s, e) => { nvForm.Close(); WriteNVItemDialog(); };
                btnEraseNV.Click += (s, e) => { nvForm.Close(); EraseNVItemDialog(); };

                nvForm.Controls.AddRange(new Control[] { btnReadNV, btnWriteNV, btnEraseNV });
                nvForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowError($"Error showing NV operations: {ex.Message}");
            }
        }

        private void ShowRPMBOperationsDialog()
        {
            try
            {
                Form rpmbForm = new Form();
                rpmbForm.Text = "RPMB Operations";
                rpmbForm.Size = new Size(400, 200);
                rpmbForm.StartPosition = FormStartPosition.CenterParent;

                Button btnReadRPMB = new Button { Text = "Read RPMB", Size = new Size(120, 30), Location = new Point(20, 20) };
                Button btnWriteRPMB = new Button { Text = "Write RPMB", Size = new Size(120, 30), Location = new Point(150, 20) };
                Button btnEraseRPMB = new Button { Text = "Erase RPMB", Size = new Size(120, 30), Location = new Point(280, 20) };
                Button btnRPMBInfo = new Button { Text = "RPMB Info", Size = new Size(120, 30), Location = new Point(85, 60) };

                btnReadRPMB.Click += (s, e) => { rpmbForm.Close(); ReadRPMBDialog(); };
                btnWriteRPMB.Click += (s, e) => { rpmbForm.Close(); WriteRPMBDialog(); };
                btnEraseRPMB.Click += (s, e) => { rpmbForm.Close(); EraseRPMBDialog(); };
                btnRPMBInfo.Click += (s, e) => { rpmbForm.Close(); ShowRPMBInfo(); };

                rpmbForm.Controls.AddRange(new Control[] { btnReadRPMB, btnWriteRPMB, btnEraseRPMB, btnRPMBInfo });
                rpmbForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowError($"Error showing RPMB operations: {ex.Message}");
            }
        }

        private void ShowGPTOperationsDialog()
        {
            try
            {
                Form gptForm = new Form();
                gptForm.Text = "GPT Operations";
                gptForm.Size = new Size(300, 150);
                gptForm.StartPosition = FormStartPosition.CenterParent;

                Button btnReadGPT = new Button { Text = "Read GPT", Size = new Size(120, 30), Location = new Point(20, 20) };
                Button btnWriteGPT = new Button { Text = "Write GPT", Size = new Size(120, 30), Location = new Point(150, 20) };

                btnReadGPT.Click += (s, e) => { gptForm.Close(); ReadGPTDialog(); };
                btnWriteGPT.Click += (s, e) => { gptForm.Close(); WriteGPTDialog(); };

                gptForm.Controls.AddRange(new Control[] { btnReadGPT, btnWriteGPT });
                gptForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowError($"Error showing GPT operations: {ex.Message}");
            }
        }

        private void ShowRawFirmwareDialog()
        {
            try
            {
                Form rawForm = new Form();
                rawForm.Text = "Raw Firmware Operations";
                rawForm.Size = new Size(300, 150);
                rawForm.StartPosition = FormStartPosition.CenterParent;

                Button btnReadRaw = new Button { Text = "Read Raw", Size = new Size(120, 30), Location = new Point(20, 20) };
                Button btnWriteRaw = new Button { Text = "Write Raw", Size = new Size(120, 30), Location = new Point(150, 20) };

                btnReadRaw.Click += (s, e) => { rawForm.Close(); ReadRawFirmwareDialog(); };
                btnWriteRaw.Click += (s, e) => { rawForm.Close(); WriteRawFirmwareDialog(); };

                rawForm.Controls.AddRange(new Control[] { btnReadRaw, btnWriteRaw });
                rawForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowError($"Error showing raw firmware operations: {ex.Message}");
            }
        }

        private void ShowPreloaderOperationsDialog()
        {
            try
            {
                Form preloaderForm = new Form();
                preloaderForm.Text = "Preloader Operations";
                preloaderForm.Size = new Size(300, 150);
                preloaderForm.StartPosition = FormStartPosition.CenterParent;

                Button btnReadPreloader = new Button { Text = "Read Preloader", Size = new Size(120, 30), Location = new Point(20, 20) };
                Button btnWritePreloader = new Button { Text = "Write Preloader", Size = new Size(120, 30), Location = new Point(150, 20) };

                btnReadPreloader.Click += (s, e) => { preloaderForm.Close(); ReadPreloaderDialog(); };
                btnWritePreloader.Click += (s, e) => { preloaderForm.Close(); WritePreloaderDialog(); };

                preloaderForm.Controls.AddRange(new Control[] { btnReadPreloader, btnWritePreloader });
                preloaderForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowError($"Error showing preloader operations: {ex.Message}");
            }
        }

        private void ShowFlashModesDialog()
        {
            try
            {
                Form flashForm = new Form();
                flashForm.Text = "Flash Modes";
                flashForm.Size = new Size(400, 200);
                flashForm.StartPosition = FormStartPosition.CenterParent;

                Button btnDownloadOnly = new Button { Text = "Download Only", Size = new Size(120, 30), Location = new Point(20, 20) };
                Button btnFormatAll = new Button { Text = "Format All", Size = new Size(120, 30), Location = new Point(150, 20) };
                Button btnFirmwareUpgrade = new Button { Text = "Firmware Upgrade", Size = new Size(120, 30), Location = new Point(280, 20) };
                Button btnScatterFlash = new Button { Text = "Flash Scatter", Size = new Size(120, 30), Location = new Point(85, 60) };

                btnDownloadOnly.Click += (s, e) => { flashForm.Close(); FlashWithMode(MTK_FlashTool.FlashMode.DOWNLOAD_ONLY); };
                btnFormatAll.Click += (s, e) => { flashForm.Close(); FlashWithMode(MTK_FlashTool.FlashMode.FORMAT_ALL_DOWNLOAD); };
                btnFirmwareUpgrade.Click += (s, e) => { flashForm.Close(); FlashWithMode(MTK_FlashTool.FlashMode.FIRMWARE_UPGRADE); };
                btnScatterFlash.Click += (s, e) => { flashForm.Close(); FlashScatterDialog(); };

                flashForm.Controls.AddRange(new Control[] { btnDownloadOnly, btnFormatAll, btnFirmwareUpgrade, btnScatterFlash });
                flashForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                ShowError($"Error showing flash modes: {ex.Message}");
            }
        }

        #endregion

        #region Operation Implementations

        private void ReadNVItemDialog()
        {
            try
            {
                string itemIdStr = ShowInputDialog("Enter NV Item ID:", "Read NV Item", "1");
                if (string.IsNullOrEmpty(itemIdStr) || !uint.TryParse(itemIdStr, out uint itemId))
                    return;

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                saveDialog.FileName = $"nvitem_{itemId}.bin";
                if (saveDialog.ShowDialog() != DialogResult.OK)
                    return;

                ExecuteFlashToolOperation("ReadNVItem", (flashTool) =>
                    flashTool.ReadNVItem(itemId, saveDialog.FileName),
                    $"NV Item {itemId} read successfully",
                    $"Failed to read NV Item {itemId}");
            }
            catch (Exception ex)
            {
                ShowError($"Error reading NV item: {ex.Message}");
            }
        }

        private void WriteNVItemDialog()
        {
            try
            {
                string itemIdStr = ShowInputDialog("Enter NV Item ID:", "Write NV Item", "1");
                if (string.IsNullOrEmpty(itemIdStr) || !uint.TryParse(itemIdStr, out uint itemId))
                    return;

                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                openDialog.Title = "Select NV Item File";
                if (openDialog.ShowDialog() != DialogResult.OK)
                    return;

                ExecuteFlashToolOperation("WriteNVItem", (flashTool) =>
                    flashTool.WriteNVItem(itemId, openDialog.FileName),
                    $"NV Item {itemId} written successfully",
                    $"Failed to write NV Item {itemId}");
            }
            catch (Exception ex)
            {
                ShowError($"Error writing NV item: {ex.Message}");
            }
        }

        private void EraseNVItemDialog()
        {
            try
            {
                string itemIdStr = ShowInputDialog("Enter NV Item ID:", "Erase NV Item", "1");
                if (string.IsNullOrEmpty(itemIdStr) || !uint.TryParse(itemIdStr, out uint itemId))
                    return;

                DialogResult result = MessageBox.Show(
                    $"Are you sure you want to erase NV Item {itemId}?",
                    "Confirm Erase",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning
                );

                if (result != DialogResult.Yes)
                    return;

                ExecuteFlashToolOperation("EraseNVItem", (flashTool) =>
                    flashTool.EraseNVItem(itemId),
                    $"NV Item {itemId} erased successfully",
                    $"Failed to erase NV Item {itemId}");
            }
            catch (Exception ex)
            {
                ShowError($"Error erasing NV item: {ex.Message}");
            }
        }

        private void ReadRPMBDialog()
        {
            try
            {
                string blockAddrStr = ShowInputDialog("Enter starting block address:", "Read RPMB", "0");
                if (string.IsNullOrEmpty(blockAddrStr) || !uint.TryParse(blockAddrStr, out uint blockAddr))
                    return;

                string blockCountStr = ShowInputDialog("Enter number of blocks:", "Read RPMB", "1");
                if (string.IsNullOrEmpty(blockCountStr) || !uint.TryParse(blockCountStr, out uint blockCount))
                    return;

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                saveDialog.FileName = $"rpmb_{blockAddr}_{blockCount}.bin";
                if (saveDialog.ShowDialog() != DialogResult.OK)
                    return;

                ExecuteFlashToolOperation("ReadRPMB", (flashTool) =>
                    flashTool.ReadRPMB(blockAddr, blockCount, saveDialog.FileName),
                    $"RPMB blocks {blockAddr}-{blockAddr + blockCount - 1} read successfully",
                    "Failed to read RPMB");
            }
            catch (Exception ex)
            {
                ShowError($"Error reading RPMB: {ex.Message}");
            }
        }

        private void WriteRPMBDialog()
        {
            try
            {
                string blockAddrStr = ShowInputDialog("Enter starting block address:", "Write RPMB", "0");
                if (string.IsNullOrEmpty(blockAddrStr) || !uint.TryParse(blockAddrStr, out uint blockAddr))
                    return;

                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                openDialog.Title = "Select RPMB File";
                if (openDialog.ShowDialog() != DialogResult.OK)
                    return;

                ExecuteFlashToolOperation("WriteRPMB", (flashTool) =>
                    flashTool.WriteRPMB(blockAddr, openDialog.FileName),
                    $"RPMB written successfully from block {blockAddr}",
                    "Failed to write RPMB");
            }
            catch (Exception ex)
            {
                ShowError($"Error writing RPMB: {ex.Message}");
            }
        }

        private void EraseRPMBDialog()
        {
            try
            {
                string blockAddrStr = ShowInputDialog("Enter starting block address:", "Erase RPMB", "0");
                if (string.IsNullOrEmpty(blockAddrStr) || !uint.TryParse(blockAddrStr, out uint blockAddr))
                    return;

                string blockCountStr = ShowInputDialog("Enter number of blocks:", "Erase RPMB", "1");
                if (string.IsNullOrEmpty(blockCountStr) || !uint.TryParse(blockCountStr, out uint blockCount))
                    return;

                DialogResult result = MessageBox.Show(
                    $"Are you sure you want to erase RPMB blocks {blockAddr}-{blockAddr + blockCount - 1}?",
                    "Confirm Erase",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning
                );

                if (result != DialogResult.Yes)
                    return;

                ExecuteFlashToolOperation("EraseRPMB", (flashTool) =>
                    flashTool.EraseRPMB(blockAddr, blockCount),
                    $"RPMB blocks {blockAddr}-{blockAddr + blockCount - 1} erased successfully",
                    "Failed to erase RPMB");
            }
            catch (Exception ex)
            {
                ShowError($"Error erasing RPMB: {ex.Message}");
            }
        }

        private void ShowRPMBInfo()
        {
            try
            {
                ExecuteFlashToolOperation("GetRPMBInfo", (flashTool) =>
                {
                    var rpmbInfo = flashTool.GetRPMBInfo();
                    if (rpmbInfo.HasValue)
                    {
                        string info = $"RPMB Information:\n" +
                                     $"Block Count: {rpmbInfo.Value.block_count}\n" +
                                     $"Block Size: {rpmbInfo.Value.block_size} bytes\n" +
                                     $"Total Size: {rpmbInfo.Value.total_size} bytes\n" +
                                     $"Configured: {rpmbInfo.Value.is_configured}";

                        MessageBox.Show(info, "RPMB Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return true;
                    }
                    return false;
                },
                "RPMB information retrieved successfully",
                "Failed to get RPMB information");
            }
            catch (Exception ex)
            {
                ShowError($"Error getting RPMB info: {ex.Message}");
            }
        }

        private void ReadGPTDialog()
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                saveDialog.FileName = "gpt.bin";
                if (saveDialog.ShowDialog() != DialogResult.OK)
                    return;

                ExecuteFlashToolOperation("ReadGPT", (flashTool) =>
                    flashTool.ReadGPT(saveDialog.FileName),
                    "GPT read successfully",
                    "Failed to read GPT");
            }
            catch (Exception ex)
            {
                ShowError($"Error reading GPT: {ex.Message}");
            }
        }

        private void WriteGPTDialog()
        {
            try
            {
                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                openDialog.Title = "Select GPT File";
                if (openDialog.ShowDialog() != DialogResult.OK)
                    return;

                DialogResult result = MessageBox.Show(
                    "Writing GPT can brick your device if the file is incorrect.\nContinue?",
                    "Warning",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning
                );

                if (result != DialogResult.Yes)
                    return;

                ExecuteFlashToolOperation("WriteGPT", (flashTool) =>
                    flashTool.WriteGPT(openDialog.FileName),
                    "GPT written successfully",
                    "Failed to write GPT");
            }
            catch (Exception ex)
            {
                ShowError($"Error writing GPT: {ex.Message}");
            }
        }

        private void ReadRawFirmwareDialog()
        {
            try
            {
                string startAddrStr = ShowInputDialog("Enter start address (hex):", "Read Raw Firmware", "0x0");
                if (string.IsNullOrEmpty(startAddrStr))
                    return;

                if (!TryParseHex(startAddrStr, out ulong startAddr))
                {
                    ShowError("Invalid start address format");
                    return;
                }

                string sizeStr = ShowInputDialog("Enter size (hex):", "Read Raw Firmware", "0x100000");
                if (string.IsNullOrEmpty(sizeStr))
                    return;

                if (!TryParseHex(sizeStr, out ulong size))
                {
                    ShowError("Invalid size format");
                    return;
                }

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                saveDialog.FileName = $"raw_0x{startAddr:X8}_0x{size:X8}.bin";
                if (saveDialog.ShowDialog() != DialogResult.OK)
                    return;

                ExecuteFlashToolOperation("ReadRawFirmware", (flashTool) =>
                    flashTool.ReadRawFirmware(startAddr, size, saveDialog.FileName),
                    "Raw firmware read successfully",
                    "Failed to read raw firmware");
            }
            catch (Exception ex)
            {
                ShowError($"Error reading raw firmware: {ex.Message}");
            }
        }

        private void WriteRawFirmwareDialog()
        {
            try
            {
                string startAddrStr = ShowInputDialog("Enter start address (hex):", "Write Raw Firmware", "0x0");
                if (string.IsNullOrEmpty(startAddrStr))
                    return;

                if (!TryParseHex(startAddrStr, out ulong startAddr))
                {
                    ShowError("Invalid start address format");
                    return;
                }

                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                openDialog.Title = "Select Raw Firmware File";
                if (openDialog.ShowDialog() != DialogResult.OK)
                    return;

                DialogResult verifyResult = MessageBox.Show(
                    "Verify after write?",
                    "Verification",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                bool verify = verifyResult == DialogResult.Yes;

                ExecuteFlashToolOperation("WriteRawFirmware", (flashTool) =>
                    flashTool.WriteRawFirmware(startAddr, openDialog.FileName, verify),
                    "Raw firmware written successfully",
                    "Failed to write raw firmware");
            }
            catch (Exception ex)
            {
                ShowError($"Error writing raw firmware: {ex.Message}");
            }
        }

        private void ReadPreloaderDialog()
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                saveDialog.FileName = "preloader.bin";
                if (saveDialog.ShowDialog() != DialogResult.OK)
                    return;

                ExecuteFlashToolOperation("ReadPreloader", (flashTool) =>
                    flashTool.ReadPreloader(saveDialog.FileName),
                    "Preloader read successfully",
                    "Failed to read preloader");
            }
            catch (Exception ex)
            {
                ShowError($"Error reading preloader: {ex.Message}");
            }
        }

        private void WritePreloaderDialog()
        {
            try
            {
                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                openDialog.Title = "Select Preloader File";
                if (openDialog.ShowDialog() != DialogResult.OK)
                    return;

                DialogResult result = MessageBox.Show(
                    "Writing preloader can brick your device if the file is incorrect.\nContinue?",
                    "Warning",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning
                );

                if (result != DialogResult.Yes)
                    return;

                DialogResult verifyResult = MessageBox.Show(
                    "Verify after write?",
                    "Verification",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                bool verify = verifyResult == DialogResult.Yes;

                ExecuteFlashToolOperation("WritePreloader", (flashTool) =>
                    flashTool.WritePreloader(openDialog.FileName, verify),
                    "Preloader written successfully",
                    "Failed to write preloader");
            }
            catch (Exception ex)
            {
                ShowError($"Error writing preloader: {ex.Message}");
            }
        }

        private void FlashWithMode(MTK_FlashTool.FlashMode mode)
        {
            try
            {
                if (!ValidateAllFiles())
                    return;

                DialogResult result = MessageBox.Show(
                    $"Flash device using {mode} mode?",
                    "Confirm Flash",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                    return;

                // Use existing download functionality but with specified mode
                LogInfo($"Starting flash operation with mode: {mode}");
                // This would integrate with the existing DownloadFiles method
                DownloadFiles();
            }
            catch (Exception ex)
            {
                ShowError($"Error flashing with mode {mode}: {ex.Message}");
            }
        }

        private void FlashScatterDialog()
        {
            try
            {
                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "Scatter Files (*.txt)|*.txt|All Files (*.*)|*.*";
                openDialog.Title = "Select Scatter File";
                if (openDialog.ShowDialog() != DialogResult.OK)
                    return;

                DialogResult result = MessageBox.Show(
                    "Flash firmware using scatter format?",
                    "Confirm Flash",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                    return;

                ExecuteFlashToolOperation("FlashScatterFirmware", (flashTool) =>
                    flashTool.FlashScatterFirmware(openDialog.FileName, MTK_FlashTool.FlashMode.DOWNLOAD_ONLY),
                    "Scatter firmware flashed successfully",
                    "Failed to flash scatter firmware");
            }
            catch (Exception ex)
            {
                ShowError($"Error flashing scatter firmware: {ex.Message}");
            }
        }

        private bool TryParseHex(string hexString, out ulong value)
        {
            value = 0;
            try
            {
                if (hexString.StartsWith("0x") || hexString.StartsWith("0X"))
                    hexString = hexString.Substring(2);

                value = Convert.ToUInt64(hexString, 16);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private void ExecuteFlashToolOperation(string operationName, Func<MTK_FlashTool, bool> operation, string successMessage, string errorMessage)
        {
            try
            {
                if (!ValidateDeviceConnection())
                    return;

                LogInfo($"Starting {operationName} operation");

                MTK_FlashTool flashTool = new MTK_FlashTool();
                flashTool.SetLogName(operationName);

                int connectResult = flashTool.DAConnect_new(0, (short)GetAvailableComPort());
                if (connectResult != 0)
                {
                    ShowError($"Failed to connect to device. Error code: {connectResult}");
                    return;
                }

                bool result = operation(flashTool);
                flashTool.DADisConnect(0);

                if (result)
                {
                    ShowSuccess(successMessage);
                }
                else
                {
                    ShowError(errorMessage);
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error in {operationName}: {ex.Message}");
            }
        }

        #endregion

        #region UI Helper Methods

        /// <summary>
        /// Update UI state based on current application state
        /// </summary>
        private void UpdateUIState()
        {
            try
            {
                // Update button states
                bool hasScatter = !string.IsNullOrEmpty(txtScatter.Text);
                bool hasDA = !string.IsNullOrEmpty(txtDa.Text) && MTK_DA.IsDALoaded;

                // Basic operations
                button4.Enabled = hasScatter && hasDA; // Download button
                btnReadPartitions.Enabled = hasDA; // Read Partitions button
                btnRebootMeta.Enabled = hasDA; // Reboot to Meta button

                // Advanced operations - require DA
                btnAdvancedOps.Enabled = hasDA;
                btnErasePartition.Enabled = hasDA;
                btnUnlockBootloader.Enabled = hasDA;
                btnRelockBootloader.Enabled = hasDA;
                btnNVOperations.Enabled = hasDA;
                btnRPMBOperations.Enabled = hasDA;
                btnGPTOperations.Enabled = hasDA;
                btnRawFirmware.Enabled = hasDA;
                btnPreloaderOps.Enabled = hasDA;
                btnFlashModes.Enabled = hasScatter && hasDA;

                // Update status indicators
                UpdateStatusIndicators();
            }
            catch (Exception ex)
            {
                LogError($"Error updating UI state: {ex.Message}");
            }
        }

        /// <summary>
        /// Update visual status indicators
        /// </summary>
        private void UpdateStatusIndicators()
        {
            // Change text color based on file status
            txtScatter.BackColor = !string.IsNullOrEmpty(txtScatter.Text) && File.Exists(txtScatter.Text)
                ? Color.LightGreen : Color.White;

            txtDa.BackColor = MTK_DA.IsDALoaded
                ? Color.LightGreen : Color.White;
        }

        /// <summary>
        /// Show error message to user
        /// </summary>
        /// <param name="message">Error message</param>
        private void ShowError(string message)
        {
            MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            LogError(message);
        }

        /// <summary>
        /// Show success message to user
        /// </summary>
        /// <param name="message">Success message</param>
        private void ShowSuccess(string message)
        {
            MessageBox.Show(message, "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            LogInfo(message);
        }

        /// <summary>
        /// Show warning message to user
        /// </summary>
        /// <param name="message">Warning message</param>
        private void ShowWarning(string message)
        {
            MessageBox.Show(message, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            LogWarning(message);
        }

        #endregion

        #region Logging Methods

        /// <summary>
        /// Log information message
        /// </summary>
        /// <param name="message">Message to log</param>
        private void LogInfo(string message)
        {
            string logMessage = $"[FORM INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            Console.WriteLine(logMessage);
            WriteToLogFile(logMessage);
        }

        /// <summary>
        /// Log warning message
        /// </summary>
        /// <param name="message">Message to log</param>
        private void LogWarning(string message)
        {
            string logMessage = $"[FORM WARNING] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            Console.WriteLine(logMessage);
            WriteToLogFile(logMessage);
        }

        /// <summary>
        /// Log error message
        /// </summary>
        /// <param name="message">Message to log</param>
        private void LogError(string message)
        {
            string logMessage = $"[FORM ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            Console.WriteLine(logMessage);
            WriteToLogFile(logMessage);
        }

        /// <summary>
        /// Write message to log file
        /// </summary>
        /// <param name="message">Message to write</param>
        private void WriteToLogFile(string message)
        {
            try
            {
                string logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "log");
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                string logFile = Path.Combine(logDir, $"SharpMTK_{DateTime.Now:yyyyMMdd}.log");
                File.AppendAllText(logFile, message + Environment.NewLine);
            }
            catch
            {
                // Ignore logging errors to prevent cascading failures
            }
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// Validate scatter file
        /// </summary>
        /// <param name="filePath">Path to scatter file</param>
        /// <returns>True if valid</returns>
        private bool ValidateScatterFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                string extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (extension != ".txt")
                {
                    return false;
                }

                // Check if file contains scatter-like content
                string content = File.ReadAllText(filePath);
                return content.Contains("partition_index") || content.Contains("begin_addr") || content.Contains("end_addr");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validate all required files before starting flash operation
        /// </summary>
        /// <returns>True if all files are valid</returns>
        private bool ValidateAllFiles()
        {
            // Check scatter file
            if (string.IsNullOrEmpty(txtScatter.Text))
            {
                ShowError("Please select a Scatter file");
                return false;
            }

            if (!ValidateScatterFile(txtScatter.Text))
            {
                ShowError("Selected Scatter file is not valid or does not exist");
                return false;
            }

            // Check DA file
            if (string.IsNullOrEmpty(txtDa.Text))
            {
                ShowError("Please select a Download Agent file");
                return false;
            }

            if (!MTK_DA.IsDALoaded)
            {
                ShowError("Download Agent is not properly loaded");
                return false;
            }

            return true;
        }

        #endregion

        /// <summary>
        /// Handle form closing to cleanup resources
        /// </summary>
        /// <param name="e">Form closing event args</param>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // Cleanup MTK resources
                MTK_DA.DAHandleDestroy();
                MTK_DL.DLHandleDestroy();

                // Wait for threads to complete
                foreach (Thread thread in threads)
                {
                    if (thread.IsAlive)
                    {
                        thread.Join(5000); // Wait up to 5 seconds
                    }
                }

                LogInfo("Application closing - resources cleaned up");
            }
            catch (Exception ex)
            {
                LogError($"Error during cleanup: {ex.Message}");
            }

            base.OnFormClosing(e);
        }
    }
}

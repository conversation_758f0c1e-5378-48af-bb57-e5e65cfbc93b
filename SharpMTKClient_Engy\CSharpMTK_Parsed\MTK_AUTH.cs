﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed
{
    public class MTK_AUTH
    {
        public static IntPtr g_auth_handle;

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int AUTH_Create(ref IntPtr p_auth_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int AUTH_Unload(IntPtr p_auth_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int AUTH_Destroy(ref IntPtr p_auth_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int AUTH_Load(IntPtr p_auth_handle, byte[] auth_filepath);

        public static void AUTHHandle()
        {
            int num = -1;
            num = AUTH_Create(ref g_auth_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("AUTH_Create err" + str);
            }
            Console.WriteLine("AUTH_Create i" + num);
        }

        public static void AUTHHandleDestroy()
        {
            int num = -1;
            num = AUTH_Unload(g_auth_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("AUTH_Unload err" + str);
            }
            Console.WriteLine("AUTH_Unload i" + num);
            num = AUTH_Destroy(ref g_auth_handle);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("AUTH_Destroy err" + str2);
            }
            Console.WriteLine("AUTH_Destroy i" + num);
        }

        public static void AUTHLOAD(string auth_file)
        {
            int num = -1;
            num = AUTH_Unload(g_auth_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("AUTH_Unload err" + str);
            }
            Console.WriteLine("AUTH_Unload i" + num);
            num = AUTH_Load(g_auth_handle, Encoding.Default.GetBytes(auth_file));
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("AUTH_Load err" + str2);
            }
            Console.WriteLine("AUTH_Load i" + num);
        }
    }
}

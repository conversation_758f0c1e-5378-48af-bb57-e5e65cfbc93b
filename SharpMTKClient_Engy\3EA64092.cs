﻿using System.Diagnostics;
using System.Reflection;

namespace SharpMTKClient_Engy
{
    [DebuggerDisplay("{2+B887F816.FD14CCA2()}")]
    public class _3EA64092
    {
        public _3EA64092()
        {
            ushort num = 38565;
            num = 18944;
            while (true)
            {
                num = (ushort)(-1176190719 + ((1176229285 >>> (int)num) - ((1594522645 > num << 22) ? 1 : 0)));
                while (true)
                {
                IL_00bc:
                    _9FBB7E1B = new CC203E2B();
                    base._002Ector();
                    EBBF1580 = MethodBase.GetCurrentMethod();
                    short num2 = (byte)(454679720 >> (-1113778024 / num >> num / -1893411649));
                    while (true)
                    {
                        switch ((uint)num % 4u)
                        {
                            case 1u:
                                if (D6867C16.Count == 0)
                                {
                                    num = (ushort)((uint)((num + 1536088228) % (sbyte)num) / (uint)num);
                                    num = (ushort)(num % ~((-1036764620 & num2) / (num2 % -1591108696)) - -12863);
                                    continue;
                                }
                                goto IL_003e;
                            case 2u:
                                num = (ushort)(-(((num2 << (int)num2) % -1574319834) & num2) - -38565);
                                return;
                            case 3u:
                                num = ((65058 < num2 >>> (num2 ^ 0x1BB62C80) * (num2 / num)) ? ((ushort)1) : ((ushort)0));
                                E01317B9 = new _99149BB4[((uint)(-637644180 / (-442474874 ^ (num * 1866458129))) / ~((2032159913 >> (int)num2 >> 9 > ((num2 > -1641653580) ? 1 : 0)) ? 1u : 0u)) ^ 0x100];
                                return;
                        }
                        break;
                    IL_003e:
                        if (((num2 / ~(num / 539001256) >>> (int)num) ^ num2) != 0)
                        {
                            num = (ushort)((uint)(num2 / 128124215 % (num2 * 1040619958)) / (uint)(~(byte)(-611016808 % ~(num >>> (int)num2))) - 4294908510u);
                            continue;
                        }
                        goto IL_00bc;
                    }
                    break;
                }
            }
        }


    }
}

﻿using SharpMTKClient_Engy.CSharpMTK_Parsed.Module;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Utility;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed.Data
{
    internal class SaveFfu
    {
        public static List<Ffu> ffuList = new List<Ffu>();

        public static List<Wb> wbList = new List<Wb>();

        public static bool isNeedWBprovision = false;

        public static double VidToInt(string strVid)
        {
            if (strVid.Substring(0, 1) == "P")
            {
                return double.Parse(strVid.Substring(1, strVid.Length - 1));
            }
            if (strVid == "MP")
            {
                return 100.0;
            }
            return -1.0;
        }

        public static void GetFfu(string dlPath)
        {
            ffuList.Clear();
            string text = "";
            string text2 = dlPath + "\\MI_FFU\\ffu_list.txt";
            if (File.Exists(text2))
            {
                Console.WriteLine("dl has ffufilelist in ffuFile:" + text2);
                FlashGlobal.IsFirmwarewrite = true;
                string directoryName = Path.GetDirectoryName(text2);
                Console.WriteLine("ffuFilePath: " + directoryName);
                MiAppConfig.SetValue("ffuPath", directoryName.Trim());
                StreamReader streamReader = new StreamReader(text2, Encoding.Default);
                while ((text = streamReader.ReadLine()) != null)
                {
                    if (text.ToLower().IndexOf("version") < 0)
                    {
                        if ("".Equals(text))
                        {
                            break;
                        }
                        string[] array = text.Split(' ');
                        ffuList.Add(new Ffu
                        {
                            Name = array[0],
                            Version = array[1],
                            Number = array[2],
                            File = array[3],
                            Size = array[4]
                        });
                    }
                }
            }
            else
            {
                Console.WriteLine("dl not exit ffufilelist");
            }
        }

        public static string FileReadByLine(string filePath)
        {
            return new StreamReader(filePath, Encoding.Default).ReadLine().ToString();
        }

        public static void GetWb(string swPath)
        {
            wbList.Clear();
            string text = swPath + "\\provision.xml";
            if (File.Exists(text))
            {
                string vendor = "";
                string partnumber = "";
                string wb_size_in_MB = "";
                string hpb_total_range_in_MB = "";
                XmlDocument xmlDocument = new XmlDocument();
                xmlDocument.Load(text);
                XmlNodeList childNodes = xmlDocument.SelectSingleNode("data").ChildNodes;
                XmlElement xmlElement = null;
                {
                    foreach (XmlNode item in childNodes)
                    {
                        if (item.Name.ToLower() == "start")
                        {
                            foreach (XmlAttribute attribute in item.Attributes)
                            {
                                string text2 = attribute.Name.ToLower();
                                if (text2 == "strictly_ctrl")
                                {
                                    if (attribute.Value != "1")
                                    {
                                        return;
                                    }
                                    isNeedWBprovision = true;
                                }
                            }
                        }
                        if (!(item.Name.ToLower() == "ufs"))
                        {
                            continue;
                        }
                        foreach (XmlAttribute attribute2 in item.Attributes)
                        {
                            switch (attribute2.Name.ToLower())
                            {
                                case "vendor":
                                    vendor = attribute2.Value;
                                    break;
                                case "partnumber":
                                    partnumber = attribute2.Value;
                                    break;
                                case "density_in_gb":
                                    _ = attribute2.Value;
                                    break;
                                case "wb_size_in_mb":
                                    wb_size_in_MB = attribute2.Value;
                                    break;
                                case "hpb_total_range_in_mb":
                                    hpb_total_range_in_MB = attribute2.Value;
                                    break;
                            }
                        }
                        wbList.Add(new Wb
                        {
                            Vendor = vendor,
                            Partnumber = partnumber,
                            Wb_size_in_MB = wb_size_in_MB,
                            Hpb_total_range_in_MB = hpb_total_range_in_MB
                        });
                    }
                    return;
                }
            }
            Console.WriteLine("WbFile " + text + " not exist");
        }
    }
}

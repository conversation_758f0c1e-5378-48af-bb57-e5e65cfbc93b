﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed.Module
{
    public class MiProperty
    {
        private static Dictionary<string, int> _chksumtable;

        public static Dictionary<string, int> ChkSumTable
        {
            get
            {
                _chksumtable = new Dictionary<string, int>();
                _chksumtable.Add("None", 0);
                _chksumtable.Add("Usb+dram checksum", 1);
                _chksumtable.Add("Flash checksum", 2);
                _chksumtable.Add("All checksum", 3);
                return _chksumtable;
            }
        }

    }
}

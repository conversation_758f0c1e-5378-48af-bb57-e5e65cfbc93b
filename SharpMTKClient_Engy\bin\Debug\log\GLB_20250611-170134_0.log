[00000001] [17:01:34:603564] [Tid0x000107f8] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [17:01:34:604564] [Tid0x000107f8] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [17:01:34:604564] [Tid0x000107f8] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [17:01:34:604564] [Tid0x000107f8] [info] create new hsession 0xae41180 #(kernel.cpp, line:92)
[00000005] [17:01:34:604564] [Tid0x000107f8] [debug] <--[C3] kernel::create_new_session
[00000006] [17:01:34:604564] [Tid0x000107f8] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [17:01:34:604564] [Tid0x000107f8] [debug] <--[C4] boot_rom::boot_rom
[00000008] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C6] device_log_source::device_log_source
[00000011] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C7] data_mux::data_mux
[00000013] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C5] device_instance::device_instance
[00000014] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C2] connection::create_session
[00000015] [17:01:34:611491] [Tid0x000107f8] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [17:01:34:611491] [Tid0x000107f8] [info] -->[C8] flashtool_connect_brom_with_handle #(undocument_api.cpp, line:100)
[00000017] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C9] connection::connect_brom_with_handle #(connection.cpp, line:146)
[00000018] [17:01:34:611491] [Tid0x000107f8] [info] (1/2)connecting brom. #(connection.cpp, line:149)
[00000019] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C11] is_valid_ip
[00000022] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C12] is_lge_impl
[00000024] [17:01:34:611491] [Tid0x000107f8] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [17:01:34:611491] [Tid0x000107f8] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C13] lib_config_parser::get_value
[00000027] [17:01:34:611491] [Tid0x000107f8] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [17:01:34:611491] [Tid0x000107f8] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [17:01:34:611491] [Tid0x000107f8] [info] try to open device: COM5 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [17:01:34:612713] [Tid0x000107f8] [info] COM5 open complete. #(comm_engine.cpp, line:168)
[00000031] [17:01:34:612713] [Tid0x000107f8] [info] <--[C14] comm_engine::open
[00000032] [17:01:34:612713] [Tid0x000107f8] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000033] [17:01:34:613713] [Tid0x000107f8] [debug] <--[C15] boot_rom::set_transfer_channel
[00000034] [17:01:34:613713] [Tid0x000107f8] [info] (2/2)security verify tool and DA. #(connection.cpp, line:188)
[00000035] [17:01:34:613713] [Tid0x000107f8] [debug] -->[C16] boot_rom_logic::security_verify_connection #(boot_rom_logic.cpp, line:41)
[00000036] [17:01:34:613713] [Tid0x000107f8] [debug] -->[C17] boot_rom::get_preloader_version #(boot_rom.cpp, line:888)
[00000037] [17:01:34:613713] [Tid0x000107f8] [debug] 			Tx->: 0x00000001 Hex[fe ]
[00000038] [17:01:34:613713] [Tid0x000107f8] [debug] 			<-Rx: 0x00000001 Hex[03 ]
[00000039] [17:01:34:613713] [Tid0x000107f8] [info] preloader version: 0x3 #(boot_rom.cpp, line:905)
[00000040] [17:01:34:613713] [Tid0x000107f8] [debug] <--[C17] boot_rom::get_preloader_version
[00000041] [17:01:34:613713] [Tid0x000107f8] [info] Preloader exist. skip connection verification. #(boot_rom_logic.cpp, line:49)
[00000042] [17:01:34:613713] [Tid0x000107f8] [debug] <--[C16] boot_rom_logic::security_verify_connection
[00000043] [17:01:34:613713] [Tid0x000107f8] [debug] <--[C9] connection::connect_brom_with_handle
[00000044] [17:01:34:613713] [Tid0x000107f8] [info] <--[C8] flashtool_connect_brom_with_handle
[00000045] [17:01:34:613713] [Tid0x000107f8] [info] -->[C20] flashtool_device_control #(flashtoolex_api.cpp, line:303)
[00000046] [17:01:34:613713] [Tid0x000107f8] [debug] -->[C21] connection::device_control #(connection.cpp, line:647)
[00000047] [17:01:34:613713] [Tid0x000107f8] [info] device control: DEV_GET_CHIP_ID code[0x1] #(connection.cpp, line:654)
[00000048] [17:01:34:613713] [Tid0x000107f8] [debug] -->[C22] boot_rom::device_control #(boot_rom.cpp, line:740)
[00000049] [17:01:34:613713] [Tid0x000107f8] [debug] -->[C23] boot_rom::get_chip_id #(boot_rom.cpp, line:113)
[00000050] [17:01:34:613713] [Tid0x000107f8] [info] get chip id  #(boot_rom.cpp, line:114)
[00000051] [17:01:34:613713] [Tid0x000107f8] [debug] 			Tx->: 0x00000001 Hex[fd ]
[00000052] [17:01:34:613713] [Tid0x000107f8] [debug] 			<-Rx: 0x00000001 Hex[fd ]
[00000053] [17:01:34:613713] [Tid0x000107f8] [debug] 			<-Rx: 0x00000002 Hex[07 66 ]
[00000054] [17:01:34:613713] [Tid0x000107f8] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000055] [17:01:34:613713] [Tid0x000107f8] [debug] 			Tx->: 0x00000001 Hex[fc ]
[00000056] [17:01:34:613713] [Tid0x000107f8] [debug] 			<-Rx: 0x00000001 Hex[fc ]
[00000057] [17:01:34:613713] [Tid0x000107f8] [debug] 			<-Rx: 0x00000002 Hex[8a 00 ]
[00000058] [17:01:34:613713] [Tid0x000107f8] [debug] 			<-Rx: 0x00000002 Hex[ca 00 ]
[00000059] [17:01:34:614714] [Tid0x000107f8] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000060] [17:01:34:614714] [Tid0x000107f8] [debug] 			<-Rx: 0x00000002 Hex[00 00 ]
[00000061] [17:01:34:614714] [Tid0x000107f8] [debug] -->[C34] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000062] [17:01:34:614714] [Tid0x000107f8] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\chip.mapping.cfg.xml #(lib_config_parser.cpp, line:29)
[00000063] [17:01:34:614714] [Tid0x000107f8] [debug] <--[C34] lib_config_parser::get_value
[00000064] [17:01:34:614714] [Tid0x000107f8] [info] Chip hw code revised from 0x766 to 0x6765. #(chip_mapping.cpp, line:208)
[00000065] [17:01:34:614714] [Tid0x000107f8] [info] chip id: hw_code[0x6765] hw_sub_code[0x8A00] hw_version[0xCA00] sw_version[0x0] #(boot_rom.cpp, line:190)
[00000066] [17:01:34:614714] [Tid0x000107f8] [debug] <--[C23] boot_rom::get_chip_id
[00000067] [17:01:34:614714] [Tid0x000107f8] [debug] <--[C22] boot_rom::device_control
[00000068] [17:01:34:614714] [Tid0x000107f8] [debug] <--[C21] connection::device_control
[00000069] [17:01:34:614714] [Tid0x000107f8] [info] <--[C20] flashtool_device_control
[00000070] [17:01:34:614714] [Tid0x000107f8] [debug] -->[C35] flashtool_get_com_handle #(undocument_api.cpp, line:126)
[00000071] [17:01:34:614714] [Tid0x000107f8] [debug] <--[C35] flashtool_get_com_handle
[00000072] [17:01:34:614714] [Tid0x000107f8] [info] -->[C36] flashtool_connect_da #(flashtoolex_api.cpp, line:134)
[00000073] [17:01:34:614714] [Tid0x000107f8] [debug] -->[C37] connection::connect_da #(connection.cpp, line:250)
[00000074] [17:01:34:614714] [Tid0x000107f8] [info] (1/7)connecting DA. #(connection.cpp, line:253)
[00000075] [17:01:34:614714] [Tid0x000107f8] [info] (2/7)read DA config. #(connection.cpp, line:254)
[00000076] [17:01:34:614714] [Tid0x000107f8] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\device.cfg.xml #(da_config_parser.cpp, line:34)
[00000077] [17:01:34:615714] [Tid0x000107f8] [info] (3/7)read DA image file. #(connection.cpp, line:262)
[00000078] [17:01:34:615714] [Tid0x000107f8] [debug] -->[C38] da_image::load #(da_image.cpp, line:24)
[00000079] [17:01:34:615714] [Tid0x000107f8] [error] file name is empty. #(file_data_factory.cpp, line:18)
[00000080] [17:01:34:615714] [Tid0x000107f8] [debug] <--[C38] da_image::load
[00000081] [17:01:34:615714] [Tid0x000107f8] [debug] <--[C37] connection::connect_da
[00000082] [17:01:34:615714] [Tid0x000107f8] [error] <ERR_CHECKPOINT>[810][error][0xc0030002]</ERR_CHECKPOINT>flashtool_connect_da fail #(flashtoolex_api.cpp, line:141)
[00000083] [17:01:34:615714] [Tid0x000107f8] [info] <--[C36] flashtool_connect_da
[00000084] [17:01:34:615714] [Tid0x000107f8] [info] -->[C39] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000085] [17:01:34:615714] [Tid0x000107f8] [debug] -->[C41] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000086] [17:01:34:615714] [Tid0x000107f8] [info] -->[C42] device_log_source::stop #(device_log_source.cpp, line:29)
[00000087] [17:01:34:615714] [Tid0x000107f8] [info] <--[C42] device_log_source::stop
[00000088] [17:01:34:615714] [Tid0x000107f8] [info] -->[C43] data_mux::stop #(data_mux.cpp, line:92)
[00000089] [17:01:34:615714] [Tid0x000107f8] [info] <--[C43] data_mux::stop
[00000090] [17:01:34:615714] [Tid0x000107f8] [debug] <--[C41] device_instance::~device_instance
[00000091] [17:01:34:615714] [Tid0x000107f8] [info] -->[C44] comm_engine::close #(comm_engine.cpp, line:382)
[00000092] [17:01:34:615714] [Tid0x000107f8] [debug] -->[C45] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000093] [17:01:34:615714] [Tid0x000107f8] [debug] <--[C45] comm_engine::cancel
[00000094] [17:01:34:618714] [Tid0x000107f8] [info] <--[C44] comm_engine::close
[00000095] [17:01:34:618714] [Tid0x000107f8] [info] delete hsession 0xae41180 #(kernel.cpp, line:102)
[00000096] [17:01:34:618714] [Tid0x000107f8] [info] <--[C39] flashtool_destroy_session

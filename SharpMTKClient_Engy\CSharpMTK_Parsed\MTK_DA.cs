using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed
{
    public class MTK_DA
    {
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct DA_INFO
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string filepath;

            public uint start_addr;

            public IntPtr buf;

            public uint buf_len;

            public uint main_prog_size;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string version;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string last_modified_date;

            private da_data_source_e da_source;
        }

        public enum da_data_source_e
        {
            DA_SOURCE_FILE,
            DA_SOURCE_MEMORY
        }

        public static IntPtr g_da_handle;

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DA_Create(ref IntPtr p_da_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DA_Unload(IntPtr p_da_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DA_Destroy(ref IntPtr p_da_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DA_IsReady(IntPtr p_da_handle, IntPtr p_da_file, bool check_if_updated);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int DA_Load(IntPtr p_da_handle, byte[] p_da_file, bool b_da_validation, bool b_da_has_sig);

        public static void DAHandle()
        {
            int num = -1;
            num = DA_Create(ref g_da_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DA_Create err" + str);
            }
            Console.WriteLine("DA_Create i" + num);
        }

        public static void DAHandleDestroy()
        {
            int num = -1;
            num = DA_Unload(g_da_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DA_Unload err" + str);
            }
            Console.WriteLine("DA_Unload i" + num);
            num = DA_Destroy(ref g_da_handle);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("DA_Destroy err" + str2);
            }
            Console.WriteLine("DA_Destroy i" + num);
        }

        public static void DALOAD(string da_file)
        {
            int num = -1;
            num = DA_Unload(g_da_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("DA_Unload err" + str);
            }
            Console.WriteLine("DA_Unload i" + num);
            num = DA_Load(g_da_handle, Encoding.Default.GetBytes(da_file), b_da_validation: false, b_da_has_sig: false);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("DA_Load err" + str2);
            }
            Console.WriteLine("DA_Load i" + num);
        }
    }
}

﻿using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Security;
using System;

namespace SharpMTKClient_Engy.MediaTeK
{
    public static class SLA_ChallengeLib
    {
        [DllImport("Lib\\SLA_Challenge.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [HandleProcessCorruptedStateExceptions]
        [SecurityCritical]
        private static extern int SLA_Challenge(IntPtr ED9EBC86, IntPtr intptr_1, uint uint_0, out IntPtr E63A871E, ref uint uint_1);

        [DllImport("Lib\\SLA_Challenge.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        [HandleProcessCorruptedStateExceptions]
        private static extern int SLA_Challenge_End(IntPtr ********, IntPtr BC247933);

    }
}

﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed.Module
{
    public class SoftwareImage
    {
        public static Hashtable DummyProgress => new Hashtable
        {
            { "xbl", 1 },
            { "tz", 2 },
            { "hyp", 3 },
            { "rpm", 4 },
            { "emmc_appsboot", 5 },
            { "pmic", 6 },
            { "devcfg", 7 },
            { "BTFM", 8 },
            { "cmnlib", 9 },
            { "cmnlib64", 10 },
            { "NON-HLOS", 11 },
            { "adspso", 12 },
            { "mdtp", 13 },
            { "keymaster", 14 },
            { "misc", 15 },
            { "system", 16 },
            { "cache", 30 },
            { "userdata", 34 },
            { "recovery", 35 },
            { "splash", 36 },
            { "logo", 37 },
            { "boot", 38 },
            { "cust", 45 }
        };
    }
}

[00000001] [19:18:46:989823] [Tid0x0000995c] [info] -->[C1] flashtool_create_session_with_handle #(undocument_api.cpp, line:83)
[00000002] [19:18:46:989823] [Tid0x0000995c] [debug] -->[C2] connection::create_session #(connection.cpp, line:43)
[00000003] [19:18:46:989823] [Tid0x0000995c] [debug] -->[C3] kernel::create_new_session #(kernel.cpp, line:76)
[00000004] [19:18:46:989823] [Tid0x0000995c] [info] create new hsession 0x1108ac60 #(kernel.cpp, line:92)
[00000005] [19:18:46:989823] [Tid0x0000995c] [debug] <--[C3] kernel::create_new_session
[00000006] [19:18:46:989823] [Tid0x0000995c] [debug] -->[C4] boot_rom::boot_rom #(boot_rom.cpp, line:32)
[00000007] [19:18:46:989823] [Tid0x0000995c] [debug] <--[C4] boot_rom::boot_rom
[00000008] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C5] device_instance::device_instance #(device_instance.cpp, line:22)
[00000009] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C6] device_log_source::device_log_source #(device_log_source.cpp, line:8)
[00000010] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C6] device_log_source::device_log_source
[00000011] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C7] data_mux::data_mux #(data_mux.cpp, line:10)
[00000012] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C7] data_mux::data_mux
[00000013] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C5] device_instance::device_instance
[00000014] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C2] connection::create_session
[00000015] [19:18:46:996993] [Tid0x0000995c] [info] <--[C1] flashtool_create_session_with_handle
[00000016] [19:18:46:996993] [Tid0x0000995c] [info] -->[C8] flashtool_connect_brom #(flashtoolex_api.cpp, line:117)
[00000017] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C9] connection::connect_brom #(connection.cpp, line:94)
[00000018] [19:18:46:996993] [Tid0x0000995c] [info] (1/2)connecting brom. #(connection.cpp, line:97)
[00000019] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C10] engine_factory::create_transmission_engine #(engine_factory.cpp, line:45)
[00000020] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C11] is_valid_ip #(engine_factory.cpp, line:13)
[00000021] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C11] is_valid_ip
[00000022] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C12] is_lge_impl #(engine_factory.cpp, line:32)
[00000023] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C12] is_lge_impl
[00000024] [19:18:46:996993] [Tid0x0000995c] [debug] -->[C13] lib_config_parser::get_value #(lib_config_parser.cpp, line:18)
[00000025] [19:18:46:996993] [Tid0x0000995c] [info] CFG: File Not Found: F:\Info\Progra\VisualStudio\SharpMTKClient_Engy\SharpMTKClient_Engy\bin\Debug\lib.cfg.xml #(lib_config_parser.cpp, line:29)
[00000026] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C13] lib_config_parser::get_value
[00000027] [19:18:46:996993] [Tid0x0000995c] [debug] <--[C10] engine_factory::create_transmission_engine
[00000028] [19:18:46:998046] [Tid0x0000995c] [info] -->[C14] comm_engine::open #(comm_engine.cpp, line:63)
[00000029] [19:18:46:998046] [Tid0x0000995c] [info] try to open device: COM13 baud rate 115200 #(comm_engine.cpp, line:71)
[00000030] [19:18:46:999046] [Tid0x0000995c] [info] COM13 open complete. #(comm_engine.cpp, line:168)
[00000031] [19:18:46:999046] [Tid0x0000995c] [info] <--[C14] comm_engine::open
[00000032] [19:18:46:999046] [Tid0x0000995c] [debug] -->[C15] boot_rom::set_transfer_channel #(boot_rom.cpp, line:41)
[00000033] [19:18:46:999046] [Tid0x0000995c] [debug] <--[C15] boot_rom::set_transfer_channel
[00000034] [19:18:46:999046] [Tid0x0000995c] [debug] -->[C16] boot_rom::connect #(boot_rom.cpp, line:47)
[00000035] [19:18:46:999046] [Tid0x0000995c] [info] start handshake with device. #(boot_rom.cpp, line:55)
[00000036] [19:18:46:999046] [Tid0x0000995c] [debug] 			Tx->: 0x00000001 Hex[a0 ]
[00000037] [19:18:46:999046] [Tid0x0000995c] [debug] 			<-Rx: 0x00000001 Hex[a0 ]
[00000038] [19:18:46:999046] [Tid0x0000995c] [error] BRom protocol error: ACK 0x5F != 0xA0 #(boot_rom.cpp, line:94)
[00000039] [19:18:46:999046] [Tid0x0000995c] [error] brom connect exception:  #(boot_rom.cpp, line:103)
[00000040] [19:18:46:999046] [Tid0x0000995c] [error] ./brom/boot_rom.cpp(95): Throw in function int __thiscall boot_rom::connect(const struct callbacks_struct_t *)
Dynamic exception type: class boost::exception_detail::clone_impl<class runtime_exception>
std::exception::what: BRom pototcol error.
 #(boot_rom.cpp, line:104)
[00000041] [19:18:46:999046] [Tid0x0000995c] [debug] <--[C16] boot_rom::connect
[00000042] [19:18:46:999046] [Tid0x0000995c] [debug] <--[C9] connection::connect_brom
[00000043] [19:18:46:999046] [Tid0x0000995c] [error] <ERR_CHECKPOINT>[809][error][0xc0060001]</ERR_CHECKPOINT>flashtool_connect_brom fail #(flashtoolex_api.cpp, line:121)
[00000044] [19:18:46:999046] [Tid0x0000995c] [info] <--[C8] flashtool_connect_brom
[00000045] [19:18:46:999046] [Tid0x0000995c] [info] -->[C19] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000046] [19:18:46:999046] [Tid0x0000995c] [debug] -->[C21] device_instance::~device_instance #(device_instance.cpp, line:31)
[00000047] [19:18:46:999046] [Tid0x0000995c] [info] -->[C22] device_log_source::stop #(device_log_source.cpp, line:29)
[00000048] [19:18:46:999046] [Tid0x0000995c] [info] <--[C22] device_log_source::stop
[00000049] [19:18:46:999046] [Tid0x0000995c] [info] -->[C23] data_mux::stop #(data_mux.cpp, line:92)
[00000050] [19:18:46:999046] [Tid0x0000995c] [info] <--[C23] data_mux::stop
[00000051] [19:18:46:999046] [Tid0x0000995c] [debug] <--[C21] device_instance::~device_instance
[00000052] [19:18:46:999046] [Tid0x0000995c] [info] -->[C24] comm_engine::close #(comm_engine.cpp, line:382)
[00000053] [19:18:46:999046] [Tid0x0000995c] [debug] -->[C25] comm_engine::cancel #(comm_engine.cpp, line:370)
[00000054] [19:18:46:999046] [Tid0x0000995c] [debug] <--[C25] comm_engine::cancel
[00000055] [19:18:47:007046] [Tid0x0000995c] [info] <--[C24] comm_engine::close
[00000056] [19:18:47:007046] [Tid0x0000995c] [info] delete hsession 0x1108ac60 #(kernel.cpp, line:102)
[00000057] [19:18:47:007046] [Tid0x0000995c] [info] <--[C19] flashtool_destroy_session
[00000058] [19:18:47:008050] [Tid0x0000995c] [info] -->[C26] flashtool_shutdown_device #(flashtoolex_api.cpp, line:149)
[00000059] [19:18:47:008050] [Tid0x0000995c] [debug] -->[C27] connection::shutdown_device #(connection.cpp, line:989)
[00000060] [19:18:47:008050] [Tid0x0000995c] [error] invalid session. #(connection.cpp, line:994)
[00000061] [19:18:47:008050] [Tid0x0000995c] [debug] <--[C27] connection::shutdown_device
[00000062] [19:18:47:008050] [Tid0x0000995c] [error] <ERR_CHECKPOINT>[811][error][0xc001000a]</ERR_CHECKPOINT>flashtool_shutdown_device fail #(flashtoolex_api.cpp, line:154)
[00000063] [19:18:47:008050] [Tid0x0000995c] [info] <--[C26] flashtool_shutdown_device
[00000064] [19:18:47:008050] [Tid0x0000995c] [info] -->[C28] flashtool_destroy_session #(flashtoolex_api.cpp, line:104)
[00000065] [19:18:47:008050] [Tid0x0000995c] [info] <--[C28] flashtool_destroy_session

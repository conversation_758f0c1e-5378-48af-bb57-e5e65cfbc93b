[0000001] [08:46:04:935661] [Tid0x00003460] [debug] -->[C1] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000002] [08:46:04:935661] [Tid0x00003460] [debug] <--[C1] DL_SetChecksumLevel
[0000003] [08:46:11:468110] [Tid0x0000fc18] [debug] -->[C2] DL_LoadScatter #(api.cpp, line:2554)
[0000004] [08:46:11:487113] [Tid0x0000fc18] [debug] used lib version: 2 #(api.cpp, line:2576)
[0000005] [08:46:11:487113] [Tid0x0000fc18] [debug] <--[C2] DL_LoadScatter
[0000006] [08:46:11:488111] [Tid0x0000fc18] [debug] -->[C3] DL_AutoLoadRomImages #(api.cpp, line:2776)
[0000007] [08:46:11:571628] [Tid0x0000fc18] [debug] <--[C3] DL_AutoLoadRomImages
[0000008] [08:46:11:573630] [Tid0x0000fc18] [debug] -->[C4] DL_GetCount #(api.cpp, line:2696)
[0000009] [08:46:11:580117] [Tid0x0000fc18] [debug] <--[C4] DL_GetCount
[0000010] [08:46:11:580117] [Tid0x0000fc18] [debug] -->[C5] DL_Rom_GetInfoAll #(api.cpp, line:2861)
[0000011] [08:46:11:580117] [Tid0x0000fc18] [debug] <--[C5] DL_Rom_GetInfoAll
[0000012] [09:00:17:628753] [Tid0x0000af14] [debug] -->[C6] DL_SetChecksumLevel #(api.cpp, line:2596)
[0000013] [09:00:17:628753] [Tid0x0000af14] [debug] <--[C6] DL_SetChecksumLevel
[0000014] [09:00:17:632760] [Tid0x0000af14] [debug] -->[C7] FlashTool_Connect #(api.cpp, line:883)
[0000015] [09:00:17:632760] [Tid0x0000af14] [debug] bCheckScatter: 1 #(api.cpp, line:884)
[0000016] [09:00:17:632760] [Tid0x0000af14] [debug] -->[C8] FlashTool_Connect_Ex #(api.cpp, line:948)
[0000017] [09:00:17:632760] [Tid0x0000af14] [debug] bCheckScatter: 1
[0000018] [09:00:17:632760] [Tid0x0000af14] [debug] -->[C9] FlashTool_Connect_BROM_Ex #(api.cpp, line:1937)
[0000019] [09:00:17:632760] [Tid0x0000af14] [debug] bCheckScatter: 1 #(api.cpp, line:1939)
[0000020] [09:00:17:632760] [Tid0x0000af14] [debug] have load scatter already #(api.cpp, line:1943)
[0000021] [09:00:17:632760] [Tid0x0000af14] [debug] libversion 2 #(api.cpp, line:1960)
[0000022] [09:00:17:632760] [Tid0x0000af14] [debug] -->[C10] cflashtool_api::FlashTool_Connect_BROM_Ex #(cflashtool_api.cpp, line:1050)
[0000023] [09:00:17:994681] [Tid0x0000af14] [warning] NOT support GetSupportModem API on chip MT6765 #(cflashtool_api.cpp, line:2554)
[0000024] [09:00:17:994681] [Tid0x0000af14] [warning] Get support Modem fail: 0xc0010003 #(cflashtool_api.cpp, line:1171)
[0000025] [09:00:18:005190] [Tid0x0000af14] [debug] <--[C10] cflashtool_api::FlashTool_Connect_BROM_Ex
[0000026] [09:00:18:005190] [Tid0x0000af14] [debug] <--[C9] FlashTool_Connect_BROM_Ex
[0000027] [09:00:18:005190] [Tid0x0000af14] [debug] <--[C8] FlashTool_Connect_Ex
[0000028] [09:00:18:005190] [Tid0x0000af14] [debug] <--[C7] FlashTool_Connect
[0000029] [09:00:18:006191] [Tid0x0000af14] [debug] -->[C11] FlashTool_Disconnect #(api.cpp, line:1033)
[0000030] [09:00:18:006191] [Tid0x0000af14] [debug] -->[C12] cflashtool_api::FlashTool_Disconnect #(cflashtool_api.cpp, line:33)
[0000031] [09:00:18:007190] [Tid0x0000af14] [debug] -->[C13] DL_ClearFTHandle #(api.cpp, line:2618)
[0000032] [09:00:18:007190] [Tid0x0000af14] [debug] <--[C13] DL_ClearFTHandle
[0000033] [09:00:18:007190] [Tid0x0000af14] [debug] <--[C12] cflashtool_api::FlashTool_Disconnect
[0000034] [09:00:18:007190] [Tid0x0000af14] [debug] <--[C11] FlashTool_Disconnect

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed
{
    public class MTK_SCERT
    {
        public static IntPtr g_scert_handle;

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int SCERT_Create(ref IntPtr p_scert_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int SCERT_Unload(IntPtr p_scert_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int SCERT_Destroy(ref IntPtr p_scert_handle);

        public static void SCERTHandle()
        {
            int num = -1;
            num = SCERT_Create(ref g_scert_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("SCERT_Create err" + str);
            }
            Console.WriteLine("SCERT_Create i" + num);
        }

        public static void SCERTHandleDestroy()
        {
            int num = -1;
            num = SCERT_Unload(g_scert_handle);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("SCERT_Unload err" + str);
            }
            Console.WriteLine("SCERT_Unload i" + num);
            num = SCERT_Destroy(ref g_scert_handle);
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("SCERT_Destroy err" + str2);
            }
            Console.WriteLine("SCERT_Destroy i" + num);
        }
    }
}

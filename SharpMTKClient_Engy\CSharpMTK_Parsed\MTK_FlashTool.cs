﻿using SharpMTKClient_Engy.CSharpMTK_Parsed.Data;
using SharpMTK<PERSON>lient_Engy.CSharpMTK_Parsed.Module;
using SharpMTKClient_Engy.CSharpMTK_Parsed.Utility;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace SharpMTKClient_Engy.CSharpMTK_Parsed
{
    public class MTK_FlashTool
    {
        public enum EXT_CLOCK
        {
            EXT_13M = 1,
            EXT_26M = 2,
            EXT_39M = 3,
            EXT_52M = 4,
            EXT_CLOCK_END = 5,
            AUTO_DETECT_EXT_CLOCK = 254,
            UNKNOWN_EXT_CLOCK = 255
        }

        public enum BBCHIP_TYPE
        {
            MT6205 = 0,
            MT6205B = 1,
            MT6218 = 2,
            MT6218B = 4,
            MT6219 = 5,
            MT6217 = 6,
            MT6228 = 7,
            MT6227 = 8,
            MT6229 = 9,
            MT6226 = 10,
            MT6226M = 11,
            MT6230 = 12,
            MT6225 = 13,
            MT6268T = 14,
            MT6223 = 15,
            MT6227D = 16,
            MT6226D = 17,
            MT6223P = 18,
            MT6238 = 19,
            MT6235 = 20,
            MT6235B = 21,
            TK6516_MD = 22,
            TK6516_AP = 23,
            MT6268A = 24,
            MT6516_MD = 25,
            MT6516_AP = 26,
            MT6239 = 27,
            MT6251T = 28,
            MT6253T = 29,
            MT6268B = 30,
            MT6253 = 32,
            MT6253D = 33,
            MT6236 = 34,
            MT6270A = 35,
            MT6276 = 128,
            MT6251 = 129,
            MT6255 = 130,
            MT6573 = 131,
            MT6575 = 132,
            MT6577 = 133,
            MT6589 = 134,
            MT6582 = 135,
            MT6572 = 136,
            MT8135 = 137,
            MT6592 = 138,
            MT6571 = 139,
            MT6595 = 140,
            MT8127 = 141,
            MT8173 = 142,
            MT6752 = 143,
            MT2601 = 144,
            MT8590 = 145,
            MT7623 = 146,
            MT7683 = 147,
            MT8591 = 148,
            MT8592 = 149,
            MT8531 = 150,
            MT7863 = 151,
            MT2701 = 152,
            MT8521 = 153,
            MT6795 = 154,
            MT6574 = 135,
            MT6735 = 155,
            MT6580 = 156,
            MT6735M = 157,
            MT6753 = 158,
            MT6737T = 159,
            MT6737M = 160,
            MT8163 = 161,
            MT6755 = 180,
            MT6797 = 181,
            MT6799 = 182,
            MT0571 = 183,
            MT6750 = 184,
            ELBRUS = 185,
            MT6757 = 186,
            MT6757D = 187,
            MT6759 = 188,
            MT8167 = 189,
            MT6570 = 190,
            MT6763 = 191,
            MT6758 = 192,
            MT6739 = 193,
            MT8695 = 194,
            MT6775 = 195,
            MT6765 = 196,
            MT6771 = 197,
            MT8518 = 198,
            MT3967 = 199,
            MT6761 = 200,
            MT6779 = 201,
            MT8168 = 202,
            MT6768 = 203,
            MT6731 = 204,
            MT6785 = 205,
            MT6885 = 206,
            MT8512 = 207,
            MT6873 = 208,
            MT0992 = 209,
            MT6853 = 210,
            MT6893 = 211,
            MT6833 = 212,
            MT8696 = 213,
            MT8195 = 214,
            MT6877 = 215,
            BBCHIP_TYPE_END = 216,
            AUTO_DETECT_BBCHIP = 254,
            UNKNOWN_BBCHIP_TYPE = 255
        }

        public delegate int CALLBACK_COM_INIT_STAGE(IntPtr hCom, IntPtr usr_arg);

        public delegate int CALLBACK_IN_BROM_STAGE(uint brom_handle, IntPtr hCom, IntPtr usr_arg);

        public delegate int CALLBACK_SLA_CHALLENGE(IntPtr usr_arg, IntPtr p_challenge_in, uint challenge_in_len, out IntPtr pp_challenge_out, ref uint p_challenge_out_len);

        public delegate int CALLBACK_SLA_CHALLENGE_END(IntPtr usr_arg, IntPtr p_challenge_out);

        public delegate int CALLBACK_WRITE_BUF_PROGRESS_INIT(IntPtr usr_arg);

        public delegate int CALLBACK_WRITE_BUF_PROGRESS(byte finished_percentage, uint finished_bytes, uint total_bytes, IntPtr usr_arg);

        public delegate void ControlCmd(int sn, byte data, int length);

        public struct BOOT_FLASHTOOL_ARG
        {
            public BBCHIP_TYPE m_bbchip_type;

            public EXT_CLOCK m_ext_clock;

            public uint m_baudrate;

            public uint m_ms_boot_timeout;

            public uint m_max_start_cmd_retry_count;

            public CALLBACK_COM_INIT_STAGE m_cb_com_init_stage;

            public IntPtr m_cb_com_init_stage_arg;

            public CALLBACK_IN_BROM_STAGE m_cb_in_brom_stage;

            public IntPtr m_cb_in_brom_stage_arg;

            public bool m_speedup_brom_baudrate;

            public IntPtr m_ready_power_on_wnd_handle;

            public IntPtr m_ready_power_on_wparam;

            public IntPtr m_ready_power_on_lparam;

            public IntPtr m_auth_handle;

            public IntPtr m_scert_handle;

            public CALLBACK_SLA_CHALLENGE m_cb_sla_challenge;

            public IntPtr m_cb_sla_challenge_arg;

            public CALLBACK_SLA_CHALLENGE_END m_cb_sla_challenge_end;

            public IntPtr m_cb_sla_challenge_end_arg;

            public IntPtr m_p_bank0_mem_cfg;

            public IntPtr m_p_bank1_mem_cfg;

            public bool m_enable_da_start_addr;

            public uint m_da_start_addr;

            public IntPtr m_da_handle;

            public CALLBACK_WRITE_BUF_PROGRESS_INIT m_cb_download_da_init;

            public IntPtr m_cb_download_da_init_arg;

            public CALLBACK_WRITE_BUF_PROGRESS m_cb_download_da;

            public IntPtr m_cb_download_da_arg;

            public bool m_usb_enable;

            public uint m_bmt_block_count;
        }

        public delegate int CALLBACK_SECURITY_PRE_PROCESS_NOTIFY(IntPtr usr_arg);

        public enum HW_ChipSelect_E
        {
            CS_0 = 0,
            CS_1 = 1,
            CS_2 = 2,
            CS_3 = 3,
            CS_4 = 4,
            CS_5 = 5,
            CS_6 = 6,
            CS_7 = 7,
            CS_WITH_DECODER = 8,
            MAX_CS = 8,
            HW_CHIP_SELECT_END = 9
        }

        public enum HW_StorageType_E
        {
            HW_STORAGE_NOR,
            HW_STORAGE_NAND,
            HW_STORAGE_EMMC,
            HW_STORAGE_SDMMC,
            HW_STORAGE_UFS,
            HW_STORAGE_NONE,
            HW_STORAGE_TYPE_END
        }

        public enum HW_RAMType_E
        {
            HW_RAM_UNKNOWN,
            HW_RAM_SRAM,
            HW_RAM_DRAM,
            HW_RAM_TYPE_END
        }

        public enum CONN_DA_END_STAGE
        {
            FIRST_DA = 1,
            SECOND_DA
        }

        public enum DA_LOG_LEVEL_E
        {
            DA_LOG_LEVEL_TRACE,
            DA_LOG_LEVEL_DEBUG,
            DA_LOG_LEVEL_INFO,
            DA_LOG_LEVEL_WARNING,
            DA_LOG_LEVEL_ERROR,
            DA_LOG_LEVEL_FATAL
        }

        public enum DA_LOG_CHANNEL_E
        {
            DA_LOG_CHANNEL_NONE,
            DA_LOG_CHANNEL_UART,
            DA_LOG_CHANNEL_USB,
            DA_LOG_CHANNEL_UART_USB
        }

        public enum FC_TYPE
        {
            FORCE_CHARGE_OFF,
            FORCE_CHARGE_ON,
            FORCE_CHARGE_AUTO
        }

        public struct FlashTool_Connect_Arg
        {
            public uint m_com_ms_read_timeout;

            public uint m_com_ms_write_timeout;

            public BOOT_FLASHTOOL_ARG m_boot_arg;

            public CALLBACK_SECURITY_PRE_PROCESS_NOTIFY m_cb_security_pre_process_notify;

            public IntPtr m_cb_security_pre_process_notify_arg;

            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)]
            public HW_ChipSelect_E[] m_nor_chip_select;

            public HW_ChipSelect_E m_nand_chip_select;

            public IntPtr m_p_nand_acccon;

            public HW_StorageType_E m_storage_type;

            public IntPtr m_p_dl_handle;

            public byte m_force_charge;

            public byte m_reset_key;

            public CONN_DA_END_STAGE m_conn_da_end_stage;

            public bool m_1st_da_enable_dram;

            public DA_LOG_LEVEL_E m_da_log_level;

            public DA_LOG_CHANNEL_E m_da_log_channel;
        }

        public struct FlashTool_Connect_Result
        {
            public DA_REPORT_T m_da_report;
        }

        public struct DA_REPORT_T
        {
            public byte m_expected_da_major_ver;

            public byte m_expected_da_minor_ver;

            public byte m_da_major_ver;

            public byte m_da_minor_ver;

            public BBCHIP_TYPE m_bbchip_type;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
            public string m_bbchip_name;

            public ushort m_bbchip_hw_ver;

            public ushort m_bbchip_sw_ver;

            public ushort m_bbchip_hw_code;

            public EXT_CLOCK m_ext_clock;

            public byte m_bbchip_secure_ver;

            public byte m_bbchip_bl_ver;

            public MTK_Status.STATUS_E m_nor_ret;

            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)]
            public HW_ChipSelect_E[] m_nor_chip_select;

            public ushort m_nor_flash_id;

            public uint m_nor_flash_size;

            public ushort m_nor_flash_dev_code_1;

            public ushort m_nor_flash_dev_code_2;

            public ushort m_nor_flash_dev_code_3;

            public ushort m_nor_flash_dev_code_4;

            public MTK_Status.STATUS_E m_nor_flash_otp_status;

            public uint m_nor_flash_otp_size;

            public MTK_Status.STATUS_E m_nand_ret;

            public HW_ChipSelect_E m_nand_chip_select;

            public ushort m_nand_flash_id;

            public ulong m_nand_flash_size;

            public ushort m_nand_id_count;

            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
            public ushort[] m_nand_flash_dev_code;

            public ushort m_nand_pagesize;

            public ushort m_nand_sparesize;

            public ushort m_nand_pages_per_block;

            public byte m_nand_io_interface;

            public byte m_nand_addr_cycle;

            public byte m_nand_bmt_exist;

            public MTK_Status.STATUS_E m_int_sram_ret;

            public uint m_int_sram_size;

            public MTK_Status.STATUS_E m_ext_ram_ret;

            public HW_RAMType_E m_ext_ram_type;

            public HW_ChipSelect_E m_ext_ram_chip_select;

            public ulong m_ext_ram_size;

            public byte m_msp_err_code;

            public MTK_Status.STATUS_E m_download_status;

            public MTK_Status.STATUS_E m_boot_style;

            public uint m_fw_ver_len;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string m_fw_ver;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
            public string m_random_id;

            private IntPtr m_dl_handle;

            public MTK_Status.STATUS_E m_emmc_ret;

            public ulong m_emmc_boot1_size;

            public ulong m_emmc_boot2_size;

            public ulong m_emmc_rpmb_size;

            public ulong m_emmc_gp1_size;

            public ulong m_emmc_gp2_size;

            public ulong m_emmc_gp3_size;

            public ulong m_emmc_gp4_size;

            public ulong m_emmc_ua_size;

            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
            public uint[] m_emmc_cid;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
            public string m_emmc_fwver;

            public MTK_Status.STATUS_E m_sdmmc_ret;

            public ulong m_sdmmc_ua_size;

            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
            public uint[] m_sdmmc_cid;

            public MTK_Status.STATUS_E m_ufs_ret;

            public ulong m_ufs_lu0_size;

            public ulong m_ufs_lu1_size;

            public ulong m_ufs_lu2_size;

            public ushort m_ufs_vendor_id;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 17)]
            public string m_ufs_cid;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 5)]
            public string m_ufs_fwver;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 129)]
            public string m_ufs_sn;
        }

        public enum ACCURACY
        {
            ACCURACY_AUTO = 0,
            ACCURACY_1_3 = 3,
            ACCURACY_1_4 = 4,
            ACCURACY_1_10 = 10,
            ACCURACY_1_100 = 100,
            ACCURACY_1_1000 = 1000,
            ACCURACY_1_10000 = 10000
        }

        public delegate int CALLBACK_DA_REPORT(ref DA_REPORT_T p_da_report, IntPtr usr_arg);

        public delegate int CALLBACK_DOWNLOAD_PROGRESS_INIT(IntPtr usr_arg, string image_name);

        public delegate int CALLBACK_DOWNLOAD_PROGRESS(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg);

        public delegate int CALLBACK_BOOTLOADER_DOWNLOAD_PROGRESS_INIT(IntPtr usr_arg);

        public delegate int CALLBACK_BOOTLOADER_DOWNLOAD_PROGRESS(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg);

        public delegate int CALLBACK_CHECKSUM_PROGRESS_INIT(IntPtr usr_arg, string image_name);

        public delegate int CALLBACK_CHECKSUM_PROGRESS(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg);

        public delegate int CALLBACK_SECURITY_POST_PROCESS_NOTIFY(IntPtr usr_arg);

        public struct FlashTool_Download_Arg
        {
            public IntPtr m_dl_handle;

            public IntPtr m_dl_handle_list;

            public CALLBACK_DA_REPORT m_cb_da_report;

            public IntPtr m_cb_da_report_arg;

            public ACCURACY m_download_accuracy;

            public CALLBACK_DOWNLOAD_PROGRESS_INIT m_cb_download_flash_init;

            public IntPtr m_cb_download_flash_init_arg;

            public CALLBACK_DOWNLOAD_PROGRESS m_cb_download_flash;

            public IntPtr m_cb_download_flash_arg;

            public CALLBACK_BOOTLOADER_DOWNLOAD_PROGRESS_INIT m_cb_download_bloader_init;

            public IntPtr m_cb_download_bloader_init_arg;

            public CALLBACK_BOOTLOADER_DOWNLOAD_PROGRESS m_cb_download_bloader;

            public IntPtr m_cb_download_bloader_arg;

            public CALLBACK_SECURITY_POST_PROCESS_NOTIFY m_cb_security_post_process_notify;

            public IntPtr m_cb_security_post_process_notify_arg;

            public CALLBACK_CHECKSUM_PROGRESS_INIT m_cb_checksum_init;

            public IntPtr m_cb_checksum_init_arg;

            public CALLBACK_CHECKSUM_PROGRESS m_cb_checksum;

            public IntPtr m_cb_checksum_arg;

            public bool m_enable_tgt_res_layout_check;

            public bool m_enable_bbchip_ver_check;

            public bool m_downloadstyle_sequential;
        }

        public struct FlashTool_Download_Result
        {
        }

        public enum AddressingMode
        {
            AddressingMode_BlockIndex,
            AddressingMode_PhysicalAddress,
            AddressingMode_LogicalAddress
        }

        public enum InputMode
        {
            InputMode_FromBuffer,
            InputMode_FromFile
        }

        public enum ProgramMode
        {
            ProgramMode_PageOnly,
            ProgramMode_PageSpare
        }

        public delegate int CALLBACK_WRITE_FLASH_PROGRESS(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg);

        public struct WriteFlashMemoryParameter
        {
            public HW_StorageType_E m_flash_type;

            public AddressingMode m_addressing_mode;

            public ulong m_address;

            public uint m_container_length;

            public InputMode m_input_mode;

            public string m_input;

            public ulong m_input_length;

            public uint m_part_id;

            public CALLBACK_WRITE_FLASH_PROGRESS m_cb_progress;

            public IntPtr m_cb_progress_arg;

            public ProgramMode m_program_mode;
        }

        public struct FlashTool_EnableWDT_Arg
        {
            public uint m_timeout_ms;

            public bool m_async;

            public bool m_reboot;

            public bool m_dlbit;

            public bool m_bNotResetRTCTime;
        }

        public enum transfer_phase
        {
            TPHASE_INIT,
            TPHASE_DA,
            TPHASE_LOADER,
            TPHASE_IMAGE,
            TPHASE_HOST_CHECKSUM,
            TPHASE_TARGET_CHECKSUM,
            TPHASE_FORMAT,
            TPHASE_READBACK,
            TPHASE_WRITE_MEMORY,
            TPHASE_MEMORY_TEST
        }

        public struct cbs_additional_info
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string image_name;
        }

        public delegate void CB_OPERATION_PROGRESS(IntPtr _this, transfer_phase phase, uint progress, ulong data_xferd, ulong data_total, ref cbs_additional_info additional_info);

        public delegate void CB_STAGE_MESSAGE(IntPtr _this, string message);

        public delegate bool CB_NOTIFY_STOP(IntPtr _this);

        public struct sla_callbacks_t
        {
            public CALLBACK_SLA_CHALLENGE cb_start;

            public CALLBACK_SLA_CHALLENGE_END cb_end;

            public IntPtr start_user_arg;

            public IntPtr end_user_arg;
        }

        public struct callbacks_struct_t
        {
            public IntPtr _this;

            public CB_OPERATION_PROGRESS cb_op_progress;

            public CB_STAGE_MESSAGE cb_stage_message;

            public CB_NOTIFY_STOP cb_notify_stop;

            public sla_callbacks_t cb_sla;
        }

        public struct ExternalMemoryConfig
        {
        }

        public struct ex_ufs_config
        {
            public uint force_provision;

            public uint tw_size_gb;

            public uint tw_no_red;

            public uint hpb_size_gb;

            public uint lu3_size_mb;

            public uint lu3_type;
        }

        public struct UFS_Config
        {
            public uint force_provision;

            public uint tw_size_gb;

            public uint tw_no_red;

            public uint hpb_size_gb;

            public uint lu3_size_mb;

            public uint lu3_type;
        }

        /// <summary>
        /// Partition information structure for reading device partitions
        /// </summary>
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct PartitionInfo
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string name;

            public ulong begin_addr;

            public ulong end_addr;

            public ulong size;

            public uint part_id;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
            public string type;
        }

        /// <summary>
        /// Meta boot argument structure
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MetaBootArg
        {
            public uint timeout_ms;

            public bool async_mode;

            public bool reboot_after;
        }

        /// <summary>
        /// Bootloader lock state enumeration
        /// </summary>
        public enum BootloaderLockState
        {
            LOCKED = 0,
            UNLOCKED = 1,
            UNKNOWN = 2
        }

        /// <summary>
        /// NV Item operation type
        /// </summary>
        public enum NVItemOperation
        {
            READ = 0,
            WRITE = 1,
            ERASE = 2
        }

        /// <summary>
        /// RPMB operation type
        /// </summary>
        public enum RPMBOperation
        {
            READ = 0,
            WRITE = 1,
            ERASE = 2
        }

        /// <summary>
        /// Flash operation mode
        /// </summary>
        public enum FlashMode
        {
            DOWNLOAD_ONLY = 0,
            FORMAT_ALL_DOWNLOAD = 1,
            FIRMWARE_UPGRADE = 2
        }

        /// <summary>
        /// NV Item information structure
        /// </summary>
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct NVItemInfo
        {
            public uint item_id;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string name;

            public uint size;

            public IntPtr data_buffer;
        }

        /// <summary>
        /// RPMB information structure
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct RPMBInfo
        {
            public uint block_count;

            public uint block_size;

            public uint total_size;

            public bool is_configured;
        }

        /// <summary>
        /// GPT entry structure
        /// </summary>
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct GPTEntry
        {
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
            public byte[] partition_type_guid;

            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
            public byte[] unique_partition_guid;

            public ulong first_lba;

            public ulong last_lba;

            public ulong attributes;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 36)]
            public string partition_name;
        }

        /// <summary>
        /// Raw firmware operation parameters
        /// </summary>
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct RawFirmwareParams
        {
            public ulong start_address;

            public ulong size;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string file_path;

            public bool verify_after_write;
        }

        /// <summary>
        /// Preloader operation parameters
        /// </summary>
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct PreloaderParams
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string file_path;

            public bool backup_original;

            public bool verify_after_write;
        }

        public enum NUTL_AddrTypeFlag_E
        {
            NUTL_ADDR_LOGICAL,
            NUTL_ADDR_PHYSICAL,
            NUTL_ADDR_FLAG_END
        }

        public enum NUTL_EraseFlag_E
        {
            NUTL_ERASE,
            NUTL_FORCE_ERASE,
            NUTL_MARK_BAD_BLOCK,
            NUTL_ERASE_FLAG_END
        }

        public enum EMMC_Part_E
        {
            EMMC_PART_UNKNOWN,
            EMMC_PART_BOOT1,
            EMMC_PART_BOOT2,
            EMMC_PART_RPMB,
            EMMC_PART_GP1,
            EMMC_PART_GP2,
            EMMC_PART_GP3,
            EMMC_PART_GP4,
            EMMC_PART_USER,
            EMMC_PART_END,
            EMMC_PART_BOOT1_BOOT2
        }

        public enum UFS_Part_E
        {
            UFS_PART_UNKNOWN,
            UFS_PART_LU0,
            UFS_PART_LU1,
            UFS_PART_LU2,
            UFS_PART_END,
            UFS_PART_LU0_LU1
        }

        public struct FORMAT_CONFIG_T
        {
            public bool m_auto_format_fat;

            public bool m_validation;

            public ulong m_format_begin_addr;

            public ulong m_format_length;

            public uint m_part_id;

            public NUTL_AddrTypeFlag_E m_AddrType;
        }

        public struct FlashTool_Format_Arg
        {
            public HW_StorageType_E m_storage_type;

            public FORMAT_CONFIG_T m_format_cfg;

            public NUTL_EraseFlag_E m_erase_flag;

            public CALLBACK_FORMAT_PROGRESS_INIT m_cb_format_report_init;

            public IntPtr m_cb_format_report_init_arg;

            public CALLBACK_FORMAT_PROGRESS m_cb_format_report;

            public IntPtr m_cb_format_report_arg;

            public CALLBACK_FORMAT_STATISTICS m_cb_format_statistics;

            public IntPtr m_cb_format_statistics_arg;
        }

        public struct FormatStatisticsReport_T
        {
            public ulong m_fmt_begin_addr;

            public ulong m_fmt_length;

            public uint m_total_blocks;

            public uint m_bad_blocks;

            public uint m_err_blocks;
        }

        public delegate int CALLBACK_FORMAT_PROGRESS_INIT(HW_StorageType_E storage_type, ulong begin_addr, ulong length, IntPtr usr_arg);

        public delegate int CALLBACK_FORMAT_PROGRESS(byte finished_percentage, IntPtr usr_arg);

        public delegate int CALLBACK_FORMAT_STATISTICS(ref FormatStatisticsReport_T p_report, IntPtr usr_arg);

        public struct FlashTool_Format_Result
        {
            private FormatStatisticsReport_T m_format_statistics;
        }

        private IntPtr g_ft_handle;

        public HW_StorageType_E m_storage_type = HW_StorageType_E.HW_STORAGE_NONE;

        public ushort m_ufs_vendor_id;

        public string m_ufs_cid = "";

        public string m_ufs_fwver = "";

        public string LogName = "";

        private FlashTool_Connect_Result Flashtool_connectResult;

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Connect(short com_port, ref FlashTool_Connect_Arg p_arg, ref FlashTool_Connect_Result p_result, ref ExternalMemoryConfig p_external_memory_config, IntPtr p_stopflag, ref IntPtr p_ft_handle, bool bbCheckScatter = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Connect_BROM(short com_port, ref FlashTool_Connect_Arg p_arg, ref IntPtr p_ft_handle, IntPtr p_stopflag, bool bbCheckScatter = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Disconnect_BROM(ref IntPtr p_ft_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Connect_Download_DA(ref FlashTool_Connect_Arg p_arg, ref IntPtr p_ft_handle, ref FlashTool_Connect_Result p_result, IntPtr p_stopflag, bool bbCheckScatter = true);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Disconnect(ref IntPtr p_ft_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Download(IntPtr p_ft_handle, ref FlashTool_Download_Arg p_dl_arg, ref FlashTool_Download_Result p_dl_result);
        [DllImport("Lib\\SLA_Challenge.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int SLA_Challenge(IntPtr usr_arg, IntPtr p_challenge_in, uint challenge_in_len, out IntPtr pp_challenge_out, ref uint p_challenge_out_len);

        [DllImport("Lib\\SLA_Challenge.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int SLA_Challenge_End(IntPtr usr_arg, IntPtr p_challenge_out);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_WriteFlashMemory(IntPtr p_ft_handle, ref WriteFlashMemoryParameter parameter, bool is_by_sram = false);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_EnableWatchDogTimeout(IntPtr ft_handle, ref FlashTool_EnableWDT_Arg p_arg);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Format_Partition(IntPtr ft_handle, string part_name, ref callbacks_struct_t callbacks);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_Format(IntPtr ft_handle, ref FlashTool_Format_Arg p_fmt_arg, ref FlashTool_Format_Result p_fmt_result);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_FirmwareUpdate(IntPtr ft_handle, byte[] fw_filename, string fw_buffer, uint fw_buffer_len);

        [DllImport("FlashtoollibEx.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int flashtool_set_ufs_config(IntPtr ft_handle, ref ex_ufs_config cfg);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_SetUFSConfig(IntPtr ft_handle, ref UFS_Config cfg);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadPartitionCount(IntPtr ft_handle, ref uint partition_count);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadPartitionInfo(IntPtr ft_handle, IntPtr partition_info_array, uint array_size);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int Boot_META(IntPtr ft_handle, ref MetaBootArg meta_arg, ref ExternalMemoryConfig ext_mem_cfg, ref int stop_flag);

        // Partition Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ErasePartition(IntPtr ft_handle, string partition_name);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadPartition(IntPtr ft_handle, string partition_name, string output_file);

        // Bootloader Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_UnlockBootloader(IntPtr ft_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_RelockBootloader(IntPtr ft_handle);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_GetBootloaderLockState(IntPtr ft_handle, ref BootloaderLockState state);

        // NV Items Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadNVItem(IntPtr ft_handle, uint item_id, IntPtr buffer, uint buffer_size, ref uint actual_size);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_WriteNVItem(IntPtr ft_handle, uint item_id, IntPtr buffer, uint buffer_size);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_EraseNVItem(IntPtr ft_handle, uint item_id);

        // RPMB Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadRPMB(IntPtr ft_handle, uint block_addr, uint block_count, IntPtr buffer);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_WriteRPMB(IntPtr ft_handle, uint block_addr, uint block_count, IntPtr buffer);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_EraseRPMB(IntPtr ft_handle, uint block_addr, uint block_count);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_GetRPMBInfo(IntPtr ft_handle, ref RPMBInfo rpmb_info);

        // GPT Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadGPT(IntPtr ft_handle, IntPtr gpt_buffer, uint buffer_size);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_WriteGPT(IntPtr ft_handle, IntPtr gpt_buffer, uint buffer_size);

        // Raw Firmware Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadRawFirmware(IntPtr ft_handle, ulong start_addr, ulong size, string output_file);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_WriteRawFirmware(IntPtr ft_handle, ulong start_addr, string input_file, bool verify);

        // Preloader Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_ReadPreloader(IntPtr ft_handle, string output_file);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_WritePreloader(IntPtr ft_handle, string input_file, bool verify);

        // Advanced Flash Operations
        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_FlashSelectedPartitions(IntPtr ft_handle, IntPtr partition_list, uint partition_count, FlashMode mode);

        [DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern int FlashTool_FlashScatterFirmware(IntPtr ft_handle, string scatter_file, FlashMode mode);

        public void SetLogName(string logname)
        {
            LogName = logname;
        }

        public static void DebugLogsOn()
        {
            Console.WriteLine("DebugLogsOn begin");
            string text = AppDomain.CurrentDomain.SetupInformation.ApplicationBase + "log\\BROM_DLL_V5.log";
            Console.WriteLine("Brom logPath: " + text);
            int num = -1;
            num = MTK_Common.Brom_Debug_SetLogFilename(Encoding.Default.GetBytes(text));
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                Console.WriteLine("Brom_Debug_SetLogFilename err" + str);
            }
            Console.WriteLine("Brom_Debug_SetLogFilename i" + num);
            num = MTK_Common.Brom_DebugOn();
            if (num != 0)
            {
                string str2 = MTK_Common.StatusToString(num);
                str2 = MTK_Common.decodeOut(str2);
                Console.WriteLine("Brom_DebugOn err" + str2);
            }
            Console.WriteLine("Brom_DebugOn i" + num);
            Console.WriteLine("DebugLogsOn end");
        }

        public int DAConnect_new(int idx, short com_port_num)
        {
            int num = -1;
            try
            {
                Console.WriteLine($"LogName: {LogName} DAConnect begin idx: {idx} com_port_num: {com_port_num}");
                //////FlashingDevice.UpdateDeviceStatus(LogName, null, "DAConnect begin idx: " + idx + " com_port_num: " + com_port_num, null, isDone: false);
                Console.WriteLine(LogName + " DAConnect begin idx: " + idx + " com_port_num: " + com_port_num);
                FlashTool_Connect_Arg p_arg = default(FlashTool_Connect_Arg);
                p_arg.m_com_ms_read_timeout = 268435455u;
                p_arg.m_com_ms_write_timeout = 268435455u;
                p_arg.m_boot_arg = BootArgSetting();
                CALLBACK_SECURITY_PRE_PROCESS_NOTIFY cALLBACK_SECURITY_PRE_PROCESS_NOTIFY = cb_security_pre_process_notify;
                p_arg.m_cb_security_pre_process_notify = cALLBACK_SECURITY_PRE_PROCESS_NOTIFY;
                p_arg.m_nor_chip_select = new HW_ChipSelect_E[2];
                p_arg.m_nor_chip_select[0] = HW_ChipSelect_E.CS_0;
                p_arg.m_nor_chip_select[1] = HW_ChipSelect_E.CS_WITH_DECODER;
                p_arg.m_p_dl_handle = MTK_DL.g_dl_handle;
                p_arg.m_conn_da_end_stage = CONN_DA_END_STAGE.SECOND_DA;
                p_arg.m_1st_da_enable_dram = false;
                p_arg.m_da_log_level = DA_LOG_LEVEL_E.DA_LOG_LEVEL_DEBUG;
                p_arg.m_da_log_channel = DA_LOG_CHANNEL_E.DA_LOG_CHANNEL_UART;
                int num2 = Marshal.SizeOf(typeof(FlashTool_Connect_Arg));

                Console.WriteLine($"LogName: {LogName} FlashTool_Connect_Arg size: {num2}");
                //////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_Arg size:" + num2, null, isDone: false);
                Console.WriteLine(LogName + "FlashTool_Connect_Arg size:" + num2);

                Console.WriteLine($"LogName: {LogName} BOOT_FLASHTOOL_ARG size: {Marshal.SizeOf(typeof(BOOT_FLASHTOOL_ARG))}");
                //////FlashingDevice.UpdateDeviceStatus(LogName, null, "BOOT_FLASHTOOL_ARG size:" + Marshal.SizeOf(typeof(BOOT_FLASHTOOL_ARG)), null, isDone: false);

                Console.WriteLine(LogName + " BOOT_FLASHTOOL_ARG size:" + Marshal.SizeOf(typeof(BOOT_FLASHTOOL_ARG)));
                IntPtr p_stopflag = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(int)));
                Flashtool_connectResult.m_da_report = default(DA_REPORT_T);
                ExternalMemoryConfig p_external_memory_config = default(ExternalMemoryConfig);

                Console.WriteLine($"LogName: {LogName} FlashTool_Connect begin.(Please Download after Shutdown!");
                //////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect begin.(Please Download after Shutdown!)", null, isDone: false);
                num = FlashTool_Connect(com_port_num, ref p_arg, ref Flashtool_connectResult, ref p_external_memory_config, p_stopflag, ref g_ft_handle);
                if (num != 0)
                {
                    MTK_Status.STATUS_E sTATUS_E = MTK_Common.IntConvertToEnum((uint)num);
                    Console.WriteLine(LogName + " FlashTool_Connect err " + sTATUS_E);
                    return num;
                }
                Console.WriteLine(LogName + " FlashTool_Connect i" + num);
                Console.WriteLine(LogName + " Flashtool_connectResult:");
                Console.WriteLine(LogName + " m_expected_da_major_ver:" + Flashtool_connectResult.m_da_report.m_expected_da_major_ver);
                Console.WriteLine(LogName + " m_expected_da_minor_ver:" + Flashtool_connectResult.m_da_report.m_expected_da_minor_ver);
                Console.WriteLine(LogName + " m_da_major_ver:" + Flashtool_connectResult.m_da_report.m_da_major_ver);
                Console.WriteLine(LogName + " m_da_minor_ver:" + Flashtool_connectResult.m_da_report.m_da_minor_ver);
                Console.WriteLine(LogName + " m_bbchip_type:" + Flashtool_connectResult.m_da_report.m_bbchip_type);
                Console.WriteLine(LogName + " m_bbchip_name:" + Flashtool_connectResult.m_da_report.m_bbchip_name);
                Console.WriteLine(LogName + " m_bbchip_hw_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_hw_ver);
                Console.WriteLine(LogName + " m_bbchip_hw_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_hw_ver);
                Console.WriteLine(LogName + " m_bbchip_sw_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_sw_ver);
                Console.WriteLine(LogName + " m_bbchip_hw_code:" + Flashtool_connectResult.m_da_report.m_bbchip_hw_code);
                Console.WriteLine(LogName + " m_bbchip_secure_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_secure_ver);
                Console.WriteLine(LogName + " m_bbchip_bl_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_bl_ver);
                Console.WriteLine(LogName + " m_ext_clock:" + Flashtool_connectResult.m_da_report.m_ext_clock);
                Console.WriteLine(LogName + " m_nand_ret:" + Flashtool_connectResult.m_da_report.m_nand_ret);
                Console.WriteLine(LogName + " m_emmc_ret:" + Flashtool_connectResult.m_da_report.m_emmc_ret);
                Console.WriteLine(LogName + " m_sdmmc_ret:" + Flashtool_connectResult.m_da_report.m_sdmmc_ret);
                Console.WriteLine(LogName + " m_ufs_ret:" + Flashtool_connectResult.m_da_report.m_ufs_ret);
                if (Flashtool_connectResult.m_da_report.m_nor_ret == MTK_Status.STATUS_E.S_DONE)
                {
                    Console.WriteLine(LogName + " HW_STROAGE_NOR");
                    m_storage_type = HW_StorageType_E.HW_STORAGE_NOR;
                }
                else if (Flashtool_connectResult.m_da_report.m_nand_ret == MTK_Status.STATUS_E.S_DONE)
                {
                    Console.WriteLine(LogName + " HW_STROAGE_NAND");
                    m_storage_type = HW_StorageType_E.HW_STORAGE_NAND;
                }
                else if (Flashtool_connectResult.m_da_report.m_emmc_ret == MTK_Status.STATUS_E.S_DONE)
                {
                    Console.WriteLine(LogName + " HW_STROAGE_EMMC");
                    Console.WriteLine(LogName + " m_emmc_boot1_size:" + Flashtool_connectResult.m_da_report.m_emmc_boot1_size);
                    Console.WriteLine(LogName + " m_emmc_boot2_size:" + Flashtool_connectResult.m_da_report.m_emmc_boot2_size);
                    Console.WriteLine(LogName + " m_emmc_rpmb_size:" + Flashtool_connectResult.m_da_report.m_emmc_rpmb_size);
                    Console.WriteLine(LogName + " m_emmc_gp1_size:" + Flashtool_connectResult.m_da_report.m_emmc_gp1_size);
                    Console.WriteLine(LogName + " m_emmc_gp2_size:" + Flashtool_connectResult.m_da_report.m_emmc_gp2_size);
                    Console.WriteLine(LogName + " m_emmc_gp3_size:" + Flashtool_connectResult.m_da_report.m_emmc_gp3_size);
                    Console.WriteLine(LogName + " m_emmc_gp4_size:" + Flashtool_connectResult.m_da_report.m_emmc_gp4_size);
                    Console.WriteLine(LogName + " m_emmc_ua_size:" + Flashtool_connectResult.m_da_report.m_emmc_ua_size);
                    m_storage_type = HW_StorageType_E.HW_STORAGE_EMMC;
                }
                else if (Flashtool_connectResult.m_da_report.m_sdmmc_ret == MTK_Status.STATUS_E.S_DONE)
                {
                    Console.WriteLine(LogName + " HW_STROAGE_SDMMC");
                    m_storage_type = HW_StorageType_E.HW_STORAGE_SDMMC;
                }
                else if (Flashtool_connectResult.m_da_report.m_ufs_ret == MTK_Status.STATUS_E.S_DONE)
                {
                    Console.WriteLine(LogName + " HW_STROAGE_UFS");
                    Console.WriteLine(LogName + " m_ufs_vendor_id:" + Flashtool_connectResult.m_da_report.m_ufs_vendor_id);
                    Console.WriteLine(LogName + " m_ufs_cid:" + Flashtool_connectResult.m_da_report.m_ufs_cid);
                    Console.WriteLine(LogName + " m_ufs_fwver:" + Flashtool_connectResult.m_da_report.m_ufs_fwver);
                    Console.WriteLine(LogName + " m_ufs_lu0_size:" + Flashtool_connectResult.m_da_report.m_ufs_lu0_size);
                    Console.WriteLine(LogName + " m_ufs_lu1_size:" + Flashtool_connectResult.m_da_report.m_ufs_lu1_size);
                    Console.WriteLine(LogName + " m_ufs_lu2_size:" + Flashtool_connectResult.m_da_report.m_ufs_lu2_size);
                    m_storage_type = HW_StorageType_E.HW_STORAGE_UFS;
                    m_ufs_vendor_id = Flashtool_connectResult.m_da_report.m_ufs_vendor_id;
                    m_ufs_cid = Flashtool_connectResult.m_da_report.m_ufs_cid;
                    m_ufs_fwver = Flashtool_connectResult.m_da_report.m_ufs_fwver;
                }
                return num;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"LogName: {LogName} Error: {ex.Message}");
                //////FlashingDevice.UpdateDeviceStatus(LogName, null, ex.Message, "error", isDone: true);
                Console.WriteLine(LogName + ex.Message);
                return -1;
            }
        }

        public int DAConnect(int idx, short com_port_num)
        {
            Console.WriteLine($"LogName: {LogName} DAConnect begin id: {idx} com_port_num: {com_port_num}");
            //////FlashingDevice.UpdateDeviceStatus(LogName, null, "DAConnect begin idx: " + idx + " com_port_num: " + com_port_num, null, isDone: false);
            Console.WriteLine(LogName + "DAConnect begin idx: " + idx + " com_port_num: " + com_port_num);
            FlashTool_Connect_Arg p_arg = default(FlashTool_Connect_Arg);
            p_arg.m_com_ms_read_timeout = 268435455u;
            p_arg.m_com_ms_write_timeout = 268435455u;
            p_arg.m_boot_arg = BootArgSetting();
            CALLBACK_SECURITY_PRE_PROCESS_NOTIFY cALLBACK_SECURITY_PRE_PROCESS_NOTIFY = cb_security_pre_process_notify;
            p_arg.m_cb_security_pre_process_notify = cALLBACK_SECURITY_PRE_PROCESS_NOTIFY;
            p_arg.m_nor_chip_select = new HW_ChipSelect_E[2];
            p_arg.m_nor_chip_select[0] = HW_ChipSelect_E.CS_0;
            p_arg.m_nor_chip_select[1] = HW_ChipSelect_E.CS_WITH_DECODER;
            p_arg.m_p_dl_handle = MTK_DL.g_dl_handle;
            p_arg.m_conn_da_end_stage = CONN_DA_END_STAGE.SECOND_DA;
            p_arg.m_1st_da_enable_dram = false;
            p_arg.m_da_log_level = DA_LOG_LEVEL_E.DA_LOG_LEVEL_INFO;
            p_arg.m_da_log_channel = DA_LOG_CHANNEL_E.DA_LOG_CHANNEL_UART;
            int num = Marshal.SizeOf(typeof(FlashTool_Connect_Arg));

            Console.WriteLine($"LogName: {LogName} FlashTool_Connect_Arg size: {num}");
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_Arg size:" + num, null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Connect_Arg size:" + num);

            Console.WriteLine($"LogName: {LogName} BOOT_FLASHTOOL_ARG size: {Marshal.SizeOf(typeof(BOOT_FLASHTOOL_ARG))}");
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "BOOT_FLASHTOOL_ARG size:" + Marshal.SizeOf(typeof(BOOT_FLASHTOOL_ARG)), null, isDone: false);
            Console.WriteLine(LogName + "BOOT_FLASHTOOL_ARG size:" + Marshal.SizeOf(typeof(BOOT_FLASHTOOL_ARG)));
            IntPtr p_stopflag = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(int)));
            int num2 = -1;
            Console.WriteLine(LogName + "FlashTool_Connect_BROM begin");
            num2 = FlashTool_Connect_BROM(com_port_num, ref p_arg, ref g_ft_handle, p_stopflag);
            if (num2 != 0)
            {
                string str = MTK_Common.StatusToString(num2);
                str = MTK_Common.decodeOut(str);
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_BROM err" + str, "error", isDone: true);
                Console.WriteLine(LogName + "FlashTool_Connect_BROM err" + str);
                return num2;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_BROM i" + num2, null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Connect_BROM i" + num2);
            Flashtool_connectResult.m_da_report = default(DA_REPORT_T);
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_Download_DA begin.", null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Connect_Download_DA begin.");
            num2 = FlashTool_Connect_Download_DA(ref p_arg, ref g_ft_handle, ref Flashtool_connectResult, p_stopflag);
            if (num2 != 0)
            {
                string str2 = MTK_Common.StatusToString(num2);
                str2 = MTK_Common.decodeOut(str2);
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_Download_DA err" + str2, "error", isDone: true);
                Console.WriteLine(LogName + "FlashTool_Connect_Download_DA err" + str2);
                return num2;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_Download_DA i" + num2, null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Connect_Download_DA i" + num2);
            Console.WriteLine(LogName + "Flashtool_connectResult:");
            Console.WriteLine(LogName + "vboytest m_expected_da_major_ver:" + Flashtool_connectResult.m_da_report.m_expected_da_major_ver);
            Console.WriteLine(LogName + "vboytest m_expected_da_minor_ver:" + Flashtool_connectResult.m_da_report.m_expected_da_minor_ver);
            Console.WriteLine(LogName + "vboytest m_da_major_ver:" + Flashtool_connectResult.m_da_report.m_da_major_ver);
            Console.WriteLine(LogName + "vboytest m_da_minor_ver:" + Flashtool_connectResult.m_da_report.m_da_minor_ver);
            Console.WriteLine(LogName + "vboytest m_bbchip_type:" + Flashtool_connectResult.m_da_report.m_bbchip_type);
            Console.WriteLine(LogName + "vboytest m_bbchip_name:" + Flashtool_connectResult.m_da_report.m_bbchip_name);
            Console.WriteLine(LogName + "vboytest m_bbchip_hw_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_hw_ver);
            Console.WriteLine(LogName + "vboytest m_bbchip_hw_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_hw_ver);
            Console.WriteLine(LogName + "vboytest m_bbchip_sw_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_sw_ver);
            Console.WriteLine(LogName + "vboytest m_bbchip_hw_code:" + Flashtool_connectResult.m_da_report.m_bbchip_hw_code);
            Console.WriteLine(LogName + "vboytest m_bbchip_secure_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_secure_ver);
            Console.WriteLine(LogName + "vboytest m_bbchip_bl_ver:" + Flashtool_connectResult.m_da_report.m_bbchip_bl_ver);
            Console.WriteLine(LogName + "vboytest m_ext_clock:" + Flashtool_connectResult.m_da_report.m_ext_clock);
            Console.WriteLine(LogName + "vboytest m_nand_ret:" + Flashtool_connectResult.m_da_report.m_nand_ret);
            Console.WriteLine(LogName + "vboytest m_emmc_ret:" + Flashtool_connectResult.m_da_report.m_emmc_ret);
            Console.WriteLine(LogName + "vboytest m_sdmmc_ret:" + Flashtool_connectResult.m_da_report.m_sdmmc_ret);
            Console.WriteLine(LogName + "vboytest m_ufs_ret:" + Flashtool_connectResult.m_da_report.m_ufs_ret);
            if (Flashtool_connectResult.m_da_report.m_nor_ret == MTK_Status.STATUS_E.S_DONE)
            {
                Console.WriteLine(LogName + "vboytest HW_STROAGE_NOR");
                m_storage_type = HW_StorageType_E.HW_STORAGE_NOR;
            }
            else if (Flashtool_connectResult.m_da_report.m_nand_ret == MTK_Status.STATUS_E.S_DONE)
            {
                Console.WriteLine(LogName + "vboytest HW_STROAGE_NAND");
                m_storage_type = HW_StorageType_E.HW_STORAGE_NAND;
            }
            else if (Flashtool_connectResult.m_da_report.m_emmc_ret == MTK_Status.STATUS_E.S_DONE)
            {
                Console.WriteLine(LogName + "vboytest HW_STROAGE_EMMC");
                m_storage_type = HW_StorageType_E.HW_STORAGE_EMMC;
            }
            else if (Flashtool_connectResult.m_da_report.m_sdmmc_ret == MTK_Status.STATUS_E.S_DONE)
            {
                Console.WriteLine(LogName + "vboytest HW_STROAGE_SDMMC");
                m_storage_type = HW_StorageType_E.HW_STORAGE_SDMMC;
            }
            else if (Flashtool_connectResult.m_da_report.m_ufs_ret == MTK_Status.STATUS_E.S_DONE)
            {
                Console.WriteLine(LogName + "vboytest HW_STROAGE_UFS");
                m_storage_type = HW_StorageType_E.HW_STORAGE_UFS;
            }
            return num2;
        }

        public int DADisConnect(int idx)
        {
            int num = FlashTool_Disconnect(ref g_ft_handle);
            if (num != 0)
            {
                try
                {
                    string str = MTK_Common.StatusToString(num);
                    str = MTK_Common.decodeOut(str);
                    Console.WriteLine(LogName + "FlashTool_Disconnect err" + str);
                }
                catch(Exception ex)
                {
                    Console.WriteLine(LogName + "FlashTool_Disconnect ex" + ex.Message);
                }
                return num;
            }
            Console.WriteLine(LogName + "FlashTool_Disconnect i" + num);
            return num;
        }

        public int Download(int idx)
        {
            int num = -1;
            FlashTool_Download_Arg p_dl_arg = default(FlashTool_Download_Arg);
            p_dl_arg.m_dl_handle = MTK_DL.g_dl_handle;
            p_dl_arg.m_dl_handle_list = MTK_DL.g_dl_handle_list;
            p_dl_arg.m_cb_da_report = cb_da_report;
            p_dl_arg.m_download_accuracy = ACCURACY.ACCURACY_AUTO;
            p_dl_arg.m_cb_download_flash_init = cb_download_process_init;
            p_dl_arg.m_cb_download_flash = cb_download_process;
            p_dl_arg.m_cb_download_bloader_init = cb_bootloader_download_process_init;
            p_dl_arg.m_cb_download_bloader = cb_bootloader_download_process;
            p_dl_arg.m_cb_checksum_init = cb_checksum_process_init;
            p_dl_arg.m_cb_checksum = cb_checksum_process;
            p_dl_arg.m_enable_bbchip_ver_check = false;
            p_dl_arg.m_enable_bbchip_ver_check = false;
            p_dl_arg.m_downloadstyle_sequential = false;
            FlashTool_Download_Result p_dl_result = default(FlashTool_Download_Result);
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Download begin", null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Download begin");
            num = FlashTool_Download(g_ft_handle, ref p_dl_arg, ref p_dl_result);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Download err", "error", isDone: true);
                Console.WriteLine(LogName + "FlashTool_Download err" + str);
                return num;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Download i" + num, null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Download i" + num);
            return num;
        }

        public BOOT_FLASHTOOL_ARG BootArgSetting()
        {
            BOOT_FLASHTOOL_ARG result = default(BOOT_FLASHTOOL_ARG);
            result.m_bbchip_type = BBCHIP_TYPE.AUTO_DETECT_BBCHIP;
            result.m_ext_clock = EXT_CLOCK.AUTO_DETECT_EXT_CLOCK;
            result.m_baudrate = 115200u;
            result.m_ms_boot_timeout = 268435455u;
            result.m_max_start_cmd_retry_count = 1u;
            CALLBACK_COM_INIT_STAGE cALLBACK_COM_INIT_STAGE = cb_com_init_stage;
            result.m_cb_com_init_stage = cALLBACK_COM_INIT_STAGE;
            CALLBACK_IN_BROM_STAGE cALLBACK_IN_BROM_STAGE = cb_in_brom_stage;
            result.m_cb_in_brom_stage = cALLBACK_IN_BROM_STAGE;
            result.m_speedup_brom_baudrate = true;
            result.m_auth_handle = MTK_AUTH.g_auth_handle;
            result.m_scert_handle = MTK_SCERT.g_scert_handle;
            result.m_cb_sla_challenge = SLA_Challenge;
            result.m_cb_sla_challenge_end = SLA_Challenge_End;
            result.m_da_handle = MTK_DA.g_da_handle;
            CALLBACK_WRITE_BUF_PROGRESS_INIT cALLBACK_WRITE_BUF_PROGRESS_INIT = cb_download_da_init;
            result.m_cb_download_da_init = cALLBACK_WRITE_BUF_PROGRESS_INIT;
            CALLBACK_WRITE_BUF_PROGRESS cALLBACK_WRITE_BUF_PROGRESS = cb_download_da;
            result.m_cb_download_da = cALLBACK_WRITE_BUF_PROGRESS;
            result.m_usb_enable = false;
            result.m_bmt_block_count = 80u;
            return result;
        }

        public int encrypt(int plainText, int key)
        {
            return plainText ^ key;
        }

        public int WriteATMBootModeFlag(int idx, List<MTK_DL.ROM_INFO> rom_info_list)
        {
            int num = 0;
            int num2 = 1;
            int num3 = encrypt(87654321 + num2, 87979987);
            string text = num3.ToString();
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cipherText: " + num3 + " atmflag: " + text, null, isDone: false);
            Console.WriteLine(LogName + "cipherText: " + num3 + " atmflag: " + text);
            num = WriteMemory(idx, 533uL, text, rom_info_list);
            if (num != 0)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "WriteMemory err", "error", isDone: true);
                Console.WriteLine(LogName + "WriteMemory err");
                return num;
            }
            return num;
        }

        public int WriteMemory(int idx, ulong offset, string set_flag, List<MTK_DL.ROM_INFO> rom_info_list)
        {
            int num = 0;
            WriteFlashMemoryParameter parameter = default(WriteFlashMemoryParameter);
            parameter.m_flash_type = m_storage_type;
            parameter.m_addressing_mode = AddressingMode.AddressingMode_LogicalAddress;
            foreach (MTK_DL.ROM_INFO item in rom_info_list)
            {
                if (item.name.Equals("proinfo"))
                {
                    ////FlashingDevice.UpdateDeviceStatus(LogName, null, $"proinfo.begin_addr {item.begin_addr}", null, isDone: false);
                    Console.WriteLine(LogName + $"proinfo.begin_addr {item.begin_addr}");
                    parameter.m_address = item.begin_addr + offset;
                    ////FlashingDevice.UpdateDeviceStatus(LogName, null, "WFM_arg.m_address " + parameter.m_address, null, isDone: false);
                    Console.WriteLine(LogName + "WFM_arg.m_address " + parameter.m_address);
                    break;
                }
            }
            parameter.m_container_length = 0u;
            parameter.m_input_mode = InputMode.InputMode_FromBuffer;
            parameter.m_input = set_flag;
            parameter.m_input_length = (ulong)Encoding.Default.GetBytes(set_flag).Length;
            if (m_storage_type == HW_StorageType_E.HW_STORAGE_EMMC)
            {
                parameter.m_part_id = 8u;
            }
            else if (m_storage_type == HW_StorageType_E.HW_STORAGE_UFS)
            {
                parameter.m_part_id = 3u;
            }
            parameter.m_cb_progress = cb_progress;
            parameter.m_program_mode = ProgramMode.ProgramMode_PageOnly;
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "m_flash_type: " + parameter.m_flash_type, null, isDone: false);
            Console.WriteLine(LogName + "m_flash_type: " + parameter.m_flash_type);
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "m_input: " + parameter.m_input, null, isDone: false);
            Console.WriteLine(LogName + "m_input: " + parameter.m_input);
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "m_input_length: " + parameter.m_input_length, null, isDone: false);
            Console.WriteLine(LogName + "m_input_length: " + parameter.m_input_length);
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "m_part_id: " + parameter.m_part_id, null, isDone: false);
            Console.WriteLine(LogName + "m_part_id: " + parameter.m_part_id);
            num = FlashTool_WriteFlashMemory(g_ft_handle, ref parameter);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_WriteFlashMemory err" + str, "error", isDone: true);
                Console.WriteLine(LogName + "FlashTool_WriteFlashMemory err" + str);
                return num;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_WriteFlashMemory status_code:" + num, null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_WriteFlashMemory status_code:" + num);
            return num;
        }

        public void DoReboot(int idx)
        {
            FlashTool_EnableWDT_Arg flashTool_EnableWDT_Arg = default(FlashTool_EnableWDT_Arg);
            flashTool_EnableWDT_Arg.m_timeout_ms = 3000u;
            flashTool_EnableWDT_Arg.m_async = false;
            flashTool_EnableWDT_Arg.m_reboot = false;
            FlashTool_EnableWDT_Arg p_arg = flashTool_EnableWDT_Arg;
            int num = FlashTool_EnableWatchDogTimeout(g_ft_handle, ref p_arg);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, $"FlashTool_EnableWatchDogTimeout({p_arg.m_timeout_ms}) failed!, error code: {str}({num})", "error", isDone: true);
                Console.WriteLine(LogName + $"FlashTool_EnableWatchDogTimeout({p_arg.m_timeout_ms}) failed!, error code: {str}({num})");
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_EnableWatchDogTimeout Succeeded.", null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_EnableWatchDogTimeout Succeeded.");
        }

        public ushort VidToInt(string strVid)
        {
            return strVid switch
            {
                "SAMSUNG" => 462,
                "SKhynix" => 429,
                "MICRON" => 300,
                "WDC" => 325,
                "TOSHIBA" => 408,
                "YMTC" => 2715,
                _ => 0,
            };
        }

        public int DoFfu(out bool needFirmwarewrite)
        {
            string text = "";
            int num = 0;
            needFirmwarewrite = false;
            foreach (Ffu ffu in SaveFfu.ffuList)
            {
                Console.WriteLine(LogName + $"item.Name[{ffu.Name}],item.Version[{ffu.Version}],item.Number[{ffu.Number}]");
                if (VidToInt(ffu.Name) != m_ufs_vendor_id)
                {
                    continue;
                }
                Console.WriteLine(LogName + $"Name match;item.Name[{ffu.Name}],item.Version[{ffu.Version}],item.Number[{ffu.Number}]");
                if (ffu.Version == m_ufs_cid.TrimEnd())
                {
                    Console.WriteLine(LogName + $"Version match;item.Name[{ffu.Name}],item.Version[{ffu.Version}],item.Number[{ffu.Number}]");
                    if (ffu.Number == m_ufs_fwver.TrimEnd())
                    {
                        Console.WriteLine(LogName + $"Number match;item.Name[{ffu.Name}],item.Version[{ffu.Version}],item.Number[{ffu.Number}]");
                        text = ffu.File;
                        needFirmwarewrite = true;
                        break;
                    }
                }
            }
            Console.WriteLine(LogName + "ffu neeFdirmwarewrite: " + needFirmwarewrite);
            if (needFirmwarewrite && !string.IsNullOrEmpty(text))
            {
                string text2 = MiAppConfig.Get("ffuPath").ToString() + "\\" + text;
                if (!File.Exists(text2))
                {
                    throw new Exception($"file {text2} not found.");
                }
                num = FlashTool_FirmwareUpdate(g_ft_handle, Encoding.Default.GetBytes(text2), null, 0u);
                if (num != 0)
                {
                    Console.WriteLine(LogName + "FlashTool_FirmwareUpdate err" + num);
                    MTK_Status.STATUS_E sTATUS_E = MTK_Common.IntConvertToEnum((uint)num);
                    Console.WriteLine(LogName + "FlashTool_FirmwareUpdate err" + sTATUS_E);
                    string str = MTK_Common.StatusToString(num);
                    str = MTK_Common.decodeOut(str);
                    Console.WriteLine(LogName + "FlashTool_FirmwareUpdate err" + str);
                    return num;
                }
                Console.WriteLine(LogName + "FlashTool_FirmwareUpdate " + text2 + " successfully");
            }
            return num;
        }

        public int DoWB()
        {
            uint result = 0u;
            uint result2 = 0u;
            int num = 0;
            bool flag = false;
            if (SaveFfu.isNeedWBprovision)
            {
                foreach (Wb wb in SaveFfu.wbList)
                {
                    if (VidToInt(wb.Vendor) == m_ufs_vendor_id && m_ufs_cid.Contains(wb.Partnumber))
                    {
                        uint.TryParse(wb.Wb_size_in_MB, out result);
                        result /= 1024;
                        uint.TryParse(wb.Hpb_total_range_in_MB, out result2);
                        result2 /= 1024;
                        flag = true;
                        break;
                    }
                }
                if (flag)
                {
                    if (result != 0)
                    {
                        Console.WriteLine(LogName + "begin do tw_size");
                        UFS_Config cfg = default(UFS_Config);
                        cfg.force_provision = 1u;
                        cfg.tw_size_gb = result;
                        cfg.tw_no_red = 1u;
                        cfg.hpb_size_gb = result2;
                        Console.WriteLine(LogName + $"FlashTool_SetUFSConfig with tw_size ={result}GB,hpb_size={result2}GB");
                        num = FlashTool_SetUFSConfig(g_ft_handle, ref cfg);
                        if (num != 0)
                        {
                            Console.WriteLine(LogName + "FlashTool_SetUFSConfig err" + num);
                            MTK_Status.STATUS_E sTATUS_E = MTK_Common.IntConvertToEnum((uint)num);
                            Console.WriteLine(LogName + "FlashTool_SetUFSConfig err" + sTATUS_E);
                            return num;
                        }
                        Console.WriteLine(LogName + $"FlashTool_SetUFSConfig with tw_size ={result}GB,hpb_size={result2}GB successfully");
                    }
                    else
                    {
                        Console.WriteLine(LogName + "FlashTool_SetUFSConfig " + result + " is 0,no need do tw_size");
                    }
                    return num;
                }
                Console.WriteLine(LogName + "error： wb not match,can not get tw_size");
                return -1;
            }
            Console.WriteLine(LogName + "no need do wb");
            return 0;
        }

        public int DoFormatAll()
        {
            callbacks_struct_t callbacks_struct_t = default(callbacks_struct_t);
            callbacks_struct_t.cb_op_progress = cb_operation_progress;
            callbacks_struct_t.cb_notify_stop = cb_notify_stop;
            callbacks_struct_t.cb_stage_message = cb_statge_message;
            callbacks_struct_t.cb_sla = new sla_callbacks_t
            {
                cb_start = SLA_Challenge,
                cb_end = SLA_Challenge_End
            };
            callbacks_struct_t callbacks = callbacks_struct_t;
            int num = FlashTool_Format_Partition(g_ft_handle, "all", ref callbacks);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_Download_DA err", "error", isDone: true);
                Console.WriteLine(LogName + "FlashTool_Connect_Download_DA err" + str);
                return num;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format_Partition success.", null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Format_Partition success." + num);
            return num;
        }

        public int DoFormatFRP()
        {
            callbacks_struct_t callbacks_struct_t = default(callbacks_struct_t);
            callbacks_struct_t.cb_op_progress = cb_operation_progress;
            callbacks_struct_t.cb_notify_stop = cb_notify_stop;
            callbacks_struct_t.cb_stage_message = cb_statge_message;
            callbacks_struct_t.cb_sla = new sla_callbacks_t
            {
                cb_start = SLA_Challenge,
                cb_end = SLA_Challenge_End
            };
            callbacks_struct_t callbacks = callbacks_struct_t;
            int num = FlashTool_Format_Partition(g_ft_handle, "frp", ref callbacks);
            if (num != 0)
            {
                string str = MTK_Common.StatusToString(num);
                str = MTK_Common.decodeOut(str);
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Connect_Download_DA err", "error", isDone: true);
                Console.WriteLine(LogName + "FlashTool_Connect_Download_DA err" + str);
                return num;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format_Partition success.", null, isDone: false);
            Console.WriteLine(LogName + "FlashTool_Format_Partition success." + num);
            return num;
        }

        public int DoFormatFRP2()
        {
            Console.WriteLine(LogName + " DoFormatFRP begin");
            FlashTool_Format_Arg flashTool_Format_Arg = default(FlashTool_Format_Arg);
            flashTool_Format_Arg.m_storage_type = m_storage_type;
            flashTool_Format_Arg.m_format_cfg.m_auto_format_fat = false;
            flashTool_Format_Arg.m_format_cfg.m_validation = true;
            flashTool_Format_Arg.m_format_cfg.m_AddrType = NUTL_AddrTypeFlag_E.NUTL_ADDR_PHYSICAL;
            flashTool_Format_Arg.m_format_cfg.m_format_begin_addr = 81297408;
            flashTool_Format_Arg.m_format_cfg.m_format_length = 1048576;
            flashTool_Format_Arg.m_format_cfg.m_part_id = 7u;
            int num = FM_ComBoEmmc_UFS_ALL();
            if (num != 0)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FM_ComBoEmmc_UFS_ALL err", "error", isDone: true);
                Console.WriteLine(LogName + "FM_ComBoEmmc_UFS_ALL err");
                return num;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FM_ComBoEmmc_UFS_ALL success.", null, isDone: false);
            Console.WriteLine(LogName + "FM_ComBoEmmc_UFS_ALL success." + num);
            return num;
        }

        public int DoFormatAllNew()
        {
            Console.WriteLine(LogName + "DoFormatAllNew begin");
            FlashTool_Format_Arg flashTool_Format_Arg = default(FlashTool_Format_Arg);
            flashTool_Format_Arg.m_storage_type = m_storage_type;
            flashTool_Format_Arg.m_format_cfg.m_auto_format_fat = false;
            flashTool_Format_Arg.m_format_cfg.m_validation = true;
            flashTool_Format_Arg.m_format_cfg.m_AddrType = NUTL_AddrTypeFlag_E.NUTL_ADDR_PHYSICAL;
            flashTool_Format_Arg.m_format_cfg.m_format_begin_addr = 0uL;
            int num = FM_ComBoEmmc_UFS_ALL();
            if (num != 0)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FM_ComBoEmmc_UFS_ALL err", "error", isDone: true);
                Console.WriteLine(LogName + "FM_ComBoEmmc_UFS_ALL err");
                return num;
            }
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FM_ComBoEmmc_UFS_ALL success.", null, isDone: false);
            Console.WriteLine(LogName + "FM_ComBoEmmc_UFS_ALL success." + num);
            return num;
        }

        public int FM_ComBoEmmc_UFS_ALL()
        {
            Console.WriteLine(LogName + "FM_ComBoEmmc_UFS_ALL begin");
            int num = 0;
            FlashTool_Format_Arg p_fmt_arg = default(FlashTool_Format_Arg);
            FlashTool_Format_Result p_fmt_result = default(FlashTool_Format_Result);
            p_fmt_arg.m_storage_type = m_storage_type;
            p_fmt_arg.m_format_cfg.m_auto_format_fat = false;
            p_fmt_arg.m_format_cfg.m_validation = true;
            p_fmt_arg.m_format_cfg.m_AddrType = NUTL_AddrTypeFlag_E.NUTL_ADDR_LOGICAL;
            p_fmt_arg.m_format_cfg.m_format_begin_addr = 0uL;
            p_fmt_arg.m_erase_flag = NUTL_EraseFlag_E.NUTL_ERASE;
            CALLBACK_FORMAT_PROGRESS_INIT cb_format_report_init = cb_format_progress_init;
            p_fmt_arg.m_cb_format_report_init = cb_format_report_init;
            CALLBACK_FORMAT_PROGRESS cb_format_report = cb_format_progress;
            p_fmt_arg.m_cb_format_report = cb_format_report;
            CALLBACK_FORMAT_STATISTICS cALLBACK_FORMAT_STATISTICS = cb_format_statistics;
            p_fmt_arg.m_cb_format_statistics = cALLBACK_FORMAT_STATISTICS;
            if (m_storage_type == HW_StorageType_E.HW_STORAGE_UFS)
            {
                if (Flashtool_connectResult.m_da_report.m_ufs_lu0_size != 0)
                {
                    p_fmt_arg.m_format_cfg.m_part_id = 1u;
                    p_fmt_arg.m_format_cfg.m_format_length = Flashtool_connectResult.m_da_report.m_ufs_lu0_size;
                    Console.WriteLine(LogName + "UFS_PART_LU0 Flashtool_formatarg.m_format_cfg.m_part_id: " + p_fmt_arg.m_format_cfg.m_part_id);
                    Console.WriteLine(LogName + "UFS_PART_LU0 Flashtool_formatarg.m_format_cfg.m_format_length: " + p_fmt_arg.m_format_cfg.m_format_length);
                    num = FlashTool_Format(g_ft_handle, ref p_fmt_arg, ref p_fmt_result);
                    if (num != 0)
                    {
                        string str = MTK_Common.StatusToString(num);
                        str = MTK_Common.decodeOut(str);
                        ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format err", "error", isDone: true);
                        Console.WriteLine(LogName + "FlashTool_Format err" + str);
                        return num;
                    }
                }
                if (Flashtool_connectResult.m_da_report.m_ufs_lu1_size != 0)
                {
                    p_fmt_arg.m_format_cfg.m_part_id = 2u;
                    p_fmt_arg.m_format_cfg.m_format_length = Flashtool_connectResult.m_da_report.m_ufs_lu1_size;
                    Console.WriteLine(LogName + "UFS_PART_LU1 Flashtool_formatarg.m_format_cfg.m_part_id: " + p_fmt_arg.m_format_cfg.m_part_id);
                    Console.WriteLine(LogName + "UFS_PART_LU1 Flashtool_formatarg.m_format_cfg.m_format_length: " + p_fmt_arg.m_format_cfg.m_format_length);
                    num = FlashTool_Format(g_ft_handle, ref p_fmt_arg, ref p_fmt_result);
                    if (num != 0)
                    {
                        string str2 = MTK_Common.StatusToString(num);
                        str2 = MTK_Common.decodeOut(str2);
                        ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format err", "error", isDone: true);
                        Console.WriteLine(LogName + "FlashTool_Format err" + str2);
                        return num;
                    }
                }
                if (Flashtool_connectResult.m_da_report.m_ufs_lu2_size != 0)
                {
                    p_fmt_arg.m_format_cfg.m_part_id = 3u;
                    p_fmt_arg.m_format_cfg.m_format_length = Flashtool_connectResult.m_da_report.m_ufs_lu2_size;
                    Console.WriteLine(LogName + "UFS_PART_LU2 Flashtool_formatarg.m_format_cfg.m_part_id: " + p_fmt_arg.m_format_cfg.m_part_id);
                    Console.WriteLine(LogName + "UFS_PART_LU2 Flashtool_formatarg.m_format_cfg.m_format_length: " + p_fmt_arg.m_format_cfg.m_format_length);
                    num = FlashTool_Format(g_ft_handle, ref p_fmt_arg, ref p_fmt_result);
                    if (num != 0)
                    {
                        string str3 = MTK_Common.StatusToString(num);
                        str3 = MTK_Common.decodeOut(str3);
                        ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format err", "error", isDone: true);
                        Console.WriteLine(LogName + "FlashTool_Format err" + str3);
                        return num;
                    }
                }
            }
            else if (m_storage_type == HW_StorageType_E.HW_STORAGE_EMMC)
            {
                if (Flashtool_connectResult.m_da_report.m_emmc_boot1_size != 0)
                {
                    p_fmt_arg.m_format_cfg.m_part_id = 1u;
                    p_fmt_arg.m_format_cfg.m_format_length = Flashtool_connectResult.m_da_report.m_emmc_boot1_size;
                    Console.WriteLine(LogName + "EMMC_PART_BOOT1 Flashtool_formatarg.m_format_cfg.m_part_id: " + p_fmt_arg.m_format_cfg.m_part_id);
                    Console.WriteLine(LogName + "EMMC_PART_BOOT1 Flashtool_formatarg.m_format_cfg.m_format_length: " + p_fmt_arg.m_format_cfg.m_format_length);
                    num = FlashTool_Format(g_ft_handle, ref p_fmt_arg, ref p_fmt_result);
                    if (num != 0)
                    {
                        string str4 = MTK_Common.StatusToString(num);
                        str4 = MTK_Common.decodeOut(str4);
                        ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format err", "error", isDone: true);
                        Console.WriteLine(LogName + "FlashTool_Format err" + str4);
                        return num;
                    }
                }
                if (Flashtool_connectResult.m_da_report.m_emmc_boot2_size != 0)
                {
                    p_fmt_arg.m_format_cfg.m_part_id = 2u;
                    p_fmt_arg.m_format_cfg.m_format_length = Flashtool_connectResult.m_da_report.m_emmc_boot2_size;
                    Console.WriteLine(LogName + "EMMC_PART_BOOT2 Flashtool_formatarg.m_format_cfg.m_part_id: " + p_fmt_arg.m_format_cfg.m_part_id);
                    Console.WriteLine(LogName + "EMMC_PART_BOOT2 Flashtool_formatarg.m_format_cfg.m_format_length: " + p_fmt_arg.m_format_cfg.m_format_length);
                    num = FlashTool_Format(g_ft_handle, ref p_fmt_arg, ref p_fmt_result);
                    if (num != 0)
                    {
                        string str5 = MTK_Common.StatusToString(num);
                        str5 = MTK_Common.decodeOut(str5);
                        ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format err", "error", isDone: true);
                        Console.WriteLine(LogName + "FlashTool_Format err" + str5);
                        return num;
                    }
                }
                if (Flashtool_connectResult.m_da_report.m_emmc_ua_size != 0)
                {
                    p_fmt_arg.m_format_cfg.m_part_id = 8u;
                    p_fmt_arg.m_format_cfg.m_format_length = Flashtool_connectResult.m_da_report.m_emmc_ua_size;
                    Console.WriteLine(LogName + "EMMC_PART_USER Flashtool_formatarg.m_format_cfg.m_part_id: " + p_fmt_arg.m_format_cfg.m_part_id);
                    Console.WriteLine(LogName + "EMMC_PART_USER Flashtool_formatarg.m_format_cfg.m_format_length: " + p_fmt_arg.m_format_cfg.m_format_length);
                    num = FlashTool_Format(g_ft_handle, ref p_fmt_arg, ref p_fmt_result);
                    if (num != 0)
                    {
                        string str6 = MTK_Common.StatusToString(num);
                        str6 = MTK_Common.decodeOut(str6);
                        ////FlashingDevice.UpdateDeviceStatus(LogName, null, "FlashTool_Format err", "error", isDone: true);
                        Console.WriteLine(LogName + "FlashTool_Format err" + str6);
                        return num;
                    }
                }
            }
            return num;
        }

        public void cb_operation_progress(IntPtr _this, transfer_phase phase, uint progress, ulong data_xferd, ulong data_total, ref cbs_additional_info additional_info)
        {
            uint num = 0u;
            if (progress % 10 == 0 && num != progress)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_operation_progress called~~~~~~", null, isDone: false);
                Console.WriteLine(LogName + "cb_operation_progress called~~~~~~");
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "Progress: " + progress, null, isDone: false);
                Console.WriteLine(LogName + "Progress: " + progress);
                num = progress;
            }
        }

        public bool cb_notify_stop(IntPtr _this)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_notify_stop called~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_notify_stop called~~~~~~");
            return false;
        }

        public void cb_statge_message(IntPtr _this, string msg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, $"cb_statge_message called: {msg}~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + $"cb_statge_message called: {msg}~~~~~~");
        }

        private int cb_security_pre_process_notify(IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_download_da called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_security_pre_process_notify called~~~~~~~~~");
            return 0;
        }

        private int cb_com_init_stage(IntPtr hCom, IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_security_pre_process_notify called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_com_init_stage called~~~~~~~~~");
            return 0;
        }

        private int cb_in_brom_stage(uint brom_handle, IntPtr hCom, IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_in_brom_stage called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_in_brom_stage called~~~~~~~~~");
            return 0;
        }

        private int cb_download_da_init(IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_download_da_init called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_download_da_init called~~~~~~~~~");
            return 0;
        }

        private int cb_download_da(byte finished_percentage, uint finished_bytes, uint total_bytes, IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_download_da called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_download_da called~~~~~~~~~");
            return 0;
        }

        private int cb_da_report(ref DA_REPORT_T p_da_report, IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_da_report called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_da_report called~~~~~~~~~");
            if (p_da_report.m_nor_ret == MTK_Status.STATUS_E.S_DONE)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "HW_STROAGE_NOR", null, isDone: false);
                Console.WriteLine(LogName + "HW_STROAGE_NOR");
            }
            else if (p_da_report.m_nand_ret == MTK_Status.STATUS_E.S_DONE)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "HW_STROAGE_NAND", null, isDone: false);
                Console.WriteLine(LogName + "HW_STROAGE_NAND");
            }
            else if (p_da_report.m_emmc_ret == MTK_Status.STATUS_E.S_DONE)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "HW_STROAGE_EMMC", null, isDone: false);
                Console.WriteLine(LogName + "HW_STROAGE_EMMC");
            }
            else if (p_da_report.m_sdmmc_ret == MTK_Status.STATUS_E.S_DONE)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "HW_STROAGE_SDMMC", null, isDone: false);
                Console.WriteLine(LogName + "HW_STROAGE_SDMMC");
            }
            else if (p_da_report.m_ufs_ret == MTK_Status.STATUS_E.S_DONE)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, "HW_STROAGE_UFS", null, isDone: false);
                Console.WriteLine(LogName + "HW_STROAGE_UFS");
            }
            return 0;
        }

        private int cb_download_process_init(IntPtr usr_arg, string image_name)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, $"{image_name} begin download", null, isDone: false);
            Console.WriteLine(LogName + $" {image_name} begin download");
            frm.SetProgressBar(0);
            return 0;
        }

        Form1 frm = new Form1();

        private int cb_download_process(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg)
        {
            if (finished_percentage % 25 == 0)
            {
                ////FlashingDevice.UpdateDeviceStatus(LogName, null, $"finished_percentage:{finished_percentage},finished_bytes:{finished_bytes},total_bytes:{total_bytes}", null, isDone: false);
                Console.WriteLine(LogName + $"finished_percentage:{finished_percentage},finished_bytes:{finished_bytes},total_bytes:{total_bytes}");
                frm.SetProgressBar(finished_percentage);
            }
            return 0;
        }

        private int cb_bootloader_download_process_init(IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_bootloader_download_process_init called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_bootloader_download_process_init called~~~~~~~~~");
            return 0;
        }

        private int cb_bootloader_download_process(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_bootloader_download_process called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_bootloader_download_process called~~~~~~~~~");
            return 0;
        }

        private int cb_checksum_process_init(IntPtr usr_arg, string image_name)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_checksum_process_init called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_checksum_process_init called~~~~~~~~~");
            return 0;
        }

        private int cb_checksum_process(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_checksum_process called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_checksum_process called~~~~~~~~~");
            return 0;
        }

        private int cb_security_post_process_notify(IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_security_post_process_notify called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_security_post_process_notify called~~~~~~~~~");
            return 0;
        }

        private int cb_progress(byte finished_percentage, ulong finished_bytes, ulong total_bytes, IntPtr usr_arg)
        {
            ////FlashingDevice.UpdateDeviceStatus(LogName, null, "cb_progress called~~~~~~~~~", null, isDone: false);
            Console.WriteLine(LogName + "cb_progress called~~~~~~~~~");
            return 0;
        }

        private int cb_format_progress_init(HW_StorageType_E storage_type, ulong begin_addr, ulong length, IntPtr usr_arg)
        {
            Console.WriteLine(LogName + "cb_format_progress_init called~~~~~~~~~");
            Console.WriteLine(LogName + string.Concat("storage_type:", storage_type, " begin_addr: ", begin_addr, " length: ", length));
            return 0;
        }

        private int cb_format_progress(byte finished_percentage, IntPtr usr_arg)
        {
            Console.WriteLine(LogName + "cb_format_progress called~~~~~~~~~");
            Console.WriteLine(LogName + "finished_percentage:" + finished_percentage);
            return 0;
        }

        private int cb_format_statistics(ref FormatStatisticsReport_T p_report, IntPtr usr_arg)
        {
            Console.WriteLine(LogName + "cb_format_statistics called~~~~~~~~~");
            return 0;
        }

        private string byteToHexStr(byte[] bytes, int length)
        {
            string text = "";
            if (bytes != null)
            {
                for (int i = 0; i < length; i++)
                {
                    text += bytes[i].ToString("X2");
                }
            }
            return text;
        }

        private int cb_SLA_Challenge_End(IntPtr usr_arg, IntPtr p_challenge_out)
        {
            Console.WriteLine(LogName + "cb_SLA_Challenge_End called~~~~~~~~~");
            return 0;
        }

        /// <summary>
        /// Read partition information from connected device
        /// </summary>
        /// <returns>List of partition information, null if failed</returns>
        public List<PartitionInfo> ReadPartitions()
        {
            try
            {
                LogInfo("Reading partition information from device...");

                uint partitionCount = 0;
                int result = FlashTool_ReadPartitionCount(g_ft_handle, ref partitionCount);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to read partition count: {errorMsg} (Code: {result})");
                    return null;
                }

                if (partitionCount == 0)
                {
                    LogWarning("No partitions found on device");
                    return new List<PartitionInfo>();
                }

                LogInfo($"Found {partitionCount} partitions on device");

                // Allocate memory for partition info array
                int structSize = Marshal.SizeOf(typeof(PartitionInfo));
                IntPtr partitionInfoPtr = Marshal.AllocHGlobal((int)(structSize * partitionCount));

                try
                {
                    result = FlashTool_ReadPartitionInfo(g_ft_handle, partitionInfoPtr, partitionCount);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to read partition info: {errorMsg} (Code: {result})");
                        return null;
                    }

                    // Convert native array to managed list
                    List<PartitionInfo> partitions = new List<PartitionInfo>();
                    for (int i = 0; i < partitionCount; i++)
                    {
                        IntPtr currentPtr = new IntPtr(partitionInfoPtr.ToInt64() + (structSize * i));
                        PartitionInfo partition = (PartitionInfo)Marshal.PtrToStructure(currentPtr, typeof(PartitionInfo));
                        partitions.Add(partition);
                    }

                    LogInfo($"Successfully read {partitions.Count} partitions");
                    return partitions;
                }
                finally
                {
                    Marshal.FreeHGlobal(partitionInfoPtr);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in ReadPartitions: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Reboot device to META mode
        /// </summary>
        /// <param name="timeoutMs">Timeout in milliseconds (default: 10000)</param>
        /// <param name="asyncMode">Async mode (default: false)</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RebootToMetaMode(uint timeoutMs = 10000, bool asyncMode = false)
        {
            try
            {
                LogInfo($"Rebooting device to META mode (timeout: {timeoutMs}ms, async: {asyncMode})...");

                MetaBootArg metaArg = new MetaBootArg
                {
                    timeout_ms = timeoutMs,
                    async_mode = asyncMode,
                    reboot_after = true
                };

                ExternalMemoryConfig extMemConfig = new ExternalMemoryConfig();
                int stopFlag = 0;

                int result = Boot_META(g_ft_handle, ref metaArg, ref extMemConfig, ref stopFlag);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to reboot to META mode: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo("Device successfully rebooted to META mode");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in RebootToMetaMode: {ex.Message}");
                return false;
            }
        }

        #region Partition Operations

        /// <summary>
        /// Read specific partition from device
        /// </summary>
        /// <param name="partitionName">Name of partition to read</param>
        /// <param name="outputFile">Output file path</param>
        /// <returns>True if successful</returns>
        public bool ReadPartition(string partitionName, string outputFile)
        {
            try
            {
                LogInfo($"Reading partition '{partitionName}' to file: {outputFile}");

                if (string.IsNullOrEmpty(partitionName))
                {
                    LogError("Partition name cannot be empty");
                    return false;
                }

                if (string.IsNullOrEmpty(outputFile))
                {
                    LogError("Output file path cannot be empty");
                    return false;
                }

                // Ensure output directory exists
                string outputDir = Path.GetDirectoryName(outputFile);
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                int result = FlashTool_ReadPartition(g_ft_handle, partitionName, outputFile);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to read partition '{partitionName}': {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully read partition '{partitionName}' to: {outputFile}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in ReadPartition: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Erase specific partition on device
        /// </summary>
        /// <param name="partitionName">Name of partition to erase</param>
        /// <returns>True if successful</returns>
        public bool ErasePartition(string partitionName)
        {
            try
            {
                LogInfo($"Erasing partition: {partitionName}");

                if (string.IsNullOrEmpty(partitionName))
                {
                    LogError("Partition name cannot be empty");
                    return false;
                }

                int result = FlashTool_ErasePartition(g_ft_handle, partitionName);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to erase partition '{partitionName}': {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully erased partition: {partitionName}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in ErasePartition: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Bootloader Operations

        /// <summary>
        /// Unlock device bootloader
        /// </summary>
        /// <returns>True if successful</returns>
        public bool UnlockBootloader()
        {
            try
            {
                LogInfo("Unlocking bootloader...");

                int result = FlashTool_UnlockBootloader(g_ft_handle);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to unlock bootloader: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo("Bootloader unlocked successfully");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in UnlockBootloader: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Relock device bootloader
        /// </summary>
        /// <returns>True if successful</returns>
        public bool RelockBootloader()
        {
            try
            {
                LogInfo("Relocking bootloader...");

                int result = FlashTool_RelockBootloader(g_ft_handle);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to relock bootloader: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo("Bootloader relocked successfully");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in RelockBootloader: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get current bootloader lock state
        /// </summary>
        /// <returns>Bootloader lock state</returns>
        public BootloaderLockState GetBootloaderLockState()
        {
            try
            {
                LogInfo("Getting bootloader lock state...");

                BootloaderLockState state = BootloaderLockState.UNKNOWN;
                int result = FlashTool_GetBootloaderLockState(g_ft_handle, ref state);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to get bootloader lock state: {errorMsg} (Code: {result})");
                    return BootloaderLockState.UNKNOWN;
                }

                LogInfo($"Bootloader lock state: {state}");
                return state;
            }
            catch (Exception ex)
            {
                LogError($"Exception in GetBootloaderLockState: {ex.Message}");
                return BootloaderLockState.UNKNOWN;
            }
        }

        #endregion

        #region NV Items Operations

        /// <summary>
        /// Read NV Item from device
        /// </summary>
        /// <param name="itemId">NV Item ID</param>
        /// <param name="outputFile">Output file path</param>
        /// <returns>True if successful</returns>
        public bool ReadNVItem(uint itemId, string outputFile)
        {
            try
            {
                LogInfo($"Reading NV Item {itemId} to file: {outputFile}");

                if (string.IsNullOrEmpty(outputFile))
                {
                    LogError("Output file path cannot be empty");
                    return false;
                }

                // Allocate buffer for NV item data (max 64KB)
                uint bufferSize = 65536;
                IntPtr buffer = Marshal.AllocHGlobal((int)bufferSize);
                uint actualSize = 0;

                try
                {
                    int result = FlashTool_ReadNVItem(g_ft_handle, itemId, buffer, bufferSize, ref actualSize);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to read NV Item {itemId}: {errorMsg} (Code: {result})");
                        return false;
                    }

                    // Write data to file
                    byte[] data = new byte[actualSize];
                    Marshal.Copy(buffer, data, 0, (int)actualSize);
                    File.WriteAllBytes(outputFile, data);

                    LogInfo($"Successfully read NV Item {itemId} ({actualSize} bytes) to: {outputFile}");
                    return true;
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in ReadNVItem: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Write NV Item to device
        /// </summary>
        /// <param name="itemId">NV Item ID</param>
        /// <param name="inputFile">Input file path</param>
        /// <returns>True if successful</returns>
        public bool WriteNVItem(uint itemId, string inputFile)
        {
            try
            {
                LogInfo($"Writing NV Item {itemId} from file: {inputFile}");

                if (!File.Exists(inputFile))
                {
                    LogError($"Input file not found: {inputFile}");
                    return false;
                }

                byte[] data = File.ReadAllBytes(inputFile);
                IntPtr buffer = Marshal.AllocHGlobal(data.Length);

                try
                {
                    Marshal.Copy(data, 0, buffer, data.Length);

                    int result = FlashTool_WriteNVItem(g_ft_handle, itemId, buffer, (uint)data.Length);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to write NV Item {itemId}: {errorMsg} (Code: {result})");
                        return false;
                    }

                    LogInfo($"Successfully wrote NV Item {itemId} ({data.Length} bytes) from: {inputFile}");
                    return true;
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in WriteNVItem: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Erase NV Item on device
        /// </summary>
        /// <param name="itemId">NV Item ID</param>
        /// <returns>True if successful</returns>
        public bool EraseNVItem(uint itemId)
        {
            try
            {
                LogInfo($"Erasing NV Item: {itemId}");

                int result = FlashTool_EraseNVItem(g_ft_handle, itemId);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to erase NV Item {itemId}: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully erased NV Item: {itemId}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in EraseNVItem: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region RPMB Operations

        /// <summary>
        /// Get RPMB information from device
        /// </summary>
        /// <returns>RPMB info structure, null if failed</returns>
        public RPMBInfo? GetRPMBInfo()
        {
            try
            {
                LogInfo("Getting RPMB information...");

                RPMBInfo rpmbInfo = new RPMBInfo();
                int result = FlashTool_GetRPMBInfo(g_ft_handle, ref rpmbInfo);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to get RPMB info: {errorMsg} (Code: {result})");
                    return null;
                }

                LogInfo($"RPMB Info - Blocks: {rpmbInfo.block_count}, Block Size: {rpmbInfo.block_size}, Total: {rpmbInfo.total_size}");
                return rpmbInfo;
            }
            catch (Exception ex)
            {
                LogError($"Exception in GetRPMBInfo: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Read RPMB data from device
        /// </summary>
        /// <param name="blockAddr">Starting block address</param>
        /// <param name="blockCount">Number of blocks to read</param>
        /// <param name="outputFile">Output file path</param>
        /// <returns>True if successful</returns>
        public bool ReadRPMB(uint blockAddr, uint blockCount, string outputFile)
        {
            try
            {
                LogInfo($"Reading RPMB blocks {blockAddr}-{blockAddr + blockCount - 1} to file: {outputFile}");

                if (string.IsNullOrEmpty(outputFile))
                {
                    LogError("Output file path cannot be empty");
                    return false;
                }

                // Get RPMB info to determine block size
                RPMBInfo? rpmbInfo = GetRPMBInfo();
                if (!rpmbInfo.HasValue)
                {
                    LogError("Failed to get RPMB information");
                    return false;
                }

                uint totalSize = blockCount * rpmbInfo.Value.block_size;
                IntPtr buffer = Marshal.AllocHGlobal((int)totalSize);

                try
                {
                    int result = FlashTool_ReadRPMB(g_ft_handle, blockAddr, blockCount, buffer);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to read RPMB: {errorMsg} (Code: {result})");
                        return false;
                    }

                    // Write data to file
                    byte[] data = new byte[totalSize];
                    Marshal.Copy(buffer, data, 0, (int)totalSize);
                    File.WriteAllBytes(outputFile, data);

                    LogInfo($"Successfully read RPMB ({totalSize} bytes) to: {outputFile}");
                    return true;
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in ReadRPMB: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Write RPMB data to device
        /// </summary>
        /// <param name="blockAddr">Starting block address</param>
        /// <param name="inputFile">Input file path</param>
        /// <returns>True if successful</returns>
        public bool WriteRPMB(uint blockAddr, string inputFile)
        {
            try
            {
                LogInfo($"Writing RPMB from file: {inputFile} to block address: {blockAddr}");

                if (!File.Exists(inputFile))
                {
                    LogError($"Input file not found: {inputFile}");
                    return false;
                }

                // Get RPMB info to determine block size
                RPMBInfo? rpmbInfo = GetRPMBInfo();
                if (!rpmbInfo.HasValue)
                {
                    LogError("Failed to get RPMB information");
                    return false;
                }

                byte[] data = File.ReadAllBytes(inputFile);
                uint blockCount = (uint)Math.Ceiling((double)data.Length / rpmbInfo.Value.block_size);

                IntPtr buffer = Marshal.AllocHGlobal(data.Length);

                try
                {
                    Marshal.Copy(data, 0, buffer, data.Length);

                    int result = FlashTool_WriteRPMB(g_ft_handle, blockAddr, blockCount, buffer);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to write RPMB: {errorMsg} (Code: {result})");
                        return false;
                    }

                    LogInfo($"Successfully wrote RPMB ({data.Length} bytes) from: {inputFile}");
                    return true;
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in WriteRPMB: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Erase RPMB blocks on device
        /// </summary>
        /// <param name="blockAddr">Starting block address</param>
        /// <param name="blockCount">Number of blocks to erase</param>
        /// <returns>True if successful</returns>
        public bool EraseRPMB(uint blockAddr, uint blockCount)
        {
            try
            {
                LogInfo($"Erasing RPMB blocks {blockAddr}-{blockAddr + blockCount - 1}");

                int result = FlashTool_EraseRPMB(g_ft_handle, blockAddr, blockCount);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to erase RPMB: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully erased RPMB blocks {blockAddr}-{blockAddr + blockCount - 1}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in EraseRPMB: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region GPT Operations

        /// <summary>
        /// Read GPT (GUID Partition Table) from device
        /// </summary>
        /// <param name="outputFile">Output file path</param>
        /// <returns>True if successful</returns>
        public bool ReadGPT(string outputFile)
        {
            try
            {
                LogInfo($"Reading GPT to file: {outputFile}");

                if (string.IsNullOrEmpty(outputFile))
                {
                    LogError("Output file path cannot be empty");
                    return false;
                }

                // Allocate buffer for GPT data (typically 34 sectors * 512 bytes)
                uint bufferSize = 34 * 512;
                IntPtr buffer = Marshal.AllocHGlobal((int)bufferSize);

                try
                {
                    int result = FlashTool_ReadGPT(g_ft_handle, buffer, bufferSize);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to read GPT: {errorMsg} (Code: {result})");
                        return false;
                    }

                    // Write data to file
                    byte[] data = new byte[bufferSize];
                    Marshal.Copy(buffer, data, 0, (int)bufferSize);
                    File.WriteAllBytes(outputFile, data);

                    LogInfo($"Successfully read GPT ({bufferSize} bytes) to: {outputFile}");
                    return true;
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in ReadGPT: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Write GPT (GUID Partition Table) to device
        /// </summary>
        /// <param name="inputFile">Input file path</param>
        /// <returns>True if successful</returns>
        public bool WriteGPT(string inputFile)
        {
            try
            {
                LogInfo($"Writing GPT from file: {inputFile}");

                if (!File.Exists(inputFile))
                {
                    LogError($"Input file not found: {inputFile}");
                    return false;
                }

                byte[] data = File.ReadAllBytes(inputFile);
                IntPtr buffer = Marshal.AllocHGlobal(data.Length);

                try
                {
                    Marshal.Copy(data, 0, buffer, data.Length);

                    int result = FlashTool_WriteGPT(g_ft_handle, buffer, (uint)data.Length);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to write GPT: {errorMsg} (Code: {result})");
                        return false;
                    }

                    LogInfo($"Successfully wrote GPT ({data.Length} bytes) from: {inputFile}");
                    return true;
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in WriteGPT: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Raw Firmware Operations

        /// <summary>
        /// Read raw firmware from device
        /// </summary>
        /// <param name="startAddr">Starting address</param>
        /// <param name="size">Size to read</param>
        /// <param name="outputFile">Output file path</param>
        /// <returns>True if successful</returns>
        public bool ReadRawFirmware(ulong startAddr, ulong size, string outputFile)
        {
            try
            {
                LogInfo($"Reading raw firmware from 0x{startAddr:X16}, size: {size} bytes to file: {outputFile}");

                if (string.IsNullOrEmpty(outputFile))
                {
                    LogError("Output file path cannot be empty");
                    return false;
                }

                if (size == 0)
                {
                    LogError("Size cannot be zero");
                    return false;
                }

                int result = FlashTool_ReadRawFirmware(g_ft_handle, startAddr, size, outputFile);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to read raw firmware: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully read raw firmware ({size} bytes) to: {outputFile}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in ReadRawFirmware: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Write raw firmware to device
        /// </summary>
        /// <param name="startAddr">Starting address</param>
        /// <param name="inputFile">Input file path</param>
        /// <param name="verify">Verify after write</param>
        /// <returns>True if successful</returns>
        public bool WriteRawFirmware(ulong startAddr, string inputFile, bool verify = true)
        {
            try
            {
                LogInfo($"Writing raw firmware from file: {inputFile} to address: 0x{startAddr:X16}, verify: {verify}");

                if (!File.Exists(inputFile))
                {
                    LogError($"Input file not found: {inputFile}");
                    return false;
                }

                int result = FlashTool_WriteRawFirmware(g_ft_handle, startAddr, inputFile, verify);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to write raw firmware: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully wrote raw firmware from: {inputFile}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in WriteRawFirmware: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Preloader Operations

        /// <summary>
        /// Read preloader from device
        /// </summary>
        /// <param name="outputFile">Output file path</param>
        /// <returns>True if successful</returns>
        public bool ReadPreloader(string outputFile)
        {
            try
            {
                LogInfo($"Reading preloader to file: {outputFile}");

                if (string.IsNullOrEmpty(outputFile))
                {
                    LogError("Output file path cannot be empty");
                    return false;
                }

                int result = FlashTool_ReadPreloader(g_ft_handle, outputFile);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to read preloader: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully read preloader to: {outputFile}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in ReadPreloader: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Write preloader to device
        /// </summary>
        /// <param name="inputFile">Input file path</param>
        /// <param name="verify">Verify after write</param>
        /// <returns>True if successful</returns>
        public bool WritePreloader(string inputFile, bool verify = true)
        {
            try
            {
                LogInfo($"Writing preloader from file: {inputFile}, verify: {verify}");

                if (!File.Exists(inputFile))
                {
                    LogError($"Input file not found: {inputFile}");
                    return false;
                }

                int result = FlashTool_WritePreloader(g_ft_handle, inputFile, verify);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to write preloader: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully wrote preloader from: {inputFile}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in WritePreloader: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Advanced Flash Operations

        /// <summary>
        /// Flash firmware using scatter format
        /// </summary>
        /// <param name="scatterFile">Scatter file path</param>
        /// <param name="mode">Flash mode</param>
        /// <returns>True if successful</returns>
        public bool FlashScatterFirmware(string scatterFile, FlashMode mode)
        {
            try
            {
                LogInfo($"Flashing scatter firmware: {scatterFile}, mode: {mode}");

                if (!File.Exists(scatterFile))
                {
                    LogError($"Scatter file not found: {scatterFile}");
                    return false;
                }

                int result = FlashTool_FlashScatterFirmware(g_ft_handle, scatterFile, mode);

                if (result != 0)
                {
                    string errorMsg = MTK_Common.StatusToString(result);
                    errorMsg = MTK_Common.decodeOut(errorMsg);
                    LogError($"Failed to flash scatter firmware: {errorMsg} (Code: {result})");
                    return false;
                }

                LogInfo($"Successfully flashed scatter firmware: {scatterFile}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Exception in FlashScatterFirmware: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Flash only selected partitions
        /// </summary>
        /// <param name="partitionNames">List of partition names to flash</param>
        /// <param name="mode">Flash mode</param>
        /// <returns>True if successful</returns>
        public bool FlashSelectedPartitions(List<string> partitionNames, FlashMode mode)
        {
            try
            {
                LogInfo($"Flashing selected partitions: [{string.Join(", ", partitionNames)}], mode: {mode}");

                if (partitionNames == null || partitionNames.Count == 0)
                {
                    LogError("Partition list cannot be empty");
                    return false;
                }

                // Convert string list to native array
                IntPtr partitionList = Marshal.AllocHGlobal(partitionNames.Count * IntPtr.Size);

                try
                {
                    for (int i = 0; i < partitionNames.Count; i++)
                    {
                        IntPtr partitionNamePtr = Marshal.StringToHGlobalAnsi(partitionNames[i]);
                        Marshal.WriteIntPtr(partitionList, i * IntPtr.Size, partitionNamePtr);
                    }

                    int result = FlashTool_FlashSelectedPartitions(g_ft_handle, partitionList, (uint)partitionNames.Count, mode);

                    if (result != 0)
                    {
                        string errorMsg = MTK_Common.StatusToString(result);
                        errorMsg = MTK_Common.decodeOut(errorMsg);
                        LogError($"Failed to flash selected partitions: {errorMsg} (Code: {result})");
                        return false;
                    }

                    LogInfo($"Successfully flashed selected partitions");
                    return true;
                }
                finally
                {
                    // Free allocated memory
                    for (int i = 0; i < partitionNames.Count; i++)
                    {
                        IntPtr partitionNamePtr = Marshal.ReadIntPtr(partitionList, i * IntPtr.Size);
                        Marshal.FreeHGlobal(partitionNamePtr);
                    }
                    Marshal.FreeHGlobal(partitionList);
                }
            }
            catch (Exception ex)
            {
                LogError($"Exception in FlashSelectedPartitions: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Logging Methods

        /// <summary>
        /// Log information message
        /// </summary>
        /// <param name="message">Message to log</param>
        private void LogInfo(string message)
        {
            string logMessage = $"[MTK_FLASHTOOL INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            Console.WriteLine(logMessage);
            WriteToLogFile(logMessage);
        }

        /// <summary>
        /// Log warning message
        /// </summary>
        /// <param name="message">Message to log</param>
        private void LogWarning(string message)
        {
            string logMessage = $"[MTK_FLASHTOOL WARNING] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            Console.WriteLine(logMessage);
            WriteToLogFile(logMessage);
        }

        /// <summary>
        /// Log error message
        /// </summary>
        /// <param name="message">Message to log</param>
        private void LogError(string message)
        {
            string logMessage = $"[MTK_FLASHTOOL ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            Console.WriteLine(logMessage);
            WriteToLogFile(logMessage);
        }

        /// <summary>
        /// Write message to log file
        /// </summary>
        /// <param name="message">Message to write</param>
        private void WriteToLogFile(string message)
        {
            try
            {
                string logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "log");
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                string logFile = Path.Combine(logDir, $"MTK_FlashTool_{DateTime.Now:yyyyMMdd}.log");
                File.AppendAllText(logFile, message + Environment.NewLine);
            }
            catch
            {
                // Ignore logging errors to prevent cascading failures
            }
        }

        #endregion
    }
}

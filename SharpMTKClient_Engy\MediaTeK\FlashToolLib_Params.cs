﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SharpMTKClient_Engy.MediaTeK
{
    public struct _939493B3
    {
        public uint _6A129231;

        public _04248484 _2A974436;

        public HandleRef AD814FBC;

        public IntPtr _19972719;
    }

    public enum _04248484
    {
        _0B2CF9A2,
        _1087BC98
    }

    public struct _192AFD94
    {
        public int A019DBB3;

        public uint _0C22AD8F;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
        public string _773191B6;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
        public string F5185F1E;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
        public string _4DAC0011;

        public bool D58F92AF;
    }

    public enum _470114B8
    {
        _28193F36,
        BD0C2BA5,
        _8E80ABA9,
        _5290142E
    }

    public delegate int _1DAE392B(IntPtr intptr_0, string string_0);
    public delegate int _1F33E0B4(byte byte_0, ulong ulong_0, ulong D8ABF723, IntPtr intptr_0);

    public struct BFB94637
    {
        public ushort B6B1002B;

        public uint _29B3DC19;

        public _1DAE392B _5A1C3F15;

        public IntPtr _91B0F03F;

        public _1F33E0B4 _76AD39BE;

        public IntPtr _37A79A33;

        public IntPtr A7827D88;
    }

    [StructLayout(LayoutKind.Sequential, Size = 1)]
    public struct A11A76BA
    {
    }

    public struct _46374508
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public char[] _67214C86;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public char[] _47021C30;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public char[] DEBADF90;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 128)]
        public char[] DDAE3F15;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public char[] _09A63C2C;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public char[] C3301E14;

        public uint _2AA0238E;

        public bool E7BF5616;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public char[] BB986FBE;

        public bool _2120631E;

        public bool _56BBC501;
    }

    public struct _6E05740E
    {
        public bool _668BDF16;

        public uint BF24E589;

        public uint _2BBA673A;

        public uint _0D17A113;

        public uint D7871AB2;

        public uint F6876893;

        public uint _613AC03F;

        public uint _3A0B7EAA;

        public uint _0931840B;

        public uint _2802CBB7;

        public uint _31AD5E3E;

        public uint _4C894D87;

        public uint _5B871033;

        public uint _3A012C3F;

        public uint _3E8FCB1A;

        public uint _741168A7;

        public uint _03156EB3;

        public uint _769C3211;

        public uint _9C1B6BAB;

        public uint E30ED4AF;

        public uint _433A4789;

        public uint C88D4E9B;

        public uint _1037BD28;

        public uint _0E9B8021;

        public uint B3097EBA;

        public uint FD85FD8B;

        public uint _51949814;

        public uint EC2C8311;

        public uint _7C1B7126;

        public uint _0530048F;

        public uint _4B2C6EBD;

        public uint _342660B3;

        public uint _98033523;

        public uint _2F95B318;

        public uint _58AE8C39;

        public uint A119459A;

        public uint _152CDD26;

        public uint _713222B0;

        public uint _871157B1;

        public uint _2FA54796;

        public _419EAD06 _50A3B32C;

        public EF337006 _9EA9768D;
    }

    public struct _419EAD06
    {
        public uint D70C9032;

        public uint _5D06A807;

        public uint _3C007C34;

        public uint AFA91D31;

        public uint _7A3B9087;

        public uint _6B0A1106;

        public uint _9ABFB838;

        public uint FDB00D11;

        public uint _14278D18;

        public uint _9AA8D78D;

        public uint _40BFDF36;

        public uint _83BF0886;

        public uint FAB9D314;

        public uint _710D3A2C;

        public uint _5527A539;
    }

    public struct EF337006
    {
        public uint _7E2F7A14;

        public uint F513E414;

        public uint _8314322E;

        public uint _24196F05;

        public uint BA8E5238;

        public uint _94872434;

        public uint _9F3D73BD;

        public uint A7A1BA88;

        public uint _34B6D88D;

        public uint _92A9CD8D;

        public uint E28BCBB1;

        public uint _05255022;

        public uint _6D2EA309;

        public uint F6BC9DA7;

        public uint _0F88630C;

        public uint _8D0D2B94;
    }

    public struct D893BE95
    {
        public byte A71B2D91;

        public byte _632CD1A5;

        public byte _0DABF3BC;

        public byte D0A29603;

        public BB22AA0D E4359B83;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
        public string C521C632;

        public ushort BA114DAC;

        public ushort DE129B00;

        public ushort _201D5D1D;

        public EXT_CLOCK B81CCD84;

        public byte AF8C98B8;

        public byte _4C017897;

        public _6094C9B6 _369F3EBD;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)]
        public BB89333F[] _682B99AA;

        public ushort _01078D9D;

        public uint _49093485;

        public ushort _431BF6B0;

        public ushort _549F1491;

        public ushort A398F037;

        public ushort F4292F00;

        public _6094C9B6 E4146609;

        public uint _8DB9A43A;

        public _6094C9B6 AEA4CE26;

        public BB89333F _9F286211;

        public ushort _7A1DD708;

        public ulong F6856737;

        public ushort _9B24BEAF;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
        public ushort[] _0E1FA231;

        public ushort _67BE3E80;

        public ushort _84127631;

        public ushort _8498232E;

        public byte B6ABDCB4;

        public byte _50307A8F;

        public byte _7D1D2532;

        public _6094C9B6 _691C678D;

        public uint _01085A3F;

        public _6094C9B6 _5A909417;

        public D19EFBB2 BA1C40BE;

        public BB89333F _66AD4203;

        public ulong _8690FCB8;

        public byte _6F2FFD82;

        public _6094C9B6 E637502F;

        public _6094C9B6 FF8CF11B;

        public uint _53AC7F91;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
        public string _7811D38D;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
        public string BE24D39A;

        private IntPtr _56947E86;

        public _6094C9B6 _251EBA0A;

        public ulong A18F1C9B;

        public ulong _50B087BC;

        public ulong _71ABFE2C;

        public ulong AB82DB05;

        public ulong _9AA30991;

        public ulong _3EA9A02F;

        public ulong _772ACD92;

        public ulong C8ADADA1;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
        public uint[] D29F34B8;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string FAB90CA8;

        public _6094C9B6 F19FD4BD;

        public ulong D037D598;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
        public uint[] BC87E22A;

        public _6094C9B6 _0A1FB63F;

        public ulong _0B01C425;

        public ulong C83010AA;

        public ulong _0C184184;

        public ushort _431CBF34;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 17)]
        public string D18757A5;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 5)]
        public string A7B5768E;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 129)]
        public string F82EAA9F;
    }

    public enum _6094C9B6 : uint
    {
        _658B3013 = 0u,
        _260123AF = 0u,
        F9006217 = 1000u,
        _23380C29 = 1000u,
        AF30633F = 1001u,
        _5EA4E195 = 1002u,
        C137A199 = 1003u,
        _6D0C5E2E = 1004u,
        _7F2C06B3 = 1005u,
        ABB97C1A = 1006u,
        _3924A489 = 1007u,
        _7712B334 = 1008u,
        _48090B20 = 1009u,
        _8933DF04 = 1010u,
        _3B0A3824 = 1011u,
        _6F3EBC98 = 1012u,
        _1D072981 = 1013u,
        _25B4103F = 1014u,
        E33F1194 = 1015u,
        D2876515 = 1016u,
        F31325B1 = 1017u,
        A03F70AE = 1018u,
        CFBA128C = 1019u,
        B395123B = 1020u,
        E1A70D0E = 1021u,
        _4AA2F908 = 1022u,
        _61068528 = 1023u,
        F417E2A9 = 1024u,
        _6192769B = 1025u,
        E93D7EB3 = 1026u,
        F9A05D99 = 1027u,
        _8905EF96 = 1028u,
        _8EAAC531 = 1029u,
        F131EEB4 = 1030u,
        _3E387B1D = 1031u,
        B9018B9A = 1032u,
        _66A36339 = 1033u,
        CB01919B = 1034u,
        _53A0E6B2 = 1035u,
        _5DA78488 = 1036u,
        _078E06A8 = 1037u,
        _100E493F = 1038u,
        _242111A1 = 1039u,
        _21856187 = 1040u,
        F402EF86 = 1041u,
        CC84D23C = 1042u,
        _98B21904 = 1043u,
        C0B7A339 = 2000u,
        _4EB12683 = 2000u,
        BDAF0183 = 2001u,
        DDA3039D = 2002u,
        _4AB0042B = 2003u,
        _1D16BF85 = 2004u,
        _93AE08A4 = 2005u,
        A1A501AF = 2006u,
        _6023BA04 = 2007u,
        _86A1188E = 2008u,
        _301CB0AB = 2009u,
        _55BFAE21 = 2010u,
        _7421B408 = 2011u,
        _14351923 = 2012u,
        E7199DBF = 2013u,
        _6AAD4DAA = 2014u,
        _0A90DB1B = 2015u,
        F11F7236 = 2016u,
        BEB98E10 = 2017u,
        _961F0E05 = 2018u,
        FB96520C = 2019u,
        _2B2CDF26 = 2020u,
        ADA19A02 = 2021u,
        _0DA948AC = 2022u,
        AEA3B631 = 2023u,
        F12A66B3 = 2024u,
        DF09732A = 2025u,
        C997BF98 = 2026u,
        _5FAD5E3C = 2027u,
        D50D9711 = 2028u,
        EDA5A58F = 2029u,
        C529DF93 = 2030u,
        _5A889BA5 = 2031u,
        _0984CB2F = 2032u,
        _2839D833 = 2033u,
        _3B36FF05 = 2034u,
        _3F31F997 = 2035u,
        A42F658F = 2036u,
        CD9309A1 = 2037u,
        EB3A9F10 = 2038u,
        _3D3B7CA4 = 2039u,
        DCBE142B = 2040u,
        _5029D48E = 2041u,
        _5508A939 = 2042u,
        _5822E403 = 2043u,
        _4BBB0E97 = 2044u,
        _93ACADB1 = 2045u,
        FB29B30A = 2046u,
        A48FE915 = 2047u,
        _2CBA5630 = 2048u,
        _2E3222B4 = 2049u,
        A11F3A8F = 3000u,
        _89BA7EB3 = 3000u,
        _9CB1F696 = 3001u,
        A6931B1F = 3002u,
        _8E8FE889 = 3003u,
        _00391182 = 3004u,
        DC3A45AB = 3005u,
        B7AD1997 = 3006u,
        CA9BCFB6 = 3007u,
        D236C98F = 3008u,
        _8B362299 = 3009u,
        _8D0C7A1B = 3010u,
        _6098AB9C = 3011u,
        DD11C73A = 3012u,
        EDA33720 = 3013u,
        _043EB28F = 3014u,
        _9C362499 = 3015u,
        _4FA1659D = 3016u,
        _28923732 = 3017u,
        ED8C930B = 3018u,
        _4B8A0983 = 3019u,
        EAA47982 = 3020u,
        _83871F0B = 3021u,
        _4A1C85BB = 3022u,
        FF849606 = 3023u,
        _409792A6 = 3024u,
        A8017820 = 3025u,
        _8E3CCEB6 = 3026u,
        _4A0CAE16 = 3027u,
        F7333A35 = 3028u,
        E12C9209 = 3029u,
        DF15CBB0 = 3030u,
        FF842099 = 3031u,
        _04123222 = 3032u,
        _5BB7CA3B = 3033u,
        _55843528 = 3034u,
        _76B3E62D = 3035u,
        F13FB830 = 3036u,
        _6796C484 = 3037u,
        _3A9B5C9F = 3038u,
        C72060A1 = 3039u,
        _18ADC39D = 3040u,
        B629B216 = 3041u,
        _4A8F6F1D = 3042u,
        _0B859D89 = 3043u,
        E7134539 = 3044u,
        _9A8D8B15 = 3045u,
        F307B238 = 3046u,
        A03B9309 = 3047u,
        _1C326821 = 3048u,
        _4A14542D = 3049u,
        _9398C528 = 3050u,
        _630261B9 = 3051u,
        _0786D1A8 = 3052u,
        _1C255A32 = 3053u,
        BE26E31B = 3054u,
        F5369400 = 3055u,
        CD1BE01C = 3056u,
        C9857487 = 3057u,
        _76B01F9C = 3058u,
        F8AD5E86 = 3059u,
        A10AED0B = 3060u,
        AE874610 = 3061u,
        _733EA938 = 3062u,
        _5138E023 = 3063u,
        _0AA18C0C = 3064u,
        _07A378A5 = 3065u,
        _5A8307B2 = 3066u,
        _6594611A = 3067u,
        _22AFC916 = 3068u,
        _34B90312 = 3069u,
        _08165CBB = 3070u,
        _5E389420 = 3071u,
        D33E6C1F = 3072u,
        F6B564A1 = 3073u,
        C624A4B2 = 3074u,
        _6517D524 = 3075u,
        _19AD3602 = 3076u,
        B0A4E6A9 = 3077u,
        _93BEF5B2 = 3078u,
        E7026BA3 = 3079u,
        _44B921A6 = 3080u,
        A8AADCA1 = 3081u,
        _5F95C01D = 3082u,
        D232C785 = 3083u,
        _352AA3BB = 3084u,
        _45A1E404 = 3085u,
        _4C1B0D39 = 3086u,
        D601C43D = 3087u,
        _9E2BEC32 = 3088u,
        _50A42535 = 3089u,
        ED8CC190 = 3090u,
        A602E537 = 3091u,
        _43872306 = 3092u,
        _353B9131 = 3093u,
        _88A6DB9C = 3094u,
        _3588998A = 3095u,
        _3D8CAC85 = 3096u,
        _413A2304 = 3097u,
        _563DB2B3 = 3098u,
        FC1079B2 = 3099u,
        _33A16529 = 3100u,
        E2BA2189 = 3101u,
        BF003904 = 3102u,
        _30978899 = 3103u,
        A1B4E3B3 = 3104u,
        EC065B33 = 3105u,
        A0223B19 = 3106u,
        _0E967299 = 3107u,
        _141FB913 = 3108u,
        D02A6999 = 3109u,
        _86AE9E9D = 3110u,
        _1125CD97 = 3111u,
        _6929B190 = 3112u,
        E42AF10F = 3113u,
        F201A3AF = 3114u,
        _378A5B1B = 3115u,
        _12B81084 = 3116u,
        A025DE06 = 3117u,
        _94AB243D = 3118u,
        _422945B4 = 3119u,
        _52A3D693 = 3120u,
        DC9AA9B1 = 3121u,
        _9238D421 = 3122u,
        F2A46122 = 3123u,
        _7E37FC35 = 3124u,
        _151A9438 = 3125u,
        C0A8828E = 3126u,
        _4D9E6C8D = 3127u,
        _3C17B381 = 3128u,
        A720E9A9 = 3129u,
        E7825600 = 3130u,
        D9275039 = 3131u,
        C58CAB01 = 3132u,
        _11933F00 = 3133u,
        _009B1236 = 3134u,
        DE105F12 = 3135u,
        _24BD4994 = 3136u,
        _36A9B525 = 3137u,
        _783E51AF = 3138u,
        A6A89531 = 3139u,
        _768AEF0C = 3140u,
        _98BF3402 = 3141u,
        _6B00CBBB = 3142u,
        BEA0D9B1 = 3143u,
        _2A183823 = 3144u,
        _9D09B098 = 3145u,
        EABA8F85 = 3146u,
        _34983C8C = 3147u,
        _403738AA = 3148u,
        CBBA681D = 3149u,
        _37294732 = 3150u,
        D91038BA = 3151u,
        _8BBFC00A = 3152u,
        _0D862126 = 3153u,
        _053A77BC = 3154u,
        _7E08F2A3 = 3155u,
        F10D9708 = 3156u,
        EE156E91 = 3157u,
        BABDBE20 = 3158u,
        _3885790B = 3159u,
        A726C193 = 3160u,
        DA9CEB05 = 3161u,
        AF3F1290 = 3162u,
        _9E380411 = 3163u,
        _7709BC17 = 3164u,
        _621D7CA9 = 3165u,
        _6C0907A8 = 3166u,
        _07163734 = 3167u,
        FBA3BE1C = 3168u,
        _760C92AE = 3169u,
        EE0F0931 = 3170u,
        CE155D98 = 3171u,
        E12EF82D = 3172u,
        AA2466A0 = 3173u,
        E23F9DAD = 3174u,
        _8F0EAF22 = 3175u,
        _2738D502 = 3176u,
        _731C3C3C = 3177u,
        E93EA424 = 3178u,
        _688B8B0B = 3179u,
        _5E103AB5 = 3180u,
        _6888A10E = 3181u,
        D3879700 = 3182u,
        _3BB9BD88 = 3183u,
        E89BC783 = 3184u,
        _8423AC9A = 3185u,
        E637CB2D = 3186u,
        E907BA33 = 4000u,
        _3B981510 = 4000u,
        _603D408B = 4001u,
        _1680AE04 = 4002u,
        _96A8DF02 = 4003u,
        _1E3DF630 = 4004u,
        _7B1348A9 = 4005u,
        _4C8207B8 = 4006u,
        E981B5BC = 4007u,
        E910B781 = 4008u,
        _7391F99A = 4009u,
        A18C3A15 = 4010u,
        _4ABC3083 = 4011u,
        C6946109 = 4012u,
        _4B3C13A8 = 4013u,
        A20A81BE = 4014u,
        _2403EB99 = 4015u,
        F42BF316 = 4016u,
        A4BB2090 = 4017u,
        _8B9533B6 = 4018u,
        _22AA6DB8 = 4019u,
        _001FA931 = 4020u,
        _0617B39C = 4021u,
        _8DAC0132 = 4022u,
        _743367AB = 4023u,
        _0C0B378D = 4024u,
        _1699AA3D = 4025u,
        _0A983433 = 4026u,
        _0888F3B3 = 4027u,
        _09B40E0B = 4028u,
        DF106BB7 = 4029u,
        D5B6EFA4 = 4030u,
        D317D6AE = 4031u,
        FE8795BA = 4032u,
        _0B832A91 = 4033u,
        _752B728A = 4034u,
        _0793BF12 = 4035u,
        _4181712A = 4036u,
        _28892105 = 4037u,
        _6039552D = 4038u,
        _5C23851E = 4039u,
        BB39BE38 = 4040u,
        E609D607 = 4041u,
        _4186C917 = 4042u,
        _49B56BA7 = 4043u,
        _259B52B6 = 4044u,
        _5525588B = 4045u,
        _3486DA13 = 4046u,
        _49BB5F1D = 4047u,
        _4F020107 = 4048u,
        _2884820F = 4049u,
        _068BAF37 = 4050u,
        _190F36A1 = 4051u,
        _5580D313 = 4052u,
        F6913637 = 4053u,
        _7880C734 = 4054u,
        F63EF19E = 4055u,
        EA104F38 = 4056u,
        _7596A2AE = 4057u,
        _7B0D3D32 = 4058u,
        E100FC31 = 4059u,
        _8F24B30B = 4060u,
        BF897E86 = 5000u,
        _731E04AE = 5000u,
        DC10690E = 5001u,
        D9AC3898 = 5002u,
        _90384BAF = 5003u,
        _75182C25 = 5004u,
        _2BB138B8 = 5005u,
        B0B55335 = 5006u,
        _83A39629 = 5007u,
        _8A331B85 = 5008u,
        _961C9238 = 5009u,
        _8D3CEE93 = 5010u,
        _59BE3286 = 5011u,
        EBBAD4A6 = 5012u,
        A39508A6 = 5013u,
        B924B31D = 5014u,
        _34158130 = 5015u,
        _44BA338D = 5016u,
        DEA2D8AF = 5017u,
        _9211419E = 5018u,
        D5A1AB38 = 5019u,
        F40D2EB0 = 5020u,
        _133828AC = 5021u,
        C73074AC = 5022u,
        E3BDE4B0 = 5023u,
        _499B1590 = 5024u,
        _8FBD49B8 = 5025u,
        _9827A120 = 5026u,
        _2608AA87 = 5027u,
        C638D308 = 5028u,
        _6484F59B = 5029u,
        _18A9D711 = 5030u,
        C5AC04AF = 5031u,
        _5E332AB8 = 5032u,
        A4A8912E = 5033u,
        _6CB61E99 = 5034u,
        _8D8DC493 = 5035u,
        B780FA26 = 5036u,
        A30C6B93 = 5037u,
        _0C3DBBA6 = 5038u,
        _2B27C607 = 5039u,
        _6A1D7A36 = 5040u,
        _370BFCAE = 5041u,
        FF326A09 = 5042u,
        _6E882E8A = 5043u,
        _3F8A7C9E = 5044u,
        F88E2727 = 5045u,
        _20BCA21C = 5046u,
        _8839AD0F = 5047u,
        _73BAADAA = 5048u,
        _820177BB = 5049u,
        _5D99D49C = 5050u,
        _4B93BC0A = 5051u,
        F3A3D305 = 5052u,
        B0006487 = 5053u,
        F4A2AC20 = 5054u,
        _6E32DE87 = 5055u,
        A0B90D0B = 5056u,
        E09D8EAA = 5057u,
        B6394E0B = 5058u,
        C718E20B = 5059u,
        _8B0D159F = 5060u,
        _339800A6 = 5061u,
        _461F9907 = 5062u,
        _90351EBB = 5063u,
        _381C8090 = 5064u,
        CC376F08 = 5065u,
        _5835670C = 5066u,
        A414B589 = 5067u,
        DC3FFCBF = 5068u,
        _0D907823 = 5069u,
        F39ECAAE = 5070u,
        C03AD22E = 5071u,
        A0069195 = 5072u,
        DF30E481 = 5073u,
        B1925033 = 5074u,
        C4A0EA28 = 5075u,
        DDB7E1B6 = 5076u,
        E933E731 = 5077u,
        FC0F402C = 5078u,
        F19EFD01 = 5079u,
        _603E6808 = 5080u,
        D53E5C0A = 5081u,
        _50BBDA20 = 5082u,
        _2ABBD4A0 = 5083u,
        A5B9BBB7 = 5084u,
        _90230A02 = 5085u,
        _7E2B9A33 = 5086u,
        _35A5441C = 6000u,
        _1DB8CC8C = 6000u,
        _882EE188 = 6001u,
        E48D7521 = 6002u,
        F99069A0 = 6003u,
        _6A9C2480 = 6004u,
        ED9D3DA8 = 6005u,
        B7821A39 = 6006u,
        C001EC12 = 6007u,
        _9FA027A3 = 6008u,
        F2B42237 = 6009u,
        _3B9A3221 = 6010u,
        _73BE3DBE = 6011u,
        _830F5402 = 6012u,
        _0B087E85 = 6013u,
        _07BCA024 = 6014u,
        E2A1D990 = 6015u,
        CC9C8198 = 6016u,
        _2EAA6A16 = 6017u,
        _4434C108 = 6018u,
        FBA18515 = 6019u,
        _6625228A = 6020u,
        _14B968AB = 6021u,
        EC28AC2B = 6022u,
        _0FB29000 = 6023u,
        AA9B83A4 = 6024u,
        _7720A608 = 6025u,
        _0C3772AA = 6026u,
        _131B14B7 = 6027u,
        A91909A7 = 6028u,
        _533B390B = 6029u,
        _03BF5F08 = 6030u,
        DAAA35AF = 6031u,
        _61187517 = 6032u,
        _27206204 = 6033u,
        C30C372A = 6034u,
        _5D18608B = 6035u,
        _03982015 = 6036u,
        _3297CC1A = 6037u,
        _0D2D7994 = 6038u,
        F50F8DBD = 6039u,
        _35A43F00 = 6040u,
        B835CB36 = 6041u,
        B7349FA7 = 6042u,
        _9C9B7DBE = 6043u,
        _4C092020 = 6044u,
        _3C1F9E38 = 6045u,
        E71B5C38 = 6046u,
        _3181FCBE = 6047u,
        _8F2D6A95 = 6048u,
        C537ADAD = 6049u,
        _5691DC1F = 6050u,
        B3832026 = 6051u,
        _78146112 = 6052u,
        _1B31CC1A = 6053u,
        _8FB50714 = 6054u,
        _83818722 = 6055u,
        F3881F1C = 6056u,
        _088F763F = 6057u,
        _302CBD99 = 6058u,
        A4929C2B = 6059u,
        _9F2BDB97 = 6060u,
        _998411B0 = 6061u,
        _25B21AA2 = 6062u,
        FC36D2A2 = 6063u,
        _0AB62613 = 6064u,
        _5D0130B0 = 6065u,
        F1BDB40D = 6066u,
        _9239E72F = 6067u,
        F71328A6 = 6068u,
        A2AACC10 = 6069u,
        E71DD821 = 6070u,
        CF3B2225 = 6071u,
        _99AEE793 = 6072u,
        BCB38A1F = 6073u,
        _6AB4D00F = 6074u,
        BE9D9536 = 6075u,
        D72780AA = 6076u,
        _4020F79B = 6077u,
        _331F1F01 = 6078u,
        _4E205B27 = 6079u,
        _87A76B81 = 6080u,
        _42B7CEAB = 6081u,
        _8901C230 = 6082u,
        _8C9EDA0F = 6083u,
        B4A7BBA8 = 6084u,
        _9C903125 = 6085u,
        _87807082 = 6086u,
        EC390221 = 6087u,
        DA9A5BAE = 6088u,
        _2891EB19 = 6089u,
        _5B9BDB2D = 6090u,
        _7F238BBA = 6091u,
        _273C52A9 = 6092u,
        _0318B421 = 6093u,
        _83B7ED25 = 6094u,
        _3B203B36 = 6095u,
        BA0FFF84 = 6096u,
        CA006C3F = 6097u,
        _108A0B13 = 6098u,
        _381E1D92 = 6099u,
        E4BF5D9F = 6100u,
        DD995E02 = 6101u,
        E52F548E = 6102u,
        EE01CA92 = 6103u,
        _6BA6402B = 6104u,
        A0847E2D = 6105u,
        F6AAE327 = 6106u,
        _7995A433 = 6107u,
        C4049593 = 6108u,
        B737AB2C = 6109u,
        _8B0C08B0 = 6110u,
        D7A2159C = 6111u,
        _5618A5B2 = 6112u,
        _4729ED11 = 6113u,
        _7C3C9293 = 6114u,
        _272DD425 = 6115u,
        BBA15286 = 6116u,
        _39A7B794 = 6117u,
        _73B708B1 = 6118u,
        E9A62016 = 6119u,
        _000858B8 = 6120u,
        _780F2621 = 6121u,
        _2FA4EB06 = 6122u,
        _42AF0280 = 6123u,
        E1B48E1D = 6124u,
        _2FB4402B = 6125u,
        BDA4F595 = 6126u,
        _371B8F0A = 6127u,
        _0F842212 = 6128u,
        _0E2D3585 = 6129u,
        BFB95D21 = 7000u,
        EE8E2317 = 7000u,
        _6CAEDF09 = 7001u,
        _8406A187 = 7002u,
        A79E1026 = 7003u,
        F280A6AD = 7004u,
        _4B9069B5 = 10001u,
        _039FC83B = 10002u,
        FA86F8A0 = 10003u,
        _6E34CB21 = 10004u,
        B31A7F2C = 10005u,
        _412ED78B = 10006u,
        _8F06389A = 10007u,
        _6E0C4B8F = 10008u,
        _9AAF381D = 3221291009u,
        _732D2982 = 3221291010u,
        CF1A6E3F = 3221291011u,
        _22892E1C = 3221291012u,
        _2C234686 = 3221291013u,
        _91AB7F8C = 3221291014u,
        F424EFBC = 3221291015u,
        _1CB6023A = 3221291016u,
        _032836B6 = 3221291017u,
        _968E4702 = 3221291018u,
        _4B880E23 = 3221291019u,
        _8FB62139 = 3221291020u,
        _6D9EAE17 = 3221291021u,
        BFB1D5BD = 3221291022u,
        _742D5516 = 3221291023u,
        _90BDEE9F = 3221291024u,
        AB90AC80 = 3221291025u,
        C9B6ACB2 = 3221291026u,
        _9EB3C981 = 3221291027u,
        _57331885 = 3221553153u,
        _2E93CD8D = 3221553154u,
        _35B5B695 = 3221553155u,
        CD862A14 = 3221553156u,
        _7C34D63F = 3221553157u,
        DA1383BE = 3221553158u,
        _859C8782 = 3221553159u,
        _1699308A = 3221553160u,
        _3B8C290D = 3221553161u,
        _44835DA7 = 3221553162u,
        _7EB19E35 = 3221553163u,
        DF91CFB2 = 3221553164u,
        B2041D0F = 3221553165u,
        F5B94398 = 3221618689u,
        _6580A381 = 3221618690u,
        _2213738D = 3221618691u,
        _1FAA0709 = 3221618692u,
        A72FB720 = 3221618693u,
        _44AEA995 = 3221618694u,
        A8B644A0 = 1074135041u,
        _24B5C7A4 = 3221749761u,
        _84B01506 = 3221684225u,
        _24B66D07 = 3221684226u,
        _4FA563A4 = 3221684227u,
        _059A0703 = 3221684228u,
        _6C24D297 = 3221684229u,
        _520C9527 = 3221422081u,
        _46A52B2B = 3221422082u,
        E6BC7A16 = 3221422083u,
        _2D93C0B6 = 3221422084u,
        _9C2BF19B = 3221422085u,
        _4D81709A = 3221422086u,
        _10161DBF = 3221422087u,
        CA004834 = 3221422088u,
        _9209BD02 = 3221422089u,
        F823C324 = 3221422090u,
        _2ABB79B0 = 3221422091u,
        B6B5AE35 = 3221422092u,
        C731DC9E = 3221422093u,
        EA91AB12 = 3221422094u,
        _820CDF23 = 3221422095u,
        _18395095 = 3221422096u,
        _0E8B1E0A = 3221422097u,
        _1C12EC8D = 3221422098u,
        _430EAA0E = 3221422099u,
        _06A4E79A = 3221422100u,
        C0037B30 = 3221422101u,
        _153FC433 = 3221422102u,
        BD298020 = 3221422103u,
        CC28C4AF = 3221422104u,
        F2A45514 = 3221422105u,
        _30B728B8 = 3221422106u,
        A212908D = 3221422107u,
        _24361427 = 3221422108u,
        C10EB919 = 3221422109u,
        DBBD0B88 = 1074003969u,
        _449BD519 = 1074003970u,
        _7C911C3F = 1074003971u,
        _881F9D96 = 1074003972u,
        D4809837 = 1074003973u,
        DA2C5130 = 1074003974u,
        _358B7CB8 = 3221487617u,
        _25BC23B2 = 3221487618u,
        _6D0A72B3 = 3221487619u,
        BAA05289 = 3221487620u,
        E0A66FA9 = 3221487621u,
        _3985D506 = 3221487622u,
        C692FA02 = 3221487623u,
        _9E91B088 = 3221487624u,
        _811D09A7 = 3221487625u,
        _6736E70A = 3221487626u,
        _1700888B = 3221487627u,
        E1880D30 = 3221487628u,
        CD967827 = 3221487629u,
        _92267807 = 3221487630u,
        E48DB09C = 3221487664u,
        C111131B = 3221487680u,
        _6D8043B6 = 3221487681u,
        _2134D013 = 3221487682u,
        _48977F09 = 3221487683u,
        _9C00B8B7 = 3221487684u,
        _3B108E2D = 3221487685u,
        _3C1FA212 = 3221487686u,
        _2D93BBBF = 3221487696u,
        _34197D8F = 3221487872u,
        _81B053B5 = 3221487874u,
        _0D3E4A85 = 3221488128u,
        _902F26B0 = 3221488129u,
        EC9CAA98 = 3221488130u,
        _3F0DAA11 = 3221488131u,
        _0126101A = 3221488132u,
        BF93BB3F = 3221488133u,
        _0B02940E = 3221488134u,
        _6396012C = 3221488135u,
        _06366389 = 3221488136u,
        _9C16EEB4 = 3221488137u,
        F61AD689 = 3221488138u,
        _109863B6 = 3221488139u,
        CC8D1F36 = 3221488140u,
        D90FF039 = 3221488141u,
        _77149B31 = 3221488142u,
        DA0FB581 = 3221488143u,
        _06A2C7A0 = 3221488144u,
        _3B95E8A2 = 3221487872u,
        FA2ED221 = 1074003970u,
        _0594F4B1 = 3221487874u,
        A90ECB0C = 1074003971u,
        D9263B35 = 3221487872u,
        CC1077A8 = 1074003970u,
        CB397923 = 3221487874u,
        _2933C28B = 1074003971u,
        _5C32C507 = 3221356545u,
        _5C08D102 = 3221356546u,
        _948934B2 = 3221356547u,
        C013B0B3 = 3221356548u,
        _3E181C91 = 3221356549u,
        _7BB7ADA1 = 3221356550u,
        _3DB4F3AF = 3221356551u,
        A80C41B3 = 3221356552u,
        _0CBBD39B = 3221356553u,
        _9E1DA7BE = 3221356554u,
        _54249CAE = 3221356555u,
        _5DBB0CA0 = 3221356556u,
        A612B526 = 3221356557u,
        _1894622F = 3221356558u,
        _58316E98 = 3221356559u,
        DE09D112 = 3221356560u,
        _7B8F05A5 = 3221356561u,
        _62AECA84 = 3221356562u,
        FB084482 = 3221356563u,
        EAA2BA8A = 3221356564u,
        D036C535 = 3221356565u,
        _55015933 = 3221356566u,
        _60B3D212 = 3221356567u,
        _458C9D9A = 3221356568u,
        F821EBA7 = 3221356569u,
        _6C124F03 = 3221356570u,
        _5F0EE5A2 = 3221356571u,
        F2AF3817 = 3221356572u,
        _9A20AC8B = 3221356573u,
        _0431F786 = 3221356574u,
        BE9EA6B7 = 3221356575u,
        _78BF731B = 3221356576u,
        EF89B504 = 3221356577u,
        _9D3AE895 = 3221356578u,
        _7814D7B9 = 3221356579u,
        _1F93A100 = 3221356580u,
        _03306CAA = 3221356581u,
        _183A419D = 3221356582u,
        F7BA14B1 = 3221356583u,
        _329FBEB0 = 3221356584u,
        F10333A7 = 3221356585u,
        F8BFEC26 = 3221356586u,
        _5B001398 = 3221356587u,
        _3C99B238 = 3221356588u,
        _2911A5B0 = 3221356589u,
        A396DC34 = 3221356590u,
        B2BBE4A1 = 3221356591u,
        _4D058CA2 = 3221356592u,
        ABBAD7AD = 3221356593u,
        A0254E9D = 3221356594u,
        _3F88321A = 3221356595u,
        _33A9C799 = 3221356596u,
        _4B039802 = 3221356597u,
        D8B50B36 = 3221356598u,
        B58F7F06 = 3221356599u,
        C32B8913 = 3221356600u,
        EC94B08B = 3221356601u,
        D723A381 = 3221356608u,
        _7B3E6B0D = 3221356609u,
        _0F92DE88 = 3221356610u,
        C72124B1 = 3221356611u,
        _09BAB324 = 3221356612u,
        E81DED27 = 3221356613u,
        AF370126 = 3221356614u,
        A3347039 = 3221356615u,
        FFB02025 = 3221356616u,
        _8595971B = 3221356617u,
        FA92931D = 3221356618u,
        _1C28CB25 = 3221356619u,
        AA0E593A = 3221356620u,
        _4F95D63B = 3221356621u,
        _4BBFE125 = 3221356622u,
        DE056616 = 3221356623u,
        _9C04988E = 3221356624u,
        F42A2FB0 = 3221356625u,
        _3F1D2190 = 3221356626u,
        E804ACBF = 3221356627u,
        _4B9F219F = 2147483647u
    }

    public enum BB89333F
    {
        _3327C62C = 0,
        _543ABA07 = 1,
        E0B63413 = 2,
        B4A2EB28 = 3,
        _491D57B1 = 4,
        _03B45C11 = 5,
        _5D01A213 = 6,
        C82D62A3 = 7,
        _623CEB3F = 8,
        _85A5D602 = 8,
        _8D8F8D25 = 9
    }

    public enum D19EFBB2
    {
        _3411C617,
        A2AC7690,
        _2B1A7C93,
        _109A6135
    }

    public delegate int FDB25103(A10F7599 a10F7599_0, ulong B0AB0FA2, ulong ulong_0, IntPtr intptr_0);
    public delegate int _980779B5(byte E2AB90B6, IntPtr FE0A01B6);
    public delegate int _2C3B3C2D(_7F8D2E34 E93CDD9A, IntPtr intptr_0);

    public struct DC0F78AE
    {
        public A10F7599 DE3B902A;

        public _0D900F32 _8CB88DA8;

        public _12B4CE81 _86A9E0BC;

        public FDB25103 _3280E51E;

        public IntPtr _6009089A;

        public _980779B5 _650286A0;

        public IntPtr A4A34C06;

        public _2C3B3C2D BEB3ABB5;

        public IntPtr D6090303;
    }

    public enum A10F7599
    {
        HW_STORAGE_NOR,
        HW_STORAGE_NAND,
        HW_STORAGE_EMMC,
        HW_STORAGE_SDMMC,
        HW_STORAGE_UFS,
        HW_STORAGE_NONE,
        HW_STORAGE_TYPE_END
    }

    public struct _7F8D2E34
    {
        public ulong _7111759B;

        public ulong _3E2BBC2D;

        public uint _543A4784;

        public uint _4A012A8E;

        public uint D7BE39A1;
    }

    public struct _0D900F32
    {
        public bool _2C1B5911;

        public bool _7D174B2F;

        public ulong _43A333BF;

        public ulong EAA13025;

        public uint _0A91B314;

        public CD29848E A6812C2D;
    }

    public enum CD29848E
    {
        C9BF260B,
        C63475B3,
        E33A8DA5
    }

    public enum _12B4CE81
    {
        _12BB7B3E,
        _9DB18423,
        EA8DBEBC,
        _8ABA3387
    }

    public struct _10843D3A
    {
        public _7F8D2E34 _63923634;
    }

    public struct _5B80A500
    {
        public uint B8874F21;

        public FCAB0D18 F41F2706;

        public string[] _6B3CFD09;

        public bool[] _3D93FF92;
    }

    public enum FCAB0D18
    {
        _6E876290,
        _4D8A1795
    }

    public struct _369BDF9D
    {
        public int BC94C62F;

        public uint _318821B0;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
        public byte[] _7F3CFD1D;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
        public byte[] _3905C234;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
        public byte[] F6854EA5;

        public bool C92C3C14;
    }


    public struct _1B168808
    {
        public uint _1A0E6E8A;

        public uint _97167792;

        public F12CA73B DE3C883D;

        public _3584671E _8C0E180B;

        public IntPtr _78A84414;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)]
        public E89641A7[] _6B23FB87;

        public E89641A7 DA9C2AA1;

        public uint _4821B339;

        public A10F7599 C9AFA72D;

        public IntPtr _9AA5DA03;

        public byte _85A07C2F;

        public const int _36991621 = 80;

        public const int _94BFD50E = 104;

        public const int _6793BCBC = 114;

        public byte AE94F702;

        public _4E224517 _2AA6101E;

        public bool C337948C;

        public _318461B9 _8615273F;

        public E9A4DBA8 _2803BFA3;
    }

    public delegate int DB9B088E(IntPtr intptr_0, IntPtr intptr_1, IntPtr intptr_2);
    public delegate int _6F066E8C(IntPtr intptr_0);
    public delegate int _908D0600(byte byte_0, uint uint_0, uint uint_1, IntPtr intptr_0);
    public delegate int _3584671E(IntPtr intptr_0);

    public enum E9A4DBA8
    {
        _5D1CA296,
        D50A6034,
        _2683B727,
        _48BC0E91
    }

    public enum _318461B9
    {
        _8E89D51C,
        _23A80E3E,
        _8EA75F89,
        _3988503F,
        A7B7AAB2,
        _92013631
    }

    public enum _4E224517
    {
        A1162030 = 1,
        FCB2DF32
    }

    public enum E89641A7
    {
        _2910AFBB = 0,
        _59AA7035 = 1,
        _32BBDBBE = 2,
        _7DB2CC0A = 3,
        _3E09E923 = 4,
        _692F2E07 = 5,
        AD9F0F99 = 6,
        _1D9D58AF = 7,
        _971D7499 = 8,
        F099723F = 8,
        _0EB1BF06 = 9
    }


    public struct F12CA73B
    {
        public BB22AA0D _55AFC01E;

        public EXT_CLOCK E711E315;

        public uint _64B80C08;

        public uint _5D0EFE20;

        public uint _643875B8;

        public _973A1413 _83396887;

        public IntPtr B68EF493;

        public DB9B088E DE0323AE;

        public IntPtr _9E846005;

        public bool DEB90C23;

        public IntPtr _9C344F8F;

        public IntPtr _0839CE81;

        public IntPtr E20BA015;

        public IntPtr _3DB8772E;

        public IntPtr F42F92B1;

        public _67A2A210 _282CA598;

        public IntPtr _5D3F9135;

        public B987450F C5325D98;

        public IntPtr _8D838DA6;

        public uint _0C3B792B;

        public uint D0B907A1;

        public bool BC94BC23;

        public uint _6A90AAA8;

        public IntPtr _44A08409;

        public _6F066E8C CF8E9307;

        public IntPtr CD891B84;

        public _908D0600 FF1F2E22;

        public IntPtr _638CAB06;

        public bool FE12BC87;

        public uint _4324CC16;
    }

    public struct _31956C37
    {
        public D893BE95 _083E6A27;
    }

    public delegate int EF113A00(IntPtr intptr_0, string string_0);

    public struct _8D8AE980
    {
        public EF113A00 _3A2BF2AF;

        public IntPtr _658A8F36;
    }

    public struct C0A092A9
    {
        public _25060D07 _1A973431;
    }

    public enum _25060D07
    {
        _309159BD = 0,
        F31B4B34 = 1,
        _9AA18E8E = 2,
        _333011AE = 65535
    }

    public struct _40196033
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
        public string A8251836;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
        public string _6AB59C23;

        public BB22AA0D _7BA9389C;

        public A10F7599 _7BBDA1AE;
    }

    public enum _40159E1B
    {
        _269F2638 = 1,
        _902D48A6 = 2,
        AD8567AA = 3,
        _8AAFEEB1 = 4,
        F786D3AD = 5,
        _08038582 = 6,
        F208B30E = 7,
        _792AEB32 = 256,
        _22B29C12 = 257,
        _9E86B6B6 = 258,
        E008B4AC = 259,
        _93118C2E = 260,
        AB9A9380 = 261,
        F6160F8C = 262,
        ACB8C402 = 263,
        _34A0740A = 264,
        _4882E799 = 265,
        F38DAFBE = 266,
        DE807599 = 267,
        _9A3A4D1F = 512
    }

    public struct B0A4EC14
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
        public byte[] _48A86915;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public byte[] DA8A1096;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 128)]
        public byte[] CDB7100A;
    }

    public struct _9510799D
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
        public string _5C3A2089;

        public ulong _35AF172D;

        public _56337609 D388242D;

        public ulong _63964CB9;

        public ulong C91F5131;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string _9CB8182C;

        public ulong A996F005;

        public ushort C8005AAB;

        public _4982B08E _1FA6B6AF;

        public bool F1B7BD2F;

        public bool _2C8A6626;

        public bool _3614639C;

        public _07183EB6 _632810AA;

        public uint CCA23E0C;

        public ulong _408AF99A;

        public bool _30A4FBB9;
    }

    public enum _56337609
    {
        _00BECF3C,
        _4200393D
    }

    public enum _4982B08E
    {
        _329E208F = 1,
        _2BBA3F1F = 7,
        F5BBAAAB = 8,
        BDB2F23A = 9,
        _08001221 = 16,
        _4F36038F = 17,
        F49B2AAE = 18,
        _52B1BAA2 = 19,
        CAABC78B = 32,
        DDAC5287 = 255
    }

    public enum _07183EB6
    {
        _0F3BC60E,
        _74279585,
        _99B9BA2B,
        _7920DF9B,
        A88B20B1,
        _84820CB7,
        _38AB679A,
        _88B84E20
    }

    public struct _932F52AC
    {
        public A10F7599 _2636CC3E;

        public IntPtr _7AB89220;

        public F4052CAF C5A97419;

        public _210C6D92 _8C9F0F84;

        public IntPtr _0A2BBDA8;

        public _8BACF425 _2792FF32;

        public IntPtr C315D123;
    }

    public enum F4052CAF
    {
        E029788A = 0,
        _5C33B81C = 3,
        _06B9E438 = 4,
        _0702271C = 10,
        _59341BAF = 100,
        _41356715 = 1000,
        E0B39E02 = 10000
    }

    public delegate int _210C6D92(A10F7599 a10F7599_0, ulong ulong_0, ulong ulong_1, string BCA17714, IntPtr intptr_0);
    public delegate int _8BACF425(byte FF94CE8A, ulong DCB4F33B, ulong ulong_0, IntPtr FD3CC5AE);

    [StructLayout(LayoutKind.Sequential, Size = 1)]
    public struct A385AEA5
    {
    }


    public delegate int _481A0D15(ref D893BE95 CD9AEA28, IntPtr D0A9381B);
    public delegate int _291E8F22(IntPtr intptr_0, string string_0);
    public delegate int _8B280537(byte byte_0, ulong ulong_0, ulong BE216D39, IntPtr intptr_0);
    public delegate int _778C632F(IntPtr intptr_0);
    public delegate int _61BDDBB6(byte byte_0, ulong ulong_0, ulong EB339BB5, IntPtr intptr_0);
    public delegate int _76947A3C(IntPtr intptr_0);
    public delegate int _3DA1F03F(IntPtr F02F3533, string string_0);
    public delegate int D809649E(byte byte_0, ulong ulong_0, ulong ulong_1, IntPtr intptr_0);

    public struct D8895415
    {
        public IntPtr A63A1A15;

        public IntPtr B0AA068F;

        public _481A0D15 DC8EC717;

        public IntPtr DE85C72B;

        public F4052CAF _45280B8E;

        public _291E8F22 _1E3910B7;

        public IntPtr _459D1C98;

        public _8B280537 _9E969506;

        public IntPtr _9DA69203;

        public _778C632F A92E7E9D;

        public IntPtr EF3F3A31;

        public _61BDDBB6 _7A85D18F;

        public IntPtr _67268699;

        public _76947A3C _3B3C6E15;

        public IntPtr _8906A51A;

        public _3DA1F03F _1C33B022;

        public IntPtr _18A02C05;

        public D809649E _8EA7B51F;

        public IntPtr _243D6E06;

        public bool _9F8D3B9B;

        public bool _2D8F6E19;

        public bool _70A62593;
    }

    [StructLayout(LayoutKind.Sequential, Size = 1)]
    public struct EDB22AA5
    {
    }

    public struct _8A0D82B1
    {
        public _1BAE2FAF _438DA6BF;

        public D0184C86 _5D31029D;

        public uint _9DB37F1F;

        public uint _582F6C84;
    }

    public struct _1BAE2FAF
    {
        public A78DF51E _2BB8F6A4;

        public ushort BDBEA6AC;

        public IntPtr _83145828;

        public string _6D094BB4;
    }

    public enum A78DF51E
    {
        B3BA8E21,
        D5AF610D,
        _220979A9
    }

    public enum D0184C86
    {
        EF89C41B = 1,
        BD0263A3,
        BE9EC718,
        _0E308F23,
        _5C9C7821,
        FDB3C01B,
        D11D4302,
        E9B00420,
        FA8E74A7,
        AE2C6AA5,
        _261961A6,
        _0F153401,
        C9072499
    }

    public delegate int _0C82F532(IntPtr B71C65B0, byte byte_0, IntPtr intptr_0);

    public struct _08BEFE15
    {
        public F12CA73B _100F0389;

        public _481A0D15 _65101398;

        public IntPtr F290D496;

        public _3584671E _382B8D39;

        public IntPtr B60CBC9F;

        public _0C82F532 AB94EF86;

        public IntPtr _7689B532;

        public byte _4912CF31;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)]
        public E89641A7[] _240946B1;

        public E89641A7 _11ABC400;

        public uint CC86189C;

        public bool _13298C0F;

        public _6E05740E _06A02719;
    }

    public struct C1B09017
    {
        public D893BE95 _55960237;

        public _3624F3BF _4819A0A4;

        public ED93B522 _7482AF93;

        public _7B943C2D _0B87EC16;
    }

    public enum _5D95EE9D
    {
        _1128EB24,
        _69AF5A2A
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
    public struct _3624F3BF
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string BD2D0D2D;

        public uint _272BA297;

        public IntPtr _9915B3A4;

        public uint _648EBE08;

        public uint C81D6A39;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
        public string B58E5ABF;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string _3C3A222C;

        private _5D95EE9D B82B46A1;
    }

    public enum _7036A1B0
    {
        _7717DA87,
        _0F982A8C
    }

    public struct ED93B522
    {
        public uint A0909B0B;

        public _7036A1B0 E4248DAF;

        [MarshalAs(UnmanagedType.LPStr)]
        public string E48F4081;

        public uint E1194300;

        [MarshalAs(UnmanagedType.LPStr)]
        public string BB2E1112;

        [MarshalAs(UnmanagedType.LPStr)]
        public string _5337092E;
    }

    public enum FBA8E43F
    {
        _5DAA9103,
        _4E94DB9F
    }

    public struct _7B943C2D
    {
        public uint _212B66AC;

        public FBA8E43F DEA4E3AB;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
        public string EF3CDC0A;

        public byte[] _4B250C93;

        public uint E09601BB;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
        public string _839D3389;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
        public string F7986E01;
    }

    public struct _9530CBB6
    {
        public _5023A121 A2A17011;
    }

    public enum _0F249D89
    {
        DDA99413,
        _2BAA8F39
    }

    public struct _5023A121
    {
        public _0F249D89 _773B3A99;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string _57273C23;

        public byte[] _76023E81;

        public ulong FC0F3C05;

        public ulong C89A7E87;

        public uint _02202FB4;

        public bool _9D87ED0A;
    }

    public struct E3337584
    {
        public E505263E _0FA56EB0;

        public uint FC99C306;

        public C29DB2BA _788B0D9B;

        public _713E612D _79B4D607;
    }

    public enum E505263E
    {
        C73F7716,
        _0A874819,
        EF862A12,
        DE1E2E3D
    }

    public struct C29DB2BA
    {
        public uint _94384183;

        public uint _0F3D9EB9;

        public uint _69314433;

        public uint _018DA69A;

        public uint _7C0C7094;

        public uint _398CC687;

        public uint _0C101E86;

        public uint _0A266603;

        public uint _8787C20E;

        public uint _59ACE585;

        public uint A21E8B93;

        public uint _90194F04;

        public uint _91ACACBA;

        public uint _9F92AAA7;
    }

    public struct _713E612D
    {
        public uint _6689373A;

        public uint _2E1EEE3B;

        public uint _8692A18A;

        public uint DFBF9023;

        public uint _47BFE737;

        public uint C01BB9A7;

        public uint A62C4B86;

        public uint CEBA1908;

        public uint _24AFBBB7;

        public uint A52C528A;

        public uint C0B5CF82;

        public uint _442E131D;
    }


    [StructLayout(LayoutKind.Sequential, Size = 1)]
    public struct AC9454A8
    {
    }

    public struct _00118F26
    {
        public uint _083A31B5;
    }


    public struct FA28852A
    {
        public uint _26093FAA;

        public bool _9CB1348C;

        public bool EE3F22B5;

        public bool _6830AC24;

        public bool _853947B6;
    }

    public delegate void _5A3C4332(IntPtr A6025A16, _963A5997 _963A5997_0, uint uint_0, ulong A2B2729D, ulong BABA3F34, ref _938F0887 A4B3CC2C);
    public delegate void _52A65DAF(IntPtr intptr_0, string string_0);
    public delegate bool _4D0DEC2C(IntPtr intptr_0);

    public struct _0400A61C
    {
        public IntPtr _709C5917;

        public _5A3C4332 _9E16CF2F;

        public _52A65DAF _7F05E790;

        public _4D0DEC2C _06999830;

        public _7E8ED989 CA3283A8;
    }

    public enum _963A5997
    {
        _1106F2AA,
        _209CF583,
        _0A394F8D,
        D6BDFC99,
        A600E9A7,
        A79C0AAF,
        _7402A91F,
        A6299B23,
        B79DBA01,
        _459E8B0F
    }

    public struct _938F0887
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
        public string _03A2AEA6;
    }

    public struct _7E8ED989
    {
        public _67A2A210 BF384A0D;

        public B987450F F4A53F80;

        public IntPtr A029AB34;

        public IntPtr _09BAB031;
    }

    public struct _63A2481B
    {
        public uint E78453A8;

        public uint _728D46B6;

        public uint _06B24A0A;

        public uint _962D0201;

        public uint _3DB6FC1A;

        public uint D32F9535;
    }

    public struct _89265D32
    {
        public BB22AA0D _81320522;

        public EXT_CLOCK B30DCF8C;

        public uint _6D88531C;

        public uint EE1BA919;

        public _973A1413 _4F3F7011;

        public IntPtr B80A7A10;

        public DB9B088E _918E7B12;

        public IntPtr E321AA07;

        public bool _1E09E8B8;

        public IntPtr B7B9A3B5;

        public IntPtr _18304502;

        public IntPtr _81BEA62F;

        public IntPtr B5939724;

        public IntPtr _38353624;

        public _67A2A210 _39915F28;

        public IntPtr _4882320C;

        public B987450F _0F2B3D27;

        public IntPtr _81988328;

        public bool _01B22F94;
    }

    public struct C28D96B7
    {
        public BB22AA0D _842584AF;

        public IntPtr E405430B;

        public ushort F1171E13;

        public ushort ACAE6E86;

        public ushort _1717058A;

        public EXT_CLOCK _651686AA;

        public uint _7E24FB0D;

        public uint E2BA9C90;

        public uint _3D206419;

        public IntPtr _043D9335;

        public uint B38E0D9E;
    }

    public struct _979F6D31
    {
        public uint _3996609C;

        public _640C902B A53108A3;

        public string[] _21AC6A35;

        public IntPtr _0783610B;
    }

    public enum _640C902B
    {
        B3212495,
        EC932BBD
    }

    public struct EB30BFB0
    {
        public int _273A1AA3;

        public uint C40C292F;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
        public byte[] _08A7CB3B;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
        public byte[] _9383CFA4;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 512)]
        public byte[] DDA935BD;

        public bool _64B23210;
    }

    public delegate int _4A0B4F96(byte byte_0, ulong ulong_0, ulong ulong_1, IntPtr DD14F51D);

    public struct D09F3E0E
    {
        public A10F7599 EC362913;

        public _95A57217 AA9FEB83;

        public ulong _77BA6E24;

        public uint E2BE563F;

        public F70BD398 B9A87FB6;

        public string _61244E21;

        public ulong _400CF0A5;

        public uint _3221523C;

        public _4A0B4F96 FD0407B1;

        public IntPtr ED8B319B;

        public _12B669A8 E2A3E20F;
    }

    public enum _95A57217
    {
        _74B66E36,
        D29E9B23,
        _61982398
    }

    public enum F70BD398
    {
        FFA4CA89,
        _0F058892
    }

    public enum _12B669A8
    {
        _748D4DBE,
        F3124D90
    }


    public struct C7BBB58C
    {
        public A10F7599 _952DF80F;

        public _95A57217 A3332FA8;

        public ulong _8A9AB693;

        public ulong A308D982;

        public uint _45BCDC19;

        public _1799AB88 _2BBD45B6;

        public string _4E0AFE23;

        public _8BACF425 _1EB6002B;

        public IntPtr _2B23DD32;
    }

    public enum _1799AB88
    {
        _799E069D,
        _3713F039
    }

    public struct _172B3509
    {
        public ulong _44BB1E8C;
    }


    public delegate int E927A3A9(ref IntPtr C5386833);
    public delegate int _471D1D93(uint ADB9091C, ulong ulong_0, ulong ulong_1, ref IntPtr DD291639);
    public delegate int _2C27ED31(ulong ulong_0, ref IntPtr intptr_0);

    public struct F9B9578D
    {
        public C81A13A2 C8065D01;

        public ulong FD3A418A;

        public ulong F71B4B2F;

        public _812A630A _1719E61A;

        public _7F111085 _549E0429;

        public uint D5345515;

        public E927A3A9 _9DADCB04;

        public IntPtr _6F07BD9C;

        public _471D1D93 E5859D2F;

        public IntPtr _268CCE1B;

        public _2C27ED31 _80B7E005;

        public IntPtr _7D1FF19A;

        public int _0C31B78A;
    }

    public enum C81A13A2
    {
        _35376196,
        C7314688,
        _983A2108,
        _6CB75C37,
        _8AA19A95,
        _892D8608,
        _343399AF,
        _0392B91F
    }

    public enum _812A630A
    {
        CB880DAB,
        _67AA34BF,
        _47067D95,
        DDB4832C
    }

    public enum _7F111085
    {
        _1FB7183E,
        _45191C34,
        _410967B5,
        _6E32A88F,
        _1012A213,
        _750FE12A,
        _95259CB9,
        _399B1335
    }

    public struct _9A34C712
    {
        public uint _8E2F383C;

        public uint _9AA7F5AD;

        public ulong _8C9ED317;

        public uint FBAFAD09;

        public uint _59363A36;

        public uint _27A46338;

        public uint _3D87ECA9;

        public byte[] C91DE136;

        public uint C7A5D3B0;
    }

    public static class FlashToolLib_Params
    {
        public static int E1BCDA2E = 0;
        public static int _4B0D9E99;
    }


}

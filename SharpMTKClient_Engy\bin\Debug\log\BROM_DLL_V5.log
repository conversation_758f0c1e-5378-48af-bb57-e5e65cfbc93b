06/10/25 23:53:31.109 BROM_DLL[13512][13064]: <PERSON>rom_DebugOn(): runtime trace is ON now. (brom_debug.cpp:82)
06/10/25 23:53:31.109 BROM_DLL[13512][13064]: DEBUG: PrintOSVersion(): Microsoft Windows 10. (host_impl.cpp:493)
06/10/25 23:53:42.021 BROM_DLL[13512][30808]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/10/25 23:53:42.021 BROM_DLL[13512][30808]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Program Files (x86)\MotoKingPRO\bin\DA\universal\MTK_DA_V5.bin), m_is_need_pkg_buf(true), m_len(0x1571150 = 0). (flashtool_handle_internal.cpp:1542)
06/10/25 23:53:42.021 BROM_DLL[13512][30808]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Program Files (x86)\MotoKingPRO\bin\DA\universal\MTK_DA_V5.bin), need reloadall (flashtool_handle_internal.cpp:1550)
06/10/25 23:53:42.021 BROM_DLL[13512][30808]: DEBUG: FileLoadUnit::Load(): rom(C:\Program Files (x86)\MotoKingPRO\bin\DA\universal\MTK_DA_V5.bin) DL by package. (flashtool_handle_internal.cpp:953)
06/10/25 23:53:42.021 BROM_DLL[13512][30808]: ERROR: DA_HANDLE::LoadBuf(): da_buf((null)),uDaBufLen(22483280). (flashtool_handle_internal.cpp:5525)
06/10/25 23:53:42.021 BROM_DLL[13512][30808]: ERROR: DA_Load(): <ERR_CHECKPOINT>[260][error][1002]</ERR_CHECKPOINT> [S_INVALID_ARGUMENTS] (flashtool_handle.cpp:1556)
06/10/25 23:53:42.021 BROM_DLL[13512][30808]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)

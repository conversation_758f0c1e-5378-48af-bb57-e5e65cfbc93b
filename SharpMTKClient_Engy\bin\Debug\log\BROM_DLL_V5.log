06/11/25 17:29:31.392 BROM_DLL[33500][67768]: <PERSON>rom_DebugOn(): runtime trace is ON now. (brom_debug.cpp:82)
06/11/25 17:29:31.392 BROM_DLL[33500][67768]: DEBUG: PrintOSVersion(): Microsoft Windows 10. (host_impl.cpp:493)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: ERROR: FileLoadUnit::CheckIsNeedInitDLPackageResource(): open file(MTK_DOWNLOAD_AGENT) failed. (flashtool_handle_internal.cpp:1540)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(MTK_DOWNLOAD_AGENT), m_is_need_pkg_buf(false), m_len(0x0 = 0). (flashtool_handle_internal.cpp:1542)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: ERROR: file_open_sentry::file_open_sentry(): fopen("MTK_DOWNLOAD_AGENT", "rb"): fail! (flashtool_handle_internal.h:1646)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: ERROR: LoadFile(): NULL == fp! (flashtool_handle_internal.cpp:7668)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: ERROR: Load(): fail to load from "MTK_DOWNLOAD_AGENT" !!!      (call from: flashtool_handle_internal.cpp:5498)  (flashtool_handle_internal.cpp:941)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: ERROR: DA_Load(): <ERR_CHECKPOINT>[260][error][5008]</ERR_CHECKPOINT> [S_FTHND_FILE_LOAD_FAIL] (flashtool_handle.cpp:1556)
06/11/25 17:29:34.628 BROM_DLL[33500][16540]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: ERROR: FileLoadUnit::CheckIsNeedInitDLPackageResource(): open file(MTK_DOWNLOAD_AGENT) failed. (flashtool_handle_internal.cpp:1540)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(MTK_DOWNLOAD_AGENT), m_is_need_pkg_buf(false), m_len(0x0 = 0). (flashtool_handle_internal.cpp:1542)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: ERROR: file_open_sentry::file_open_sentry(): fopen("MTK_DOWNLOAD_AGENT", "rb"): fail! (flashtool_handle_internal.h:1646)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: ERROR: LoadFile(): NULL == fp! (flashtool_handle_internal.cpp:7668)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: ERROR: Load(): fail to load from "MTK_DOWNLOAD_AGENT" !!!      (call from: flashtool_handle_internal.cpp:5498)  (flashtool_handle_internal.cpp:941)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: ERROR: DA_Load(): <ERR_CHECKPOINT>[260][error][5008]</ERR_CHECKPOINT> [S_FTHND_FILE_LOAD_FAIL] (flashtool_handle.cpp:1556)
06/11/25 17:29:34.628 BROM_DLL[33500][65076]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: FileLoadUnit::CheckIsNeedInitDLPackageResource(): open file(MTK_DOWNLOAD_AGENT) failed. (flashtool_handle_internal.cpp:1540)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(MTK_DOWNLOAD_AGENT), m_is_need_pkg_buf(false), m_len(0x0 = 0). (flashtool_handle_internal.cpp:1542)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: file_open_sentry::file_open_sentry(): fopen("MTK_DOWNLOAD_AGENT", "rb"): fail! (flashtool_handle_internal.h:1646)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: LoadFile(): NULL == fp! (flashtool_handle_internal.cpp:7668)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: Load(): fail to load from "MTK_DOWNLOAD_AGENT" !!!      (call from: flashtool_handle_internal.cpp:5498)  (flashtool_handle_internal.cpp:941)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: DA_Load(): <ERR_CHECKPOINT>[260][error][5008]</ERR_CHECKPOINT> [S_FTHND_FILE_LOAD_FAIL] (flashtool_handle.cpp:1556)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: FileLoadUnit::CheckIsNeedInitDLPackageResource(): open file(MTK_DOWNLOAD_AGENT) failed. (flashtool_handle_internal.cpp:1540)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(MTK_DOWNLOAD_AGENT), m_is_need_pkg_buf(false), m_len(0x0 = 0). (flashtool_handle_internal.cpp:1542)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: file_open_sentry::file_open_sentry(): fopen("MTK_DOWNLOAD_AGENT", "rb"): fail! (flashtool_handle_internal.h:1646)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: LoadFile(): NULL == fp! (flashtool_handle_internal.cpp:7668)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: Load(): fail to load from "MTK_DOWNLOAD_AGENT" !!!      (call from: flashtool_handle_internal.cpp:5498)  (flashtool_handle_internal.cpp:941)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: ERROR: DA_Load(): <ERR_CHECKPOINT>[260][error][5008]</ERR_CHECKPOINT> [S_FTHND_FILE_LOAD_FAIL] (flashtool_handle.cpp:1556)
06/11/25 17:29:34.629 BROM_DLL[33500][65076]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)

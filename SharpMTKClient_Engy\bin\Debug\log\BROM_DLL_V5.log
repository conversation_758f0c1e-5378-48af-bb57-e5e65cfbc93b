06/10/25 23:12:50.298 BROM_DLL[34980][32268]: <PERSON>rom_DebugOn(): runtime trace is ON now. (brom_debug.cpp:82)
06/10/25 23:12:50.298 BROM_DLL[34980][32268]: DEBUG: PrintOSVersion(): Microsoft Windows 10. (host_impl.cpp:493)
06/10/25 23:12:55.711 BROM_DLL[34980][32268]: DA_Unload(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/10/25 23:12:55.711 BROM_DLL[34980][32268]: DA_Unload(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
06/10/25 23:12:55.712 BROM_DLL[34980][32268]: DL_Rom_UnloadAll(): DL_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/10/25 23:12:55.712 BROM_DLL[34980][32268]: DL_Rom_UnloadAll(): DL_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)

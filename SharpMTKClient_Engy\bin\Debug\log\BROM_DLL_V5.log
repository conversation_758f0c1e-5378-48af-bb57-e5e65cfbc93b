04/17/25 19:43:19.570 BROM_DLL[45824][59884]: Brom_DebugOn(): runtime trace is ON now. (brom_debug.cpp:82)
04/17/25 19:43:19.570 BROM_DLL[45824][59884]: DEBUG: PrintOSVersion(): Microsoft Windows 10. (host_impl.cpp:493)
04/17/25 19:43:22.691 BROM_DLL[45824][47888]: DL_LoadScatter(): DL_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:22.691 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::LoadScatter(): scatter_filepath(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\MT6765_Android_scatter.txt) (flashtool_handle_internal.cpp:2646)
04/17/25 19:43:22.691 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::LayoutConfigFileParser::LayoutConfigFilePareserCreator(): Layout configuration file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\MT6765_Android_scatter.txt). (LayoutConfigFileParser.cpp:145)
04/17/25 19:43:22.691 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::LayoutConfigFileChecker::IsConfigFile(): - general: MTK_PLATFORM_CFG is found at (0). (LayoutConfigFileParser.cpp:90)
04/17/25 19:43:22.691 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::LayoutConfigFileChecker::UpdateLayoutFileType(): Layout file type(LAYOUT_CONFIG_FILE). (LayoutConfigFileParser.cpp:51)
04/17/25 19:43:22.691 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::LayoutConfigFileParser::LayoutConfigFileParser(): Layout file name(MT6765_Android_scatter.txt). (LayoutConfigFileParser.cpp:134)
04/17/25 19:43:22.691 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::SmartPhoneLayoutCfgParser::Parse(): m_config_file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\MT6765_Android_scatter.txt). (LayoutConfigFileParser.cpp:488)
04/17/25 19:43:22.705 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::SPLayoutCfgGeneralSettingParserBase::Parse(): get LAYOUT_SETTING_SKIP_PMT_OPERATE:yaml-cpp: error at line 8, column 7: key not found: skip_pmt_operate. (LayoutConfigFileParser.cpp:770)
04/17/25 19:43:22.705 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::SPLayoutCfgGeneralSettingParserBase::Parse(): get skip DL info option:yaml-cpp: error at line 8, column 7: key not found: skip_write_dl_info. (LayoutConfigFileParser.cpp:781)
04/17/25 19:43:22.705 BROM_DLL[45824][47888]: WARN: SPFlashTool::SPLayoutCfgGeneralSettingParserBase::Parse(): yaml-cpp: error at line 8, column 7: key not found: type. (LayoutConfigFileParser.cpp:792)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryBBChipEnumValue(): BBChip(MT6765) (LayoutConfigFileSetting.cpp:127)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryBootChannelEnumValue(): BootChannel(MSDC_0) (LayoutConfigFileSetting.cpp:109)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.706 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: LayoutSetting::LayoutInfoToEnum::QueryStorageEnumValue(): storage(EMMC) (LayoutConfigFileSetting.cpp:118)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::LoadScatter(): is_combo_nand(1) (flashtool_handle_internal.cpp:2849)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::ScatterValidateCheck(): ScatterValidateCheck  enter. (flashtool_handle_internal.cpp:2973)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::ScatterValidateCheck(): ScatterValidateCheck  leave. (flashtool_handle_internal.cpp:3055)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: ===================== DL_HANDLE(0x0AF0FF50)::DumpDebug() ===================== (flashtool_handle_internal.cpp:5171)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [0]: preloader     ,   0x0000000000000000 = (0x0000000000000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [1]: pgpt          ,   0x0000000000000000 = (0x0000000000000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [2]: boot_para     ,   0x0000000000008000 = (0x0000000000008000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [3]: para          ,   0x0000000000108000 = (0x0000000000108000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [4]: elabel        ,   0x0000000000188000 = (0x0000000000188000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [5]: journey_persist,   0x0000000000988000 = (0x0000000000988000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [6]: expdb         ,   0x0000000003988000 = (0x0000000003988000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [7]: frp           ,   0x0000000004D88000 = (0x0000000004D88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [8]: nvcfg         ,   0x0000000004E88000 = (0x0000000004E88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [9]: nvdata        ,   0x0000000006E88000 = (0x0000000006E88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [10]: md_udc        ,   0x000000000AE88000 = (0x000000000AE88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [11]: metadata      ,   0x000000000C522000 = (0x000000000C522000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [12]: protect1      ,   0x000000000E522000 = (0x000000000E522000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [13]: protect2      ,   0x000000000ED22000 = (0x000000000ED22000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [14]: seccfg        ,   0x000000000F800000 = (0x000000000F800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [15]: persist       ,   0x0000000010000000 = (0x0000000010000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [16]: sec1          ,   0x0000000013000000 = (0x0000000013000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [17]: proinfo       ,   0x0000000013200000 = (0x0000000013200000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [18]: misc2         ,   0x0000000013500000 = (0x0000000013500000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [19]: signinfo      ,   0x0000000013800000 = (0x0000000013800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [20]: nvram         ,   0x0000000013B00000 = (0x0000000013B00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [21]: logo          ,   0x0000000017B00000 = (0x0000000017B00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [22]: md1img_a      ,   0x0000000018800000 = (0x0000000018800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [23]: spmfw_a       ,   0x000000001EC00000 = (0x000000001EC00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [24]: scp_a         ,   0x000000001ED00000 = (0x000000001ED00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [25]: sspm_a        ,   0x000000001EE00000 = (0x000000001EE00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [26]: gz_a          ,   0x000000001EF00000 = (0x000000001EF00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [27]: lk_a          ,   0x000000001FF00000 = (0x000000001FF00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [28]: boot_a        ,   0x0000000020000000 = (0x0000000020000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [29]: vendor_boot_a ,   0x0000000022000000 = (0x0000000022000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [30]: dtbo_a        ,   0x0000000026000000 = (0x0000000026000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [31]: tee_a         ,   0x0000000026800000 = (0x0000000026800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [32]: vbmeta_a      ,   0x0000000026D00000 = (0x0000000026D00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [33]: vbmeta_system_a,   0x0000000027500000 = (0x0000000027500000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [34]: vbmeta_vendor_a,   0x0000000027D00000 = (0x0000000027D00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [35]: md1img_b      ,   0x0000000028800000 = (0x0000000028800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [36]: spmfw_b       ,   0x000000002EC00000 = (0x000000002EC00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [37]: scp_b         ,   0x000000002ED00000 = (0x000000002ED00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [38]: sspm_b        ,   0x000000002EE00000 = (0x000000002EE00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [39]: gz_b          ,   0x000000002EF00000 = (0x000000002EF00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [40]: lk_b          ,   0x000000002FF00000 = (0x000000002FF00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [41]: boot_b        ,   0x0000000030000000 = (0x0000000030000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [42]: vendor_boot_b ,   0x0000000032000000 = (0x0000000032000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [43]: dtbo_b        ,   0x0000000036000000 = (0x0000000036000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [44]: tee_b         ,   0x0000000036800000 = (0x0000000036800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [45]: super         ,   0x0000000037000000 = (0x0000000037000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [46]: vbmeta_b      ,   0x0000000177000000 = (0x0000000177000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [47]: vbmeta_system_b,   0x0000000177800000 = (0x0000000177800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [48]: vbmeta_vendor_b,   0x0000000178000000 = (0x0000000178000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [49]: userdata      ,   0x0000000178800000 = (0x0000000178800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: ========================================================================== (flashtool_handle_internal.cpp:5183)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_LoadScatter(): DL_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
04/17/25 19:43:22.707 BROM_DLL[45824][47888]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DL_AutoLoad(): DL_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::LayoutConfigFileChecker::IsConfigFile(): - general: MTK_PLATFORM_CFG is found at (0). (LayoutConfigFileParser.cpp:90)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: SPFlashTool::LayoutConfigFileChecker::UpdateLayoutFileType(): Layout file type(LAYOUT_CONFIG_FILE). (LayoutConfigFileParser.cpp:51)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(0), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\preloader_p410ae.bin) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\preloader_p410ae.bin), m_is_need_pkg_buf(false), m_len(0x3ee18 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\preloader_p410ae.bin", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(257560). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = preloader (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::UpdateRomFileInfoByPreloader(): UpdateRomFileInfoByPreloader get bbchiptype : 196 (flashtool_handle_internal.cpp:4346)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\preloader_p410ae.bin", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 257560 (virtual_io.cpp:259)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\preloader_p410ae.bin", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 257560 (virtual_io.cpp:259)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.708 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [0]: preloader     ,   0x0000000000000000 = (0x0000000000000000->0x000000000003EE17), m_enable(FALSE), m_buf(0x0AF90FD0), IsDLByPkg(false), m_len(0x000000000003EE18)=257560, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\preloader_p410ae.bin"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.709 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(21), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\logo-verified.bin) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.709 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\logo-verified.bin), m_is_need_pkg_buf(false), m_len(0x62ad10 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.709 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\logo-verified.bin", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.709 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(6466832). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = logo (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\logo-verified.bin", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 6466832 (virtual_io.cpp:259)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\logo-verified.bin", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 6466832 (virtual_io.cpp:259)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::GetDLFileContentByOffset(): read through the end of file: m_file_len = 0x62ad10, offset + buf_len = 0x0 (flashtool_handle_internal.cpp:1302)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- fail to get extension header (sec_util.cpp:177)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [21]: logo          ,   0x0000000017B00000 = (0x0000000017B00000->0x000000001812AD0F), m_enable(FALSE), m_buf(0x10DAE020), IsDLByPkg(false), m_len(0x000000000062AD10)=6466832, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\logo-verified.bin"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(22), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img), m_is_need_pkg_buf(true), m_len(0x2fe6df0 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img), need reloadall (flashtool_handle_internal.cpp:1550)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::Load(): rom(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img) DL by package. (flashtool_handle_internal.cpp:953)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = md1img_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 50228720 (virtual_io.cpp:259)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 50228720 (virtual_io.cpp:259)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [22]: md1img_a      ,   0x0000000018800000 = (0x0000000018800000->0x000000001B7E6DEF), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(true), m_len(0x0000000002FE6DF0)=50228720, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(23), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\spmfw-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.711 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\spmfw-verified.img), m_is_need_pkg_buf(false), m_len(0xfa80 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\spmfw-verified.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(64128). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = spmfw_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\spmfw-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 64128 (virtual_io.cpp:259)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\spmfw-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 64128 (virtual_io.cpp:259)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::GetDLFileContentByOffset(): read through the end of file: m_file_len = 0xfa80, offset + buf_len = 0x0 (flashtool_handle_internal.cpp:1302)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- fail to get extension header (sec_util.cpp:177)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [23]: spmfw_a       ,   0x000000001EC00000 = (0x000000001EC00000->0x000000001EC0FA7F), m_enable(FALSE), m_buf(0x0AF40C98), IsDLByPkg(false), m_len(0x000000000000FA80)=64128, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\spmfw-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(24), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\scp-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\scp-verified.img), m_is_need_pkg_buf(false), m_len(0xa7330 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\scp-verified.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(684848). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = scp_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\scp-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 684848 (virtual_io.cpp:259)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\scp-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 684848 (virtual_io.cpp:259)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::GetDLFileContentByOffset(): read through the end of file: m_file_len = 0xa7330, offset + buf_len = 0x0 (flashtool_handle_internal.cpp:1302)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- fail to get extension header (sec_util.cpp:177)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [24]: scp_a         ,   0x000000001ED00000 = (0x000000001ED00000->0x000000001EDA732F), m_enable(FALSE), m_buf(0x07E3E020), IsDLByPkg(false), m_len(0x00000000000A7330)=684848, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\scp-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(25), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\sspm-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\sspm-verified.img), m_is_need_pkg_buf(false), m_len(0x6a290 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\sspm-verified.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(434832). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = sspm_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\sspm-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 434832 (virtual_io.cpp:259)
04/17/25 19:43:22.712 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\sspm-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 434832 (virtual_io.cpp:259)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::GetDLFileContentByOffset(): read through the end of file: m_file_len = 0x6a290, offset + buf_len = 0x0 (flashtool_handle_internal.cpp:1302)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- fail to get extension header (sec_util.cpp:177)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [25]: sspm_a        ,   0x000000001EE00000 = (0x000000001EE00000->0x000000001EE6A28F), m_enable(FALSE), m_buf(0x113E0048), IsDLByPkg(false), m_len(0x000000000006A290)=434832, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\sspm-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(26), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\gz-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\gz-verified.img), m_is_need_pkg_buf(false), m_len(0x2b3610 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\gz-verified.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.713 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(2831888). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = gz_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\gz-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 2831888 (virtual_io.cpp:259)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\gz-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 2831888 (virtual_io.cpp:259)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::GetDLFileContentByOffset(): read through the end of file: m_file_len = 0x2b3610, offset + buf_len = 0x0 (flashtool_handle_internal.cpp:1302)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- fail to get extension header (sec_util.cpp:177)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [26]: gz_a          ,   0x000000001EF00000 = (0x000000001EF00000->0x000000001F1B360F), m_enable(FALSE), m_buf(0x115EA020), IsDLByPkg(false), m_len(0x00000000002B3610)=2831888, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\gz-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(27), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\lk-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\lk-verified.img), m_is_need_pkg_buf(false), m_len(0xe9490 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\lk-verified.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(955536). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = lk_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\lk-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 955536 (virtual_io.cpp:259)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\lk-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 955536 (virtual_io.cpp:259)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::GetDLFileContentByOffset(): read through the end of file: m_file_len = 0xe9490, offset + buf_len = 0x0 (flashtool_handle_internal.cpp:1302)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- fail to get extension header (sec_util.cpp:177)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [27]: lk_a          ,   0x000000001FF00000 = (0x000000001FF00000->0x000000001FFE948F), m_enable(FALSE), m_buf(0x118AD020), IsDLByPkg(false), m_len(0x00000000000E9490)=955536, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\lk-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(28), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img), m_is_need_pkg_buf(true), m_len(0x2000000 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img), need reloadall (flashtool_handle_internal.cpp:1550)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::Load(): rom(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img) DL by package. (flashtool_handle_internal.cpp:953)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = boot_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 33554432 (virtual_io.cpp:259)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 33554432 (virtual_io.cpp:259)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 66 (virtual_io.cpp:257)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 66 (virtual_io.cpp:258)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 33554432 (virtual_io.cpp:259)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- corrupted extension header (sec_util.cpp:182)
04/17/25 19:43:22.714 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [28]: boot_a        ,   0x0000000020000000 = (0x0000000020000000->0x0000000021FFFFFF), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(true), m_len(0x0000000002000000)=33554432, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(30), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img), m_is_need_pkg_buf(false), m_len(0x13490 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(78992). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = dtbo_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 78992 (virtual_io.cpp:259)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 78992 (virtual_io.cpp:259)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 64 (virtual_io.cpp:257)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 64 (virtual_io.cpp:258)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 78992 (virtual_io.cpp:259)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- corrupted extension header (sec_util.cpp:182)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [30]: dtbo_a        ,   0x0000000026000000 = (0x0000000026000000->0x000000002601348F), m_enable(FALSE), m_buf(0x0AFCFDF0), IsDLByPkg(false), m_len(0x0000000000013490)=78992, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(31), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\tee-verified.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\tee-verified.img), m_is_need_pkg_buf(false), m_len(0xda140 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\tee-verified.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(893248). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = tee_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\tee-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 893248 (virtual_io.cpp:259)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.715 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\tee-verified.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 893248 (virtual_io.cpp:259)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- sign image v3 (sec_util.cpp:166)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::GetDLFileContentByOffset(): read through the end of file: m_file_len = 0xda140, offset + buf_len = 0x0 (flashtool_handle_internal.cpp:1302)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: lib_sec_get_metadata_size -- fail to get extension header (sec_util.cpp:177)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [31]: tee_a         ,   0x0000000026800000 = (0x0000000026800000->0x00000000268DA13F), m_enable(FALSE), m_buf(0x119A5020), IsDLByPkg(false), m_len(0x00000000000DA140)=893248, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\tee-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(32), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta.img), m_is_need_pkg_buf(false), m_len(0x1000 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(4096). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = vbmeta_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 4096 (virtual_io.cpp:259)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 4096 (virtual_io.cpp:259)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [32]: vbmeta_a      ,   0x0000000026D00000 = (0x0000000026D00000->0x0000000026D00FFF), m_enable(FALSE), m_buf(0x0AF5A108), IsDLByPkg(false), m_len(0x0000000000001000)=4096, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(33), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_system.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_system.img), m_is_need_pkg_buf(false), m_len(0x1000 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_system.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(4096). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = vbmeta_system_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_system.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 4096 (virtual_io.cpp:259)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_system.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 4096 (virtual_io.cpp:259)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [33]: vbmeta_system_a,   0x0000000027500000 = (0x0000000027500000->0x0000000027500FFF), m_enable(FALSE), m_buf(0x0AF580F8), IsDLByPkg(false), m_len(0x0000000000001000)=4096, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_system.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(34), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_vendor.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_vendor.img), m_is_need_pkg_buf(false), m_len(0x1000 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_vendor.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(4096). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = vbmeta_vendor_a (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_vendor.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 4096 (virtual_io.cpp:259)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_vendor.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 4096 (virtual_io.cpp:259)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [34]: vbmeta_vendor_a,   0x0000000027D00000 = (0x0000000027D00000->0x0000000027D00FFF), m_enable(FALSE), m_buf(0x0AF570F0), IsDLByPkg(false), m_len(0x0000000000001000)=4096, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_vendor.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(45), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img), m_is_need_pkg_buf(true), m_len(0x3005ce70 = 1). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img), need reloadall (flashtool_handle_internal.cpp:1550)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::Load(): rom(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img) DL by package. (flashtool_handle_internal.cpp:953)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = super (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.716 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 5100654192 (virtual_io.cpp:259)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: IO_FILE::IO_FILE(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img", "rb"): OK!, m_fp(0x7ABE0EE8). (virtual_io.cpp:96)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_ReadMode: 1 (virtual_io.cpp:255)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): feof(m_fp): 0 (virtual_io.cpp:256)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): Telli64(m_fp): 0 (virtual_io.cpp:257)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): (u64)Telli64(m_fp): 0 (virtual_io.cpp:258)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: IO_FILE::IsEnd(): m_filelength: 5100654192 (virtual_io.cpp:259)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: IO_FILE::~IO_FILE(): fclose(m_fp(0x7ABE0EE8)): OK! (virtual_io.cpp:112)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [45]: super         ,   0x0000000037000000 = (0x0000000037000000->0x000000016705CE6F), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(true), m_len(0x000000013005CE70)=5100654192, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): index(49), name(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\userdata.img) (flashtool_handle_internal.cpp:4674)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\userdata.img), m_is_need_pkg_buf(false), m_len(0xfa0 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\userdata.img", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::ObtainFileLength(): file length(4000). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DL_HANDLE()::Rom_Load(): ROM loaded, name = userdata (flashtool_handle_internal.cpp:4680)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [49]: userdata      ,   0x0000000178800000 = (0x0000000178800000->0x0000000178800F9F), m_enable(FALSE), m_buf(0x1144A2E0), IsDLByPkg(false), m_len(0x0000000000000FA0)=4000, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\userdata.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.717 BROM_DLL[45824][47888]: DL_AutoLoad(): DL_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_GetCount(): DL_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): rom list count: p_rom_count(0x07C7F134)=50 (flashtool_handle_internal.cpp:3117)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): super boost count 0. (flashtool_handle_internal.cpp:3119)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): RSV rom count 3. (flashtool_handle_internal.cpp:3121)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): DL_HANDLE(0x0AF0FF50)::GetAllRomPartCount(): p_rom_count(0x07C7F134)=53. (flashtool_handle_internal.cpp:3123)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_GetCount(): DL_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_Rom_GetInfoAll(): DL_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): rom list count: p_rom_count(0x07C7F0D8)=50 (flashtool_handle_internal.cpp:3117)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): super boost count 0. (flashtool_handle_internal.cpp:3119)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): RSV rom count 3. (flashtool_handle_internal.cpp:3121)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DEBUG: DL_HANDLE::GetAllRomPartCount(): DL_HANDLE(0x0AF0FF50)::GetAllRomPartCount(): p_rom_count(0x07C7F0D8)=53. (flashtool_handle_internal.cpp:3123)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [0]: preloader     ,   0x0000000000000000 = (0x0000000000000000->0x000000000003EE17), m_enable(TRUE ), m_buf(0x0AF90FD0), IsDLByPkg(false), m_len(0x000000000003EE18)=257560, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\preloader_p410ae.bin"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [1]: pgpt          ,   0x0000000000000000 = (0x0000000000000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [2]: boot_para     ,   0x0000000000008000 = (0x0000000000008000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [3]: para          ,   0x0000000000108000 = (0x0000000000108000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [4]: elabel        ,   0x0000000000188000 = (0x0000000000188000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [5]: journey_persist,   0x0000000000988000 = (0x0000000000988000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [6]: expdb         ,   0x0000000003988000 = (0x0000000003988000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [7]: frp           ,   0x0000000004D88000 = (0x0000000004D88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [8]: nvcfg         ,   0x0000000004E88000 = (0x0000000004E88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [9]: nvdata        ,   0x0000000006E88000 = (0x0000000006E88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [10]: md_udc        ,   0x000000000AE88000 = (0x000000000AE88000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [11]: metadata      ,   0x000000000C522000 = (0x000000000C522000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [12]: protect1      ,   0x000000000E522000 = (0x000000000E522000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [13]: protect2      ,   0x000000000ED22000 = (0x000000000ED22000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [14]: seccfg        ,   0x000000000F800000 = (0x000000000F800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [15]: persist       ,   0x0000000010000000 = (0x0000000010000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [16]: sec1          ,   0x0000000013000000 = (0x0000000013000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [17]: proinfo       ,   0x0000000013200000 = (0x0000000013200000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [18]: misc2         ,   0x0000000013500000 = (0x0000000013500000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [19]: signinfo      ,   0x0000000013800000 = (0x0000000013800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [20]: nvram         ,   0x0000000013B00000 = (0x0000000013B00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [21]: logo          ,   0x0000000017B00000 = (0x0000000017B00000->0x000000001812AD0F), m_enable(TRUE ), m_buf(0x10DAE020), IsDLByPkg(false), m_len(0x000000000062AD10)=6466832, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\logo-verified.bin"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [22]: md1img_a      ,   0x0000000018800000 = (0x0000000018800000->0x000000001B7E6DEF), m_enable(TRUE ), m_buf(0x00000000), IsDLByPkg(true), m_len(0x0000000002FE6DF0)=50228720, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\md1img-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [23]: spmfw_a       ,   0x000000001EC00000 = (0x000000001EC00000->0x000000001EC0FA7F), m_enable(TRUE ), m_buf(0x0AF40C98), IsDLByPkg(false), m_len(0x000000000000FA80)=64128, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\spmfw-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [24]: scp_a         ,   0x000000001ED00000 = (0x000000001ED00000->0x000000001EDA732F), m_enable(TRUE ), m_buf(0x07E3E020), IsDLByPkg(false), m_len(0x00000000000A7330)=684848, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\scp-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [25]: sspm_a        ,   0x000000001EE00000 = (0x000000001EE00000->0x000000001EE6A28F), m_enable(TRUE ), m_buf(0x113E0048), IsDLByPkg(false), m_len(0x000000000006A290)=434832, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\sspm-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [26]: gz_a          ,   0x000000001EF00000 = (0x000000001EF00000->0x000000001F1B360F), m_enable(TRUE ), m_buf(0x115EA020), IsDLByPkg(false), m_len(0x00000000002B3610)=2831888, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\gz-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [27]: lk_a          ,   0x000000001FF00000 = (0x000000001FF00000->0x000000001FFE948F), m_enable(TRUE ), m_buf(0x118AD020), IsDLByPkg(false), m_len(0x00000000000E9490)=955536, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\lk-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [28]: boot_a        ,   0x0000000020000000 = (0x0000000020000000->0x0000000021FFFFFF), m_enable(TRUE ), m_buf(0x00000000), IsDLByPkg(true), m_len(0x0000000002000000)=33554432, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\boot.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [29]: vendor_boot_a ,   0x0000000022000000 = (0x0000000022000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [30]: dtbo_a        ,   0x0000000026000000 = (0x0000000026000000->0x000000002601348F), m_enable(TRUE ), m_buf(0x0AFCFDF0), IsDLByPkg(false), m_len(0x0000000000013490)=78992, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\dtbo-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [31]: tee_a         ,   0x0000000026800000 = (0x0000000026800000->0x00000000268DA13F), m_enable(TRUE ), m_buf(0x119A5020), IsDLByPkg(false), m_len(0x00000000000DA140)=893248, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\tee-verified.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [32]: vbmeta_a      ,   0x0000000026D00000 = (0x0000000026D00000->0x0000000026D00FFF), m_enable(TRUE ), m_buf(0x0AF5A108), IsDLByPkg(false), m_len(0x0000000000001000)=4096, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [33]: vbmeta_system_a,   0x0000000027500000 = (0x0000000027500000->0x0000000027500FFF), m_enable(TRUE ), m_buf(0x0AF580F8), IsDLByPkg(false), m_len(0x0000000000001000)=4096, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_system.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [34]: vbmeta_vendor_a,   0x0000000027D00000 = (0x0000000027D00000->0x0000000027D00FFF), m_enable(TRUE ), m_buf(0x0AF570F0), IsDLByPkg(false), m_len(0x0000000000001000)=4096, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\vbmeta_vendor.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [35]: md1img_b      ,   0x0000000028800000 = (0x0000000028800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [36]: spmfw_b       ,   0x000000002EC00000 = (0x000000002EC00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [37]: scp_b         ,   0x000000002ED00000 = (0x000000002ED00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [38]: sspm_b        ,   0x000000002EE00000 = (0x000000002EE00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [39]: gz_b          ,   0x000000002EF00000 = (0x000000002EF00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [40]: lk_b          ,   0x000000002FF00000 = (0x000000002FF00000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [41]: boot_b        ,   0x0000000030000000 = (0x0000000030000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [42]: vendor_boot_b ,   0x0000000032000000 = (0x0000000032000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [43]: dtbo_b        ,   0x0000000036000000 = (0x0000000036000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [44]: tee_b         ,   0x0000000036800000 = (0x0000000036800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [45]: super         ,   0x0000000037000000 = (0x0000000037000000->0x000000016705CE6F), m_enable(TRUE ), m_buf(0x00000000), IsDLByPkg(true), m_len(0x000000013005CE70)=5100654192, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\super.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [46]: vbmeta_b      ,   0x0000000177000000 = (0x0000000177000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [47]: vbmeta_system_b,   0x0000000177800000 = (0x0000000177800000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [48]: vbmeta_vendor_b,   0x0000000178000000 = (0x0000000178000000->0x0000000000000000), m_enable(FALSE), m_buf(0x00000000), IsDLByPkg(false), m_len(0x0000000000000000)=0, file(""). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_HANDLE(0x0AF0FF50): [49]: userdata      ,   0x0000000178800000 = (0x0000000178800000->0x0000000178800F9F), m_enable(TRUE ), m_buf(0x1144A2E0), IsDLByPkg(false), m_len(0x0000000000000FA0)=4000, file("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\userdata.img"). (flashtool_handle_internal.cpp:5175)
04/17/25 19:43:22.724 BROM_DLL[45824][47888]: DL_Rom_GetInfoAll(): DL_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_Unload(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_Unload(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin), m_is_need_pkg_buf(false), m_len(0x89740 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: file_open_sentry::ObtainFileLength(): file length(563008). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: WARNING: DA_HANDLE(0x0AF120C0):Load(): DA validation is bypassed.  (flashtool_handle_internal.cpp:5543)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntrySet::Validation(): m_da_identifier(MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327), m_da_description(MTK_DOWNLOAD_AGENT), m_info_ver(0x4), m_info_magic(0x22668899). (da_entry_base.cpp:313)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntrySet::Validation(): m_da_count(1). (da_entry_base.cpp:318)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntryV4::Validation(): DA(0x6765): m_load_regions_count(3). (da_entry_base.cpp:245)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(624), m_start_addr(1342177280), m_sig_offse(0), m_sig_len(0) (da_entry_base.cpp:254)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(236424), m_start_addr(2097152), m_sig_offse(236168), m_sig_len(256) (da_entry_base.cpp:254)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(311772), m_start_addr(1073741824), m_sig_offse(311516), m_sig_len(256) (da_entry_base.cpp:254)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntryBase::Dump(): m_magic(0xdada), m_entry_region_index(0x0). (da_entry_base.cpp:57)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntryBase::Dump(): m_bbchip_hw_code(0x6765), m_bbchip_hw_ver(0xca00), m_bbchip_sw_ver(0x0). (da_entry_base.cpp:59)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DEBUG: DAEntryV4::Dump(): m_bbchip_hw_sub_code(0x8a00), (da_entry_base.cpp:275)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_HANDLE(0x0AF120C0): DA("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin"): m_header={ "MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327", ver(4) }, sizeof(DAInfo_ST)=13932(0x0000366C), m_default_start_addr(0x40000800), m_flu.m_buf(0x11A9D020), m_flu.m_len(0x00089740)=0. (flashtool_handle_internal.cpp:5718)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_HANDLE(0x0AF120C0):     DA[0]={ magic(0xDADA), hw_code(0x6765), hw_ver(0xCA00), sw_ver(0x0000), entry_region_index(0) }.                                                                                                    (flashtool_handle_internal.cpp:5723)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_HANDLE(0x0AF120C0):          -> LoadRegion[0]={ m_buf(0x11AA078C), m_len(0x00000270)=624, m_start_addr(0x50000000), m_sig_offset(0x00000000)=0, m_sig_len(0x00000000)=0 }.          (flashtool_handle_internal.cpp:5726)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_HANDLE(0x0AF120C0):          -> LoadRegion[1]={ m_buf(0x11AA09FC), m_len(0x00039B88)=236424, m_start_addr(0x00200000), m_sig_offset(0x00039A88)=236168, m_sig_len(0x00000100)=256 }.          (flashtool_handle_internal.cpp:5726)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_HANDLE(0x0AF120C0):          -> LoadRegion[2]={ m_buf(0x11ADA584), m_len(0x0004C1DC)=311772, m_start_addr(0x40000000), m_sig_offset(0x0004C0DC)=311516, m_sig_len(0x00000100)=256 }.          (flashtool_handle_internal.cpp:5726)
04/17/25 19:43:25.156 BROM_DLL[45824][66436]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_Unload(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: Unload(): free(0x11A9D020) done.  (call from: flashtool_handle_internal.cpp:5618)  (flashtool_handle_internal.cpp:1128)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_Unload(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin), m_is_need_pkg_buf(false), m_len(0x89740 = 0). (flashtool_handle_internal.cpp:1542)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin", "rb"): OK!, fp(0x7ABE0EE8) (flashtool_handle_internal.h:1651)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: file_open_sentry::ObtainFileLength(): file length(563008). (flashtool_handle_internal.cpp:143)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7ABE0EE8) OK! (flashtool_handle_internal.h:1702)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: WARNING: DA_HANDLE(0x0AF120C0):Load(): DA validation is bypassed.  (flashtool_handle_internal.cpp:5543)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntrySet::Validation(): m_da_identifier(MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327), m_da_description(MTK_DOWNLOAD_AGENT), m_info_ver(0x4), m_info_magic(0x22668899). (da_entry_base.cpp:313)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntrySet::Validation(): m_da_count(1). (da_entry_base.cpp:318)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntryV4::Validation(): DA(0x6765): m_load_regions_count(3). (da_entry_base.cpp:245)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(624), m_start_addr(1342177280), m_sig_offse(0), m_sig_len(0) (da_entry_base.cpp:254)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(236424), m_start_addr(2097152), m_sig_offse(236168), m_sig_len(256) (da_entry_base.cpp:254)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(311772), m_start_addr(1073741824), m_sig_offse(311516), m_sig_len(256) (da_entry_base.cpp:254)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntryBase::Dump(): m_magic(0xdada), m_entry_region_index(0x0). (da_entry_base.cpp:57)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntryBase::Dump(): m_bbchip_hw_code(0x6765), m_bbchip_hw_ver(0xca00), m_bbchip_sw_ver(0x0). (da_entry_base.cpp:59)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DEBUG: DAEntryV4::Dump(): m_bbchip_hw_sub_code(0x8a00), (da_entry_base.cpp:275)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_HANDLE(0x0AF120C0): DA("C:\Users\<USER>\Downloads\STAS32.79-77-28-63-9_hawaiip_g_user_CID50_sign_20250301123505_SVC\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin"): m_header={ "MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327", ver(4) }, sizeof(DAInfo_ST)=13932(0x0000366C), m_default_start_addr(0x40000800), m_flu.m_buf(0x08B81020), m_flu.m_len(0x00089740)=0. (flashtool_handle_internal.cpp:5718)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_HANDLE(0x0AF120C0):     DA[0]={ magic(0xDADA), hw_code(0x6765), hw_ver(0xCA00), sw_ver(0x0000), entry_region_index(0) }.                                                                                                    (flashtool_handle_internal.cpp:5723)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_HANDLE(0x0AF120C0):          -> LoadRegion[0]={ m_buf(0x08B8478C), m_len(0x00000270)=624, m_start_addr(0x50000000), m_sig_offset(0x00000000)=0, m_sig_len(0x00000000)=0 }.          (flashtool_handle_internal.cpp:5726)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_HANDLE(0x0AF120C0):          -> LoadRegion[1]={ m_buf(0x08B849FC), m_len(0x00039B88)=236424, m_start_addr(0x00200000), m_sig_offset(0x00039A88)=236168, m_sig_len(0x00000100)=256 }.          (flashtool_handle_internal.cpp:5726)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_HANDLE(0x0AF120C0):          -> LoadRegion[2]={ m_buf(0x08BBE584), m_len(0x0004C1DC)=311772, m_start_addr(0x40000000), m_sig_offset(0x0004C0DC)=311516, m_sig_len(0x00000100)=256 }.          (flashtool_handle_internal.cpp:5726)
04/17/25 19:43:30.255 BROM_DLL[45824][44400]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
04/17/25 19:43:51.426 BROM_DLL[45824][56524]: DL_SetChecksumLevel(): DL_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:51.426 BROM_DLL[45824][56524]: DL_SetChecksumLevel(): DL_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
04/17/25 19:43:51.426 BROM_DLL[45824][56524]: Brom_DebugOn(): runtime trace is ON now. (brom_debug.cpp:82)
04/17/25 19:43:51.426 BROM_DLL[45824][56524]: DEBUG: PrintOSVersion(): Microsoft Windows 10. (host_impl.cpp:493)
04/17/25 19:43:51.429 BROM_DLL[45824][56524]: DEBUG: DA_cmd::DA_cmd(): m_p_stopflag(0x0F025B48)=0. (da_cmd.cpp:82)
04/17/25 19:43:51.429 BROM_DLL[45824][56524]: DEBUG: FLASHTOOL_API_HANDLE::FLASHTOOL_API_HANDLE(): m_da_report initialize to 0. (flashtool_api_internal.h:88)
04/17/25 19:43:51.429 BROM_DLL[45824][56524]: DEBUG: FLASHTOOL_API_HANDLE::FLASHTOOL_API_HANDLE(): m_boot_result initialize to 0. (flashtool_api_internal.h:90)
04/17/25 19:43:51.435 BROM_DLL[45824][56524]: AUTH_GetInfo(): AUTH_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
04/17/25 19:43:51.435 BROM_DLL[45824][56524]: ERROR: AUTH_GetInfo(): <ERR_CHECKPOINT>[293][error][5007]</ERR_CHECKPOINT> [S_FTHND_FILE_IS_NOT_LOADED_YET] (flashtool_handle.cpp:2266)
04/17/25 19:43:51.435 BROM_DLL[45824][56524]: AUTH_GetInfo(): AUTH_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
04/17/25 19:43:51.435 BROM_DLL[45824][56524]: ERROR: SCERT_GetInfo(): <ERR_CHECKPOINT>[300][error][1002]</ERR_CHECKPOINT> [S_INVALID_ARGUMENTS] (flashtool_handle.cpp:2431)
04/17/25 19:43:51.937 BROM_DLL[45824][56524]: FlashTool_GetDLHandle(0x1144B690): mutex: LOCK ... (mutex.cpp:143)
04/17/25 19:43:51.937 BROM_DLL[45824][56524]: FlashTool_GetDLHandle(0x1144B690): mutex: UNLOCK. (mutex.cpp:158)
04/17/25 19:43:51.937 BROM_DLL[45824][56524]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
04/17/25 19:43:51.937 BROM_DLL[45824][56524]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
04/17/25 19:43:51.937 BROM_DLL[45824][56524]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
04/17/25 19:43:51.937 BROM_DLL[45824][56524]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
04/17/25 19:43:51.946 BROM_DLL[45824][56524]: FlashTool_GetDLHandle(0x1144B690): mutex: LOCK ... (mutex.cpp:143)
04/17/25 19:43:51.946 BROM_DLL[45824][56524]: FlashTool_GetDLHandle(0x1144B690): mutex: UNLOCK. (mutex.cpp:158)
04/17/25 19:43:51.946 BROM_DLL[45824][56524]: DL_ClearFTHandle()DL_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
04/17/25 19:43:51.946 BROM_DLL[45824][56524]: DL_ClearFTHandle()DL_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)

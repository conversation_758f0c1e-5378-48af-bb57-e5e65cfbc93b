06/11/25 13:25:09.952 BROM_DLL[56300][42748]: <PERSON>rom_DebugOn(): runtime trace is ON now. (brom_debug.cpp:82)
06/11/25 13:25:09.952 BROM_DLL[56300][42748]: DEBUG: PrintOSVersion(): Microsoft Windows 10. (host_impl.cpp:493)
06/11/25 13:25:14.417 BROM_DLL[56300][43448]: DA_Load(): DA_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/11/25 13:25:14.417 BROM_DLL[56300][43448]: DEBUG: FileLoadUnit::CheckIsNeedInitDLPackageResource(): file(C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin), m_is_need_pkg_buf(false), m_len(0x89740 = 0). (flashtool_handle_internal.cpp:1542)
06/11/25 13:25:14.417 BROM_DLL[56300][43448]: DEBUG: file_open_sentry::file_open_sentry(): fopen("C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin", "rb"): OK!, fp(0x7BCD0EE8) (flashtool_handle_internal.h:1651)
06/11/25 13:25:14.417 BROM_DLL[56300][43448]: DEBUG: file_open_sentry::ObtainFileLength(): file length(563008). (flashtool_handle_internal.cpp:143)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: file_open_sentry::~file_open_sentry(): fclose(0x7BCD0EE8) OK! (flashtool_handle_internal.h:1702)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: WARNING: DA_HANDLE(0x0AFF2108):Load(): DA validation is bypassed.  (flashtool_handle_internal.cpp:5543)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntrySet::Validation(): m_da_identifier(MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327), m_da_description(MTK_DOWNLOAD_AGENT), m_info_ver(0x4), m_info_magic(0x22668899). (da_entry_base.cpp:313)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntrySet::Validation(): m_da_count(1). (da_entry_base.cpp:318)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntryV4::Validation(): DA(0x6765): m_load_regions_count(3). (da_entry_base.cpp:245)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(624), m_start_addr(1342177280), m_sig_offse(0), m_sig_len(0) (da_entry_base.cpp:254)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(236424), m_start_addr(2097152), m_sig_offse(236168), m_sig_len(256) (da_entry_base.cpp:254)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntryV4::LoadRegionValidation(): m_len(311772), m_start_addr(1073741824), m_sig_offse(311516), m_sig_len(256) (da_entry_base.cpp:254)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntryBase::Dump(): m_magic(0xdada), m_entry_region_index(0x0). (da_entry_base.cpp:57)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntryBase::Dump(): m_bbchip_hw_code(0x6765), m_bbchip_hw_ver(0xca00), m_bbchip_sw_ver(0x0). (da_entry_base.cpp:59)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DEBUG: DAEntryV4::Dump(): m_bbchip_hw_sub_code(0x8a00), (da_entry_base.cpp:275)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DA_HANDLE(0x0AFF2108): DA("C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin"): m_header={ "MTK_AllInOne_DA_v3.3001.2021/10/18.17:06_567327", ver(4) }, sizeof(DAInfo_ST)=13932(0x0000366C), m_default_start_addr(0x40000800), m_flu.m_buf(0x0D01A020), m_flu.m_len(0x00089740)=0. (flashtool_handle_internal.cpp:5718)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DA_HANDLE(0x0AFF2108):     DA[0]={ magic(0xDADA), hw_code(0x6765), hw_ver(0xCA00), sw_ver(0x0000), entry_region_index(0) }.                                                                                                    (flashtool_handle_internal.cpp:5723)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DA_HANDLE(0x0AFF2108):          -> LoadRegion[0]={ m_buf(0x0D01D78C), m_len(0x00000270)=624, m_start_addr(0x50000000), m_sig_offset(0x00000000)=0, m_sig_len(0x00000000)=0 }.          (flashtool_handle_internal.cpp:5726)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DA_HANDLE(0x0AFF2108):          -> LoadRegion[1]={ m_buf(0x0D01D9FC), m_len(0x00039B88)=236424, m_start_addr(0x00200000), m_sig_offset(0x00039A88)=236168, m_sig_len(0x00000100)=256 }.          (flashtool_handle_internal.cpp:5726)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DA_HANDLE(0x0AFF2108):          -> LoadRegion[2]={ m_buf(0x0D057584), m_len(0x0004C1DC)=311772, m_start_addr(0x40000000), m_sig_offset(0x0004C0DC)=311516, m_sig_len(0x00000100)=256 }.          (flashtool_handle_internal.cpp:5726)
06/11/25 13:25:14.418 BROM_DLL[56300][43448]: DA_Load(): DA_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
06/11/25 13:25:32.459 BROM_DLL[56300][42748]: DL_SetChecksumLevel(): DL_HANDLE->rwlock: WRITE_LOCK ... (rwlock.cpp:460)
06/11/25 13:25:32.459 BROM_DLL[56300][42748]: DL_SetChecksumLevel(): DL_HANDLE->rwlock: WRITE_UNLOCK. (rwlock.cpp:476)
06/11/25 13:25:32.460 BROM_DLL[56300][42748]: Brom_DebugOn(): runtime trace is ON now. (brom_debug.cpp:82)
06/11/25 13:25:32.460 BROM_DLL[56300][42748]: DEBUG: PrintOSVersion(): Microsoft Windows 10. (host_impl.cpp:493)
06/11/25 13:25:32.753 BROM_DLL[56300][42748]: DEBUG: DA_cmd::DA_cmd(): m_p_stopflag(0x0B73B590)=0. (da_cmd.cpp:82)
06/11/25 13:25:32.753 BROM_DLL[56300][42748]: DEBUG: FLASHTOOL_API_HANDLE::FLASHTOOL_API_HANDLE(): m_da_report initialize to 0. (flashtool_api_internal.h:88)
06/11/25 13:25:32.753 BROM_DLL[56300][42748]: DEBUG: FLASHTOOL_API_HANDLE::FLASHTOOL_API_HANDLE(): m_boot_result initialize to 0. (flashtool_api_internal.h:90)
06/11/25 13:25:32.754 BROM_DLL[56300][42748]: AUTH_GetInfo(): AUTH_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
06/11/25 13:25:32.754 BROM_DLL[56300][42748]: ERROR: AUTH_GetInfo(): <ERR_CHECKPOINT>[293][error][5007]</ERR_CHECKPOINT> [S_FTHND_FILE_IS_NOT_LOADED_YET] (flashtool_handle.cpp:2266)
06/11/25 13:25:32.754 BROM_DLL[56300][42748]: AUTH_GetInfo(): AUTH_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
06/11/25 13:25:32.754 BROM_DLL[56300][42748]: ERROR: SCERT_GetInfo(): <ERR_CHECKPOINT>[300][error][1002]</ERR_CHECKPOINT> [S_INVALID_ARGUMENTS] (flashtool_handle.cpp:2431)
06/11/25 13:25:33.209 BROM_DLL[56300][42748]: FlashTool_GetDLHandle(0x0B001180): mutex: LOCK ... (mutex.cpp:143)
06/11/25 13:25:33.209 BROM_DLL[56300][42748]: FlashTool_GetDLHandle(0x0B001180): mutex: UNLOCK. (mutex.cpp:158)
06/11/25 13:25:33.209 BROM_DLL[56300][42748]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
06/11/25 13:25:33.209 BROM_DLL[56300][42748]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)
06/11/25 13:25:33.209 BROM_DLL[56300][42748]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_LOCK ... (rwlock.cpp:407)
06/11/25 13:25:33.209 BROM_DLL[56300][42748]: DL_Get_PlatformName(): DL_HANDLE->rwlock: READ_UNLOCK. (rwlock.cpp:423)

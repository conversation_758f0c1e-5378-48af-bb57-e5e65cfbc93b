[FORM INFO] 2025-06-11 00:03:34 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:03:34 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:03:34 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:03:34 - Application initialized successfully
[FORM INFO] 2025-06-11 00:03:36 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:03:36 - Showing file dialog...
[FORM INFO] 2025-06-11 00:03:38 - User selected DA file: C:\ProgramData\MWorkerTool\ModelData\SP_Flash_Tool_Windows_v1.2316.00.000\MTK_AllInOne_DA.bin
[FORM INFO] 2025-06-11 00:03:38 - Validating DA file...
[FORM INFO] 2025-06-11 00:03:38 - DA file validation passed
[FORM INFO] 2025-06-11 00:03:38 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:04:07 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:04:07 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:04:07 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:04:07 - Application initialized successfully
[FORM INFO] 2025-06-11 00:04:09 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:04:09 - Showing file dialog...
[FORM INFO] 2025-06-11 00:04:24 - User selected DA file: C:\ProgramData\MWorkerTool\ModelData\Smart_Phone_Flash_MISC_Develop_Tool_P410\DA_PL_MT6765_2128_encrypt.bin
[FORM INFO] 2025-06-11 00:04:24 - Validating DA file...
[FORM INFO] 2025-06-11 00:04:24 - DA file validation passed
[FORM INFO] 2025-06-11 00:04:24 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:04:42 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:04:42 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:04:42 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:04:42 - Application initialized successfully
[FORM INFO] 2025-06-11 00:04:44 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:04:45 - Showing file dialog...
[FORM INFO] 2025-06-11 00:04:59 - User selected DA file: C:\Program Files (x86)\MotoKingPRO\bin\DA\universal\MTK_DA_V6.bin
[FORM INFO] 2025-06-11 00:04:59 - Validating DA file...
[FORM INFO] 2025-06-11 00:04:59 - DA file validation passed
[FORM INFO] 2025-06-11 00:04:59 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:04:59 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:08:33 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:08:33 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:08:33 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:08:33 - Application initialized successfully
[FORM INFO] 2025-06-11 00:08:34 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:08:37 - Showing file dialog...
[FORM INFO] 2025-06-11 00:08:39 - User selected DA file: C:\Program Files (x86)\MotoKingPRO\bin\DA\universal\MTK_DA_V6.bin
[FORM INFO] 2025-06-11 00:08:39 - Validating DA file...
[FORM INFO] 2025-06-11 00:08:39 - DA file validation passed
[FORM INFO] 2025-06-11 00:08:39 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:08:39 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:09:03 - Standard DA load result: True
[FORM INFO] 2025-06-11 00:09:05 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 00:09:09 - Download Agent loaded successfully: MTK_DA_V6.bin
[FORM INFO] 2025-06-11 00:09:26 - Starting read partitions operation...
[FORM INFO] 2025-06-11 00:09:26 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:09:26 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:09:26 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:09:26 - Application initialized successfully
[FORM INFO] 2025-06-11 00:09:26 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 00:09:31 - Failed to connect to device for partition reading. Error code: -1073676287
[FORM INFO] 2025-06-11 00:09:57 - Application closing - resources cleaned up
[FORM INFO] 2025-06-11 00:14:01 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:14:01 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:14:01 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:14:01 - Application initialized successfully
[FORM INFO] 2025-06-11 00:14:02 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:14:02 - Showing file dialog...
[FORM INFO] 2025-06-11 00:14:04 - User selected DA file: C:\Program Files (x86)\MotoKingPRO\bin\DA\universal\MTK_DA_V5.bin
[FORM INFO] 2025-06-11 00:14:04 - Validating DA file...
[FORM INFO] 2025-06-11 00:14:04 - DA file validation passed
[FORM INFO] 2025-06-11 00:14:04 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:14:04 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:14:11 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:14:11 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:14:11 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:14:11 - Application initialized successfully
[FORM INFO] 2025-06-11 00:14:12 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:14:12 - Showing file dialog...
[FORM INFO] 2025-06-11 00:14:14 - User selected DA file: C:\Program Files (x86)\MotoKingPRO\bin\DA\universal\MTK_DA_V6.bin
[FORM INFO] 2025-06-11 00:14:14 - Validating DA file...
[FORM INFO] 2025-06-11 00:14:14 - DA file validation passed
[FORM INFO] 2025-06-11 00:14:14 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:14:14 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:14:19 - Standard DA load result: True
[FORM INFO] 2025-06-11 00:14:19 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 00:14:21 - Download Agent loaded successfully: MTK_DA_V6.bin
[FORM INFO] 2025-06-11 00:14:26 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:14:26 - Showing file dialog...
[FORM INFO] 2025-06-11 00:15:15 - User selected DA file: C:\ProgramData\MWorkerTool\ModelData\Smart_Phone_Flash_MISC_Develop_Tool_P410\DA_PL_MT6765_2128_encrypt.bin
[FORM INFO] 2025-06-11 00:15:15 - Validating DA file...
[FORM INFO] 2025-06-11 00:15:15 - DA file validation passed
[FORM INFO] 2025-06-11 00:15:15 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:15:15 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:15:18 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:15:18 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:15:18 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:15:18 - Application initialized successfully
[FORM INFO] 2025-06-11 00:15:19 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:15:19 - Showing file dialog...
[FORM INFO] 2025-06-11 00:15:23 - User selected DA file: C:\ProgramData\MWorkerTool\ModelData\SP_Flash_Tool_Windows_v1.2316.00.000\MTK_AllInOne_DA.bin
[FORM INFO] 2025-06-11 00:15:23 - Validating DA file...
[FORM INFO] 2025-06-11 00:15:23 - DA file validation passed
[FORM INFO] 2025-06-11 00:15:23 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:15:23 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:17:15 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:17:15 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:17:15 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:17:15 - Application initialized successfully
[FORM INFO] 2025-06-11 00:17:16 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:17:16 - Showing file dialog...
[FORM INFO] 2025-06-11 00:17:22 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 00:17:22 - Validating DA file...
[FORM INFO] 2025-06-11 00:17:22 - DA file validation passed
[FORM INFO] 2025-06-11 00:17:22 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:17:22 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:17:22 - Standard DA load result: True
[FORM INFO] 2025-06-11 00:17:22 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 00:17:23 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 00:17:26 - Loading scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 00:17:26 - Found 53 ROM partitions
[FORM INFO] 2025-06-11 00:17:27 - Scatter file loaded successfully. 11 partitions enabled.
[FORM INFO] 2025-06-11 00:17:29 - Scatter file loaded: 11 partitions found
[FORM ERROR] 2025-06-11 00:17:47 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 00:17:47 - Stack trace:    en System.Windows.Forms.DataGridViewTextBoxCell.PaintPrivate(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewTextBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 00:17:47 - Is terminating: True
[FORM INFO] 2025-06-11 00:17:50 - Global exception handlers installed
[FORM INFO] 2025-06-11 00:17:50 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 00:17:50 - MTK Download handler initialized
[FORM INFO] 2025-06-11 00:17:50 - Application initialized successfully
[FORM INFO] 2025-06-11 00:17:51 - Starting DA selection process...
[FORM INFO] 2025-06-11 00:17:51 - Showing file dialog...
[FORM INFO] 2025-06-11 00:17:54 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 00:17:54 - Validating DA file...
[FORM INFO] 2025-06-11 00:17:54 - DA file validation passed
[FORM INFO] 2025-06-11 00:17:54 - Starting DA load operation...
[FORM INFO] 2025-06-11 00:17:54 - Attempting standard DA load...
[FORM INFO] 2025-06-11 00:17:54 - Standard DA load result: True
[FORM INFO] 2025-06-11 00:17:54 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 00:17:55 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 00:17:57 - Loading scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 00:17:57 - Found 53 ROM partitions
[FORM INFO] 2025-06-11 00:17:57 - Scatter file loaded successfully. 16 partitions enabled.
[FORM INFO] 2025-06-11 00:17:58 - Scatter file loaded: 16 partitions found
[FORM INFO] 2025-06-11 08:46:04 - Global exception handlers installed
[FORM INFO] 2025-06-11 08:46:04 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 08:46:04 - MTK Download handler initialized
[FORM INFO] 2025-06-11 08:46:04 - Application initialized successfully
[FORM INFO] 2025-06-11 08:46:06 - Starting DA selection process...
[FORM INFO] 2025-06-11 08:46:06 - Showing file dialog...
[FORM INFO] 2025-06-11 08:46:08 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 08:46:08 - Validating DA file...
[FORM INFO] 2025-06-11 08:46:08 - DA file validation passed
[FORM INFO] 2025-06-11 08:46:08 - Starting DA load operation...
[FORM INFO] 2025-06-11 08:46:08 - Attempting standard DA load...
[FORM INFO] 2025-06-11 08:46:08 - Standard DA load result: True
[FORM INFO] 2025-06-11 08:46:08 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 08:46:09 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 08:46:11 - Loading scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 08:46:11 - Found 53 ROM partitions
[FORM INFO] 2025-06-11 08:46:11 - Scatter file loaded successfully. 16 partitions enabled.
[FORM INFO] 2025-06-11 08:46:12 - Scatter file loaded: 16 partitions found
[FORM INFO] 2025-06-11 09:00:11 - Starting download/flash operation...
[FORM INFO] 2025-06-11 09:00:11 - Working directory: C:\Users\<USER>\Downloads\Firmware_G22
[FORM INFO] 2025-06-11 09:00:11 - Scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 09:00:11 - DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 09:00:11 - Processing FFU files...
[FORM INFO] 2025-06-11 09:00:11 - Configuring MTK device...
[FORM INFO] 2025-06-11 09:00:11 - Flash mode: Download Only
[FORM INFO] 2025-06-11 09:00:11 - Searching for MTK device (timeout: 60s)...
[FORM INFO] 2025-06-11 09:00:17 - Device found on COM port: 5
[FORM INFO] 2025-06-11 09:00:17 - Starting flash operation...
[FORM INFO] 2025-06-11 09:00:17 - Flash thread started: com=5
[FORM INFO] 2025-06-11 09:00:17 - Global exception handlers installed
[FORM INFO] 2025-06-11 09:00:17 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 09:00:17 - MTK Download handler initialized
[FORM INFO] 2025-06-11 09:00:17 - Application initialized successfully
[FORM INFO] 2025-06-11 09:00:33 - Global exception handlers installed
[FORM INFO] 2025-06-11 09:00:33 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 09:00:33 - MTK Download handler initialized
[FORM INFO] 2025-06-11 09:00:33 - Application initialized successfully
[FORM INFO] 2025-06-11 09:00:36 - Loading scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 09:00:36 - Found 53 ROM partitions
[FORM INFO] 2025-06-11 09:00:36 - Scatter file loaded successfully. 16 partitions enabled.
[FORM INFO] 2025-06-11 09:00:37 - Scatter file loaded: 16 partitions found
[FORM INFO] 2025-06-11 09:00:38 - Starting DA selection process...
[FORM INFO] 2025-06-11 09:00:38 - Showing file dialog...
[FORM INFO] 2025-06-11 09:00:39 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 09:00:39 - Validating DA file...
[FORM INFO] 2025-06-11 09:00:39 - DA file validation passed
[FORM INFO] 2025-06-11 09:00:39 - Starting DA load operation...
[FORM INFO] 2025-06-11 09:00:39 - Attempting standard DA load...
[FORM INFO] 2025-06-11 09:00:39 - Standard DA load result: True
[FORM INFO] 2025-06-11 09:00:39 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 09:00:40 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 09:00:54 - Starting read partitions operation...
[FORM INFO] 2025-06-11 09:00:54 - Global exception handlers installed
[FORM INFO] 2025-06-11 09:00:54 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 09:00:54 - MTK Download handler initialized
[FORM INFO] 2025-06-11 09:00:54 - Application initialized successfully
[FORM INFO] 2025-06-11 09:00:54 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 09:00:56 - Failed to connect to device for partition reading. Error code: -1073676287
[FORM INFO] 2025-06-11 09:00:58 - Starting read partitions operation...
[FORM INFO] 2025-06-11 09:00:58 - Global exception handlers installed
[FORM INFO] 2025-06-11 09:00:58 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 09:00:58 - MTK Download handler initialized
[FORM INFO] 2025-06-11 09:00:58 - Application initialized successfully
[FORM INFO] 2025-06-11 09:00:58 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 09:01:01 - Failed to connect to device for partition reading. Error code: -1073676287
[FORM INFO] 2025-06-11 09:02:43 - Starting read partitions operation...
[FORM INFO] 2025-06-11 09:02:43 - Global exception handlers installed
[FORM INFO] 2025-06-11 09:02:43 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 09:02:43 - MTK Download handler initialized
[FORM INFO] 2025-06-11 09:02:43 - Application initialized successfully
[FORM INFO] 2025-06-11 09:02:43 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 09:02:45 - Failed to connect to device for partition reading. Error code: -1073676287
[FORM INFO] 2025-06-11 09:06:09 - Application closing - resources cleaned up
[FORM INFO] 2025-06-11 12:58:23 - Global exception handlers installed
[FORM INFO] 2025-06-11 12:58:23 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 12:58:23 - MTK Download handler initialized
[FORM INFO] 2025-06-11 12:58:23 - Application initialized successfully
[FORM INFO] 2025-06-11 12:58:24 - Starting DA selection process...
[FORM INFO] 2025-06-11 12:58:24 - Showing file dialog...
[FORM INFO] 2025-06-11 12:58:25 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 12:58:25 - Validating DA file...
[FORM INFO] 2025-06-11 12:58:25 - DA file validation passed
[FORM INFO] 2025-06-11 12:58:25 - Starting DA load operation...
[FORM INFO] 2025-06-11 12:58:25 - Attempting standard DA load...
[FORM INFO] 2025-06-11 12:58:25 - Standard DA load result: True
[FORM INFO] 2025-06-11 12:58:25 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 12:58:26 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 12:58:28 - Loading scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 12:58:28 - Found 53 ROM partitions
[FORM INFO] 2025-06-11 12:58:28 - Scatter file loaded successfully. 16 partitions enabled.
[FORM INFO] 2025-06-11 12:58:29 - Scatter file loaded: 16 partitions found
[FORM INFO] 2025-06-11 12:58:32 - Starting read partitions operation...
[FORM INFO] 2025-06-11 12:58:32 - Global exception handlers installed
[FORM INFO] 2025-06-11 12:58:32 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 12:58:32 - MTK Download handler initialized
[FORM INFO] 2025-06-11 12:58:32 - Application initialized successfully
[FORM INFO] 2025-06-11 12:58:32 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 12:58:37 - Failed to connect to device for partition reading. Error code: -1073676287
[FORM INFO] 2025-06-11 12:58:51 - Starting read partitions operation...
[FORM INFO] 2025-06-11 13:13:58 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:13:58 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:13:58 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:13:58 - Application initialized successfully
[FORM INFO] 2025-06-11 13:13:59 - Starting DA selection process...
[FORM INFO] 2025-06-11 13:13:59 - Showing file dialog...
[FORM INFO] 2025-06-11 13:14:01 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 13:14:01 - Validating DA file...
[FORM INFO] 2025-06-11 13:14:01 - DA file validation passed
[FORM INFO] 2025-06-11 13:14:01 - Starting DA load operation...
[FORM INFO] 2025-06-11 13:14:01 - Attempting standard DA load...
[FORM INFO] 2025-06-11 13:14:01 - Standard DA load result: True
[FORM INFO] 2025-06-11 13:14:01 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 13:14:02 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 13:14:04 - Loading scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 13:14:04 - Found 53 ROM partitions
[FORM INFO] 2025-06-11 13:14:05 - Scatter file loaded successfully. 16 partitions enabled.
[FORM INFO] 2025-06-11 13:14:05 - Scatter file loaded: 16 partitions found
[FORM INFO] 2025-06-11 13:14:10 - Starting read partitions operation...
[FORM INFO] 2025-06-11 13:14:10 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:14:10 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:14:10 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:14:10 - Application initialized successfully
[FORM INFO] 2025-06-11 13:14:11 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 13:14:31 - Failed to connect to device for partition reading. Error code: -1073676287
[FORM INFO] 2025-06-11 13:14:42 - Starting read partitions operation...
[FORM INFO] 2025-06-11 13:14:42 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:14:42 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:14:42 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:14:42 - Application initialized successfully
[FORM INFO] 2025-06-11 13:14:43 - Connecting to device for partition reading...
[FORM INFO] 2025-06-11 13:16:08 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:16:08 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:16:08 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:16:08 - Application initialized successfully
[FORM INFO] 2025-06-11 13:16:09 - Starting DA selection process...
[FORM INFO] 2025-06-11 13:16:09 - Showing file dialog...
[FORM INFO] 2025-06-11 13:16:11 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 13:16:11 - Validating DA file...
[FORM INFO] 2025-06-11 13:16:11 - DA file validation passed
[FORM INFO] 2025-06-11 13:16:11 - Starting DA load operation...
[FORM INFO] 2025-06-11 13:16:11 - Attempting standard DA load...
[FORM INFO] 2025-06-11 13:16:11 - Standard DA load result: True
[FORM INFO] 2025-06-11 13:16:11 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 13:16:12 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 13:16:14 - Loading scatter file: C:\Users\<USER>\Downloads\Firmware_G22\MT6765_Android_scatter.txt
[FORM INFO] 2025-06-11 13:16:14 - Found 53 ROM partitions
[FORM INFO] 2025-06-11 13:16:14 - Scatter file loaded successfully. 16 partitions enabled.
[FORM INFO] 2025-06-11 13:16:15 - Scatter file loaded: 16 partitions found
[FORM INFO] 2025-06-11 13:16:19 - Starting read partitions operation...
[FORM INFO] 2025-06-11 13:16:20 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:16:20 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:16:20 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:16:20 - Application initialized successfully
[FORM INFO] 2025-06-11 13:16:20 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 13:16:42 - Failed to connect to device for partition reading. Error code: -1073545201
[FORM INFO] 2025-06-11 13:18:02 - Starting ReadGPT operation
[FORM INFO] 2025-06-11 13:18:02 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:18:02 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:18:02 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:18:02 - Application initialized successfully
[FORM ERROR] 2025-06-11 13:20:51 - Failed to connect to device. Error code: -1073348607
[FORM INFO] 2025-06-11 13:21:32 - Starting ReadPreloader operation
[FORM INFO] 2025-06-11 13:21:32 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:21:32 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:21:32 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:21:32 - Application initialized successfully
[FORM ERROR] 2025-06-11 13:21:41 - Failed to connect to device. Error code: -1073545201
[FORM INFO] 2025-06-11 13:23:34 - Starting ReadPreloader operation
[FORM INFO] 2025-06-11 13:23:34 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:23:34 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:23:34 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:23:34 - Application initialized successfully
[FORM ERROR] 2025-06-11 13:23:36 - Failed to connect to device. Error code: -1073348607
[FORM INFO] 2025-06-11 13:23:49 - Starting ReadPreloader operation
[FORM INFO] 2025-06-11 13:23:49 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:23:49 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:23:49 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:23:49 - Application initialized successfully
[FORM ERROR] 2025-06-11 13:23:55 - Failed to connect to device. Error code: -1073348607
[FORM INFO] 2025-06-11 13:24:27 - Starting read partitions operation...
[FORM INFO] 2025-06-11 13:24:27 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:24:27 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:24:27 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:24:27 - Application initialized successfully
[FORM INFO] 2025-06-11 13:24:27 - Connecting to device for partition reading...
[FORM ERROR] 2025-06-11 13:24:30 - Failed to connect to device for partition reading. Error code: -1073348607
[FORM INFO] 2025-06-11 13:24:56 - Starting ReadPreloader operation
[FORM INFO] 2025-06-11 13:24:56 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:24:56 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:24:56 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:24:56 - Application initialized successfully
[FORM ERROR] 2025-06-11 13:24:58 - Failed to connect to device. Error code: -1073545201
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM ERROR] 2025-06-11 13:25:05 - UNHANDLED DOMAIN EXCEPTION: Referencia a objeto no establecida como instancia de un objeto.
[FORM ERROR] 2025-06-11 13:25:05 - Stack trace:    en System.Windows.Forms.DataGridViewCheckBoxCell.PaintPrivate(Graphics g, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts, Boolean computeContentBounds, Boolean computeErrorIconBounds, Boolean paint)
   en System.Windows.Forms.DataGridViewCheckBoxCell.Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates elementState, Object value, Object formattedValue, String errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewCell.PaintWork(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, Int32 rowIndex, DataGridViewElementStates cellState, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.PaintCells(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow, DataGridViewPaintParts paintParts)
   en System.Windows.Forms.DataGridViewRow.Paint(Graphics graphics, Rectangle clipBounds, Rectangle rowBounds, Int32 rowIndex, DataGridViewElementStates rowState, Boolean isFirstDisplayedRow, Boolean isLastVisibleRow)
   en System.Windows.Forms.DataGridView.PaintRows(Graphics g, Rectangle boundingRect, Rectangle clipRect, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.PaintGrid(Graphics g, Rectangle gridBounds, Rectangle clipRect, Boolean singleVerticalBorderAdded, Boolean singleHorizontalBorderAdded)
   en System.Windows.Forms.DataGridView.OnPaint(PaintEventArgs e)
   en System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   en System.Windows.Forms.Control.WmPaint(Message& m)
   en System.Windows.Forms.Control.WndProc(Message& m)
   en System.Windows.Forms.DataGridView.WndProc(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   en System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   en System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
[FORM ERROR] 2025-06-11 13:25:05 - Is terminating: True
[FORM INFO] 2025-06-11 13:25:09 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:25:09 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:25:09 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:25:09 - Application initialized successfully
[FORM INFO] 2025-06-11 13:25:11 - Starting DA selection process...
[FORM INFO] 2025-06-11 13:25:11 - Showing file dialog...
[FORM INFO] 2025-06-11 13:25:14 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 13:25:14 - Validating DA file...
[FORM INFO] 2025-06-11 13:25:14 - DA file validation passed
[FORM INFO] 2025-06-11 13:25:14 - Starting DA load operation...
[FORM INFO] 2025-06-11 13:25:14 - Attempting standard DA load...
[FORM INFO] 2025-06-11 13:25:14 - Standard DA load result: True
[FORM INFO] 2025-06-11 13:25:14 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 13:25:15 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 13:25:32 - Starting ReadPreloader operation
[FORM INFO] 2025-06-11 13:25:32 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:25:32 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 13:25:32 - MTK Download handler initialized
[FORM INFO] 2025-06-11 13:25:32 - Application initialized successfully
[FORM ERROR] 2025-06-11 13:25:34 - Failed to connect to device. Error code: -1073545201
[FORM INFO] 2025-06-11 13:39:26 - Global exception handlers installed
[FORM INFO] 2025-06-11 13:39:26 - Initializing MTK handlers...
[FORM ERROR] 2025-06-11 13:39:34 - Failed to initialize MTK Download handler: No se puede cargar el archivo DLL 'FlashToolLib.dll': No se puede encontrar el módulo especificado. (Excepción de HRESULT: 0x8007007E)
[FORM INFO] 2025-06-11 14:56:37 - Global exception handlers installed
[FORM INFO] 2025-06-11 14:56:37 - Initializing MTK handlers...
[FORM ERROR] 2025-06-11 16:51:59 - Failed to initialize MTK Download handler: No se puede cargar el archivo DLL 'FlashToolLib.dll': No se puede encontrar el módulo especificado. (Excepción de HRESULT: 0x8007007E)
[FORM INFO] 2025-06-11 16:53:25 - Global exception handlers installed
[FORM INFO] 2025-06-11 16:53:25 - FlashToolLib.dll is up to date
[FORM INFO] 2025-06-11 16:53:25 - FlashToolLib.v1.dll is up to date
[FORM INFO] 2025-06-11 16:53:25 - FlashtoollibEx.dll is up to date
[FORM INFO] 2025-06-11 16:53:25 - SLA_Challenge.dll is up to date
[FORM INFO] 2025-06-11 16:53:25 - All required DLLs copied successfully
[FORM INFO] 2025-06-11 16:53:25 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 16:53:37 - MTK Download handler initialized
[FORM INFO] 2025-06-11 16:53:37 - Application initialized successfully
[FORM INFO] 2025-06-11 16:53:40 - Starting DA selection process...
[FORM INFO] 2025-06-11 16:53:40 - Showing file dialog...
[FORM INFO] 2025-06-11 16:53:43 - User selected DA file: C:\Users\<USER>\Downloads\Firmware_G22\DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 16:53:43 - Validating DA file...
[FORM INFO] 2025-06-11 16:53:43 - DA file validation passed
[FORM INFO] 2025-06-11 16:53:43 - Starting DA load operation...
[FORM INFO] 2025-06-11 16:53:43 - Attempting standard DA load...
[FORM INFO] 2025-06-11 16:53:43 - Standard DA load result: True
[FORM INFO] 2025-06-11 16:53:43 - DA loaded successfully, updating UI...
[FORM INFO] 2025-06-11 16:53:45 - Download Agent loaded successfully: DA_SWSEC_2128_STAS32.79-77-28-63-9.bin
[FORM INFO] 2025-06-11 16:53:53 - Starting ReadPreloader operation
[FORM INFO] 2025-06-11 16:53:53 - Global exception handlers installed
[FORM INFO] 2025-06-11 16:53:53 - FlashToolLib.dll is up to date
[FORM INFO] 2025-06-11 16:53:53 - FlashToolLib.v1.dll is up to date
[FORM INFO] 2025-06-11 16:53:53 - FlashtoollibEx.dll is up to date
[FORM INFO] 2025-06-11 16:53:53 - SLA_Challenge.dll is up to date
[FORM INFO] 2025-06-11 16:53:53 - All required DLLs copied successfully
[FORM INFO] 2025-06-11 16:53:53 - Initializing MTK handlers...
[FORM INFO] 2025-06-11 16:53:56 - MTK Download handler initialized
[FORM INFO] 2025-06-11 16:53:56 - Application initialized successfully
[FORM ERROR] 2025-06-11 16:54:21 - Failed to connect to device. Error code: -1073545201
